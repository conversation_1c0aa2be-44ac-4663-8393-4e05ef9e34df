security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        # used to reload user from sessnio & other features (e.g. switch_user)
        app_user_provider:
            entity:
                class: App\Core\Entity\User
                property: email
        # used to reload user from session & other features (e.g. switch_user)
        # used to reload user from session & other features (e.g. switch_user)
        # used to reload user from session & other features (e.g. switch_user)
    firewalls:
        api_login:
            pattern: ^/api/login
            stateless: true
            json_login:
                check_path: /api/login_check # or api_login_check as defined in config/routes.yaml
                success_handler: lexik_jwt_authentication.handler.authentication_success
                failure_handler: lexik_jwt_authentication.handler.authentication_failure
            login_throttling:
                max_attempts: 3
                interval: '15 minutes'
        api:
            pattern: ^/api
            stateless: true
            jwt: ~
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        main:
            lazy: false
            provider: app_user_provider
            form_login:
                default_target_path: dashboard
                always_use_default_target_path: true
                login_path: app_login
                check_path: app_login
                enable_csrf: true
            logout:
                path: app_logout
            login_throttling:
                max_attempts: 3
                interval: '15 minutes'
                # where to redirect after logout
                # target: app_any_route
                # where to redirect after logout
                # target: app_any_route
#            remember_me:
#                secret: '%kernel.secret%'
#                lifetime: 604800

            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#the-firewall

            # https://symfony.com/doc/current/security/impersonating_user.html
            # switch_user: true

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
         - { path: ^/webhook, roles: PUBLIC_ACCESS }
         - { path: ^/login, roles: PUBLIC_ACCESS }
         - { path: ^/web/error, roles: PUBLIC_ACCESS }
         - { path: ^/, roles: ROLE_ADMIN }
         - { path: ^/api/login, roles: PUBLIC_ACCESS }
         - { path: ^/api, roles: IS_AUTHENTICATED_FULLY }
        # - { path: ^/profile, roles: ROLE_USER }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
