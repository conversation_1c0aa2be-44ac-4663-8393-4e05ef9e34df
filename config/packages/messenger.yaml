framework:
    messenger:
        # Uncomment this (and the failed transport below) to send failed messages to this transport for later handling.
        failure_transport: failed

        transports:
            # https://symfony.com/doc/current/messenger.html#transport-configuration
             doctrine_async_subiekt_add_comment_to_invoice:
               dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
               options:
                 queue_name: 'subiekt_add_comment_to_invoice'
             doctrine_async_check_status_prestashop:
               dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
               options:
                 queue_name: 'doctrine_async_check_status_prestashop'
             doctrine_async_single_check_status_prestashop:
               dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
               options:
                 queue_name: 'doctrine_async_single_check_status_prestashop'
             doctrine_async_migrate_stock:
               dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
               options:
                 queue_name: 'migrate_stock'
             order_status_event_mail:
               dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
               options:
                 queue_name: 'order_status_event_mail'
             doctrine_async_fetch_single_order:
               dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
               options:
                 queue_name: 'doctrine_async_fetch_single_order'
             doctrine_bs_regenerate_zk:
               dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
               options:
                 queue_name: 'doctrine_bs_regenerate_zk'
             doctrine_create_invoice:
               dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
               options:
                 queue_name: 'doctrine_create_invoice'
             doctrine_async_change_prestashop_status:
               dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
               options:
                 queue_name: 'doctrine_async_change_prestashop_status'
             doctrine_async_order_renew_message:
               dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
               options:
                 queue_name: 'doctrine_async_order_renew_message'
             failed: 'doctrine://default?queue_name=failed'

            # sync: 'sync://'

        routing:
            # Route your messages to the transports
             'App\Core\Message\FetchSingleOrderMessage': doctrine_async_fetch_single_order
             'App\Core\Scheduler\Message\OrderCheckStatusInPrestaShopMessage': doctrine_async_check_status_prestashop
             'App\Core\Scheduler\Message\SingleOrderCheckStatusInPrestaShopMessage': doctrine_async_single_check_status_prestashop
             'App\Core\Message\SubiektAddCommentToInvoiceMessage': doctrine_async_subiekt_add_comment_to_invoice
             'App\Core\Message\MigrateStockMessage': doctrine_async_migrate_stock
             'App\Core\Message\EmailOnStatusMessage': order_status_event_mail
             'App\Core\Message\CreateInvoiceMessage': doctrine_create_invoice
             'App\Core\Message\ChangePrestashopStatusMessenger': doctrine_async_change_prestashop_status
             'App\Core\Message\OrderRenewMessage': doctrine_async_order_renew_message


# when@test:
#    framework:
#        messenger:
#            transports:
#                # replace with your transport name here (e.g., my_transport: 'in-memory://')
#                # For more Messenger testing tools, see https://github.com/zenstruck/messenger-test
#                async: 'in-memory://'
