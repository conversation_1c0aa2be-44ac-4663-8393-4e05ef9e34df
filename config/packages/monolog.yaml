monolog:
    channels:
        - deprecation # Deprecations are logged in the dedicated "deprecation" channel when it exists
        - subiekt
        - prestashop
        - baselinker
        - scheduler
        - user_action

when@dev:
    monolog:
        handlers:
            error_filter:
                type: filter
                handler: error_log_handler
                min_level: error
                bubble: false
            notice_filter:
                type: filter
                handler: notice_log_handler
                accepted_levels: [ notice ]
                bubble: false
            info_filter:
                type: filter
                handler: info_log_handler
                accepted_levels: [ info ]
                bubble: false
            debug_filter:
                type: filter
                handler: debug_log_handler
                accepted_levels: [ debug ]
                bubble: false
            error_log_handler:
                type: rotating_file
                max_files: 3
                path: "%kernel.logs_dir%/error/error.log"
            notice_log_handler:
                type: rotating_file
                max_files: 3
                path: "%kernel.logs_dir%/notice/notice.log"
            info_log_handler:
                type: rotating_file
                max_files: 3
                path: "%kernel.logs_dir%/info/info.log"
            debug_log_handler:
                type: rotating_file
                max_files: 3
                path: "%kernel.logs_dir%/debug/debug.log"
            user_action:
                type: service
                level: info
                id: App\Core\Service\DatabaseLogHandler
                channels: [ "user_action" ]
            subiekt:
                type: rotating_file
                path: "%kernel.logs_dir%/message/subiekt.log"
                level: debug
                channels: [ "subiekt" ]
                max_files: 3
            prestashop:
                type: rotating_file
                path: "%kernel.logs_dir%/message/prestashop.log"
                level: debug
                channels: [ "prestashop" ]
                max_files: 3
            baselinker:
                type: rotating_file
                path: "%kernel.logs_dir%/message/baselinker.log"
                level: debug
                channels: [ "baselinker" ]
                max_files: 3
            scheduler:
                type: rotating_file
                path: "%kernel.logs_dir%/message/scheduler.log"
                level: debug
                channels: [ "scheduler" , "subiekt", "prestashop", "baselinker" ]
            console:
                type: console
                process_psr_3_messages: false
                channels: ["!event", "!doctrine", "!console"]

when@test:
    monolog:
        handlers:
            main:
                type: fingers_crossed
                action_level: error
                handler: nested
                excluded_http_codes: [404, 405]
                channels: ["!event"]
            nested:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: debug

when@prod:
    monolog:
        handlers:
            error_filter:
                type: filter
                handler: error_log_handler
                min_level: error
                bubble: false
            notice_filter:
                type: filter
                handler: notice_log_handler
                accepted_levels: [notice]
                bubble: false
            info_filter:
                type: filter
                handler: info_log_handler
                accepted_levels: [info]
                bubble: false
            debug_filter:
                type: filter
                handler: debug_log_handler
                accepted_levels: [debug]
                bubble: false
            error_log_handler:
                type: rotating_file
                max_files: 3
                path: "%kernel.logs_dir%/error/error.log"
            notice_log_handler:
                type: rotating_file
                max_files: 3
                path: "%kernel.logs_dir%/notice/notice.log"
            info_log_handler:
                type: rotating_file
                max_files: 3
                path: "%kernel.logs_dir%/info/info.log"
            debug_log_handler:
                type: rotating_file
                max_files: 3
                path: "%kernel.logs_dir%/debug/debug.log"
            user_action:
                type: service
                level: info
                id: App\Core\Service\DatabaseLogHandler
                channels: [ "user_action" ]
            subiekt:
                type: rotating_file
                path: "%kernel.logs_dir%/message/subiekt.log"
                level: debug
                channels: [ "subiekt" ]
                max_files: 3
            prestashop:
                type: rotating_file
                path: "%kernel.logs_dir%/message/prestashop.log"
                level: debug
                channels: [ "prestashop" ]
                max_files: 3
            baselinker:
                type: rotating_file
                path: "%kernel.logs_dir%/message/baselinker.log"
                level: debug
                channels: [ "baselinker" ]
                max_files: 3
            scheduler:
                type: rotating_file
                path: "%kernel.logs_dir%/message/scheduler.log"
                level: debug
                channels: [ "scheduler" , "subiekt", "prestashop", "baselinker"]
                max_files: 3