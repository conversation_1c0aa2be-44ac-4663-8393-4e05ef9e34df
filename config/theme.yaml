# Theme configuration - defines available regions
theme:
  name: 'base'
  regions:
    sidebar_menu:
      label: 'Sidebar Menu'
      description: 'Left sidebar navigation menu (fixed height)'
      weight: -20
    top_panel:
      label: 'Top Panel'
      description: 'Top panel area for widgets and tools (1/5 screen height)'
      weight: -10
    content_before:
      label: 'Content Before'
      description: 'Content area before main content'
      weight: 0
    page_content:
      label: 'Main Content'
      description: 'Primary content area (4/5 screen height)'
      weight: 10
    content_after:
      label: 'Content After'
      description: 'Content area after main content'
      weight: 20

  # Default layout template
  layout: 'layouts/main.html.twig'

  # Theme assets
  assets:
    css:
      - 'vendor/bootstrap/dist/css/bootstrap.min.css'
      - 'vendor/@fortawesome/fontawesome-free/css/all.min.css'
      - 'styles/app.scss'
    js:
      - 'app'
