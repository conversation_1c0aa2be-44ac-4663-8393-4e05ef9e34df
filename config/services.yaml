# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    telegram.debug: '%env(TELEGRAM_DEBUG)%'
services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            iterable $viewProvider: !tagged_iterator 'order.view_type'
            iterable $carrierProvider: !tagged_iterator 'carrier.provider'
            $actionLogger: '@monolog.logger.user_action'


    App\:
        resource: '../src/'
        exclude:
            - '../src/Core/DependencyInjection/'
            - '../src/Core/Entity/'
            - '../src/Core/Kernel.php'

    SCA\FedexApi\Fedex:
        arguments:
            $httpClient: '@http_client'

    App\Core\Serializer\Encoder\XmlEncoder:
        decorates: 'serializer.encoder.xml'

    App\Core\Template\Twig\DynamicTwigLoader:
        arguments:
            $paths: [ '%kernel.project_dir%/templates' ]
            $moduleDiscovery: '@App\Engine\Service\ModuleDiscoveryService'
        tags: [ 'twig.loader' ]

    App\Engine\Service\ThemeService:
        arguments:
            $projectDir: '%kernel.project_dir%'

    App\Engine\Service\ModuleDiscoveryService:
        arguments:
            $projectDir: '%kernel.project_dir%'

    # Module route provider for CMF Routing
    app.module_route_provider:
        class: App\Engine\Routing\ModuleRouteProvider
        arguments:
            $moduleDiscovery: '@App\Engine\Service\ModuleDiscoveryService'

    accountant.default_accountant_service:
        class: App\Core\Service\Accountant\AccountantServiceFactory
        factory: ['@App\Core\Service\Accountant\AccountantServiceFactory', 'getAccountantService' ]

    App\Core\Service\Accountant\Types\Account\AccountInterface:
        public: true
        alias: 'accountant.default_accountant_service'


    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name

    rate_limiter.storage.filesystem:
        class: Symfony\Component\RateLimiter\Storage\CacheStorage
        arguments:
            - '@cache.app'


    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
