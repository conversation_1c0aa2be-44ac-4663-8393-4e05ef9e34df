<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250805102053 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE accounant_account (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          type VARCHAR(255) NOT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE accounant_account_settings (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          account_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          value VARCHAR(255) NOT NULL,
          INDEX IDX_5695655F9B6B5FBA (account_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE basket (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          user_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          ean13 VARCHAR(255) NOT NULL,
          status_id INT NOT NULL,
          INDEX IDX_2246507BA76ED395 (user_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE basket_order (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          order_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          basket_id_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          user_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          date_created DATE NOT NULL,
          date_updated DATE NOT NULL,
          status_id INT NOT NULL,
          UNIQUE INDEX UNIQ_A351D8A28D9F6D38 (order_id),
          INDEX IDX_A351D8A2293CD56D (basket_id_id),
          INDEX IDX_A351D8A2A76ED395 (user_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE basket_order_product (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          basket_order_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          order_product_id_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          quantity_required INT NOT NULL,
          quantity_completed INT NOT NULL,
          status_id INT NOT NULL,
          quantity_packed INT DEFAULT NULL,
          location VARCHAR(50) DEFAULT NULL,
          INDEX IDX_27137617E93062E8 (basket_order_id),
          INDEX IDX_27137617737BADD9 (order_product_id_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE block_placements (
          id INT AUTO_INCREMENT NOT NULL,
          block_id VARCHAR(255) NOT NULL,
          region VARCHAR(255) NOT NULL,
          weight INT NOT NULL,
          enabled TINYINT(1) NOT NULL,
          configuration JSON NOT NULL,
          created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE carrier (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          type VARCHAR(255) NOT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE carrier_settings (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          carrier_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          value LONGTEXT NOT NULL,
          INDEX IDX_9D169A4121DFC797 (carrier_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          type_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          status_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          seller_company_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          document_number_internal VARCHAR(255) NOT NULL,
          document_number_external VARCHAR(255) DEFAULT NULL,
          created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          accounting_reference VARCHAR(255) DEFAULT NULL,
          INDEX IDX_D8698A76C54C8C93 (type_id),
          INDEX IDX_D8698A766BF700BD (status_id),
          INDEX IDX_D8698A7658C0EBBC (seller_company_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document_item (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          document_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) DEFAULT NULL,
          quantity_required INT DEFAULT NULL,
          ean13 VARCHAR(15) NOT NULL,
          reference VARCHAR(30) DEFAULT NULL,
          unit_price_netto DOUBLE PRECISION DEFAULT NULL,
          unit_price_brutto DOUBLE PRECISION DEFAULT NULL,
          total_price_netto DOUBLE PRECISION DEFAULT NULL,
          total_price_brutto DOUBLE PRECISION DEFAULT NULL,
          tax DOUBLE PRECISION DEFAULT NULL,
          expiration_date DATETIME DEFAULT NULL,
          quantity_completed INT DEFAULT NULL,
          is_in_subiekt TINYINT(1) DEFAULT 0,
          sample TINYINT(1) DEFAULT 0 NOT NULL,
          eans JSON DEFAULT NULL,
          INDEX IDX_B8AFA98DC33F7837 (document_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document_seller_company (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          internal_name VARCHAR(255) DEFAULT NULL,
          owner_name VARCHAR(255) DEFAULT NULL,
          owner_surname VARCHAR(255) DEFAULT NULL,
          company_name VARCHAR(255) NOT NULL,
          nip VARCHAR(12) DEFAULT NULL,
          street VARCHAR(255) DEFAULT NULL,
          house_nr VARCHAR(255) DEFAULT NULL,
          city VARCHAR(255) DEFAULT NULL,
          post_code VARCHAR(255) DEFAULT NULL,
          account_number VARCHAR(30) DEFAULT NULL,
          active TINYINT(1) NOT NULL,
          format VARCHAR(30) NOT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document_shelf_stocktaking (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          shelf_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          document_stocktaking_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          finished TINYINT(1) NOT NULL,
          INDEX IDX_904241AC7C12FBC0 (shelf_id),
          INDEX IDX_904241ACF3F59E45 (document_stocktaking_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document_shelf_stocktaking_data (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          ean_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          document_shelf_stocktaking_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          quantity INT DEFAULT NULL,
          old_quantity INT DEFAULT NULL,
          INDEX IDX_64F61D03F50FDA5 (ean_id),
          INDEX IDX_64F61D03C7933F5C (document_shelf_stocktaking_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document_status (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document_stocktaking (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          storehouse_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          internal_number VARCHAR(255) DEFAULT NULL,
          external_number VARCHAR(255) DEFAULT NULL,
          finished TINYINT(1) NOT NULL,
          INDEX IDX_922B45F16F7858F (storehouse_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document_type (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          prefix VARCHAR(255) NOT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ean (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          product_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          ean VARCHAR(255) NOT NULL,
          INDEX IDX_67B1C6604584665A (product_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ean_shelf_quantity (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          ean_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          shelf_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          quantity INT DEFAULT NULL,
          virtual_quantity INT DEFAULT NULL,
          INDEX IDX_F0B915B1F50FDA5 (ean_id),
          INDEX IDX_F0B915B17C12FBC0 (shelf_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE email_account (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          smtp_address VARCHAR(255) NOT NULL,
          smtp_login VARCHAR(255) NOT NULL,
          smtp_password VARCHAR(255) NOT NULL,
          email_address VARCHAR(255) NOT NULL,
          sender_name VARCHAR(255) NOT NULL,
          smtp_security VARCHAR(255) NOT NULL,
          full_name VARCHAR(255) NOT NULL,
          smtp_port INT NOT NULL,
          domain VARCHAR(255) DEFAULT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE email_template (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          email_account_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(30) NOT NULL,
          subject VARCHAR(255) NOT NULL,
          content LONGTEXT NOT NULL,
          lang VARCHAR(10) NOT NULL,
          INDEX IDX_9C0600CA37D8AD65 (email_account_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE integration (
          id INT AUTO_INCREMENT NOT NULL,
          owner_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          type VARCHAR(255) NOT NULL,
          INDEX IDX_FDE96D9B7E3C61F9 (owner_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE integration_data (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          integration_id INT NOT NULL,
          fetch_status_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          cancel_status_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          order_fetch_interval VARCHAR(255) NOT NULL,
          fetch_products VARCHAR(255) NOT NULL,
          order_status_send_paid VARCHAR(255) NOT NULL,
          order_status_synchronize VARCHAR(255) NOT NULL,
          api_url VARCHAR(255) NOT NULL,
          api_token VARCHAR(255) DEFAULT NULL,
          api_secret VARCHAR(255) DEFAULT NULL,
          UNIQUE INDEX UNIQ_986DCE789E82DDEA (integration_id),
          INDEX IDX_986DCE78B51E65F3 (fetch_status_id),
          INDEX IDX_986DCE78DC565623 (cancel_status_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE integration_settings (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          integration_id INT NOT NULL,
          name VARCHAR(255) NOT NULL,
          value LONGTEXT DEFAULT NULL,
          INDEX IDX_F79F652F9E82DDEA (integration_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE integration_status_mapping (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          integration_data_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          order_status_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          integration_status_id VARCHAR(50) NOT NULL,
          integration_status_name VARCHAR(255) NOT NULL,
          paid TINYINT(1) NOT NULL,
          shipped TINYINT(1) NOT NULL,
          delivered TINYINT(1) NOT NULL,
          INDEX IDX_81572AA35AD91B65 (integration_data_id),
          INDEX IDX_81572AA3D7707B45 (order_status_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE `log` (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          user_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          message LONGTEXT DEFAULT NULL,
          context JSON DEFAULT NULL,
          level SMALLINT DEFAULT NULL,
          level_name VARCHAR(50) DEFAULT NULL,
          extra JSON DEFAULT NULL,
          created_at DATETIME NOT NULL,
          entity_type VARCHAR(255) DEFAULT NULL,
          entity_id VARCHAR(255) DEFAULT NULL,
          action VARCHAR(255) DEFAULT NULL,
          INDEX IDX_8F3F68C5A76ED395 (user_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE module_states (
          id INT AUTO_INCREMENT NOT NULL,
          module_id VARCHAR(255) NOT NULL,
          installed TINYINT(1) NOT NULL,
          enabled TINYINT(1) NOT NULL,
          configuration JSON NOT NULL,
          installed_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          UNIQUE INDEX UNIQ_9BC13762AFC2B591 (module_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE `order` (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          internal_status_id_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          mark_flag_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          order_id VARCHAR(50) NOT NULL,
          shop_order_id INT NOT NULL,
          external_order_id VARCHAR(50) NOT NULL,
          order_source VARCHAR(20) NOT NULL,
          order_source_id INT NOT NULL,
          order_source_info VARCHAR(200) NOT NULL,
          order_status_id INT NOT NULL,
          date_add DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          date_confirmed DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          date_in_status DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          confirmed TINYINT(1) NOT NULL,
          user_login VARCHAR(100) NOT NULL,
          currency VARCHAR(3) NOT NULL,
          payment_method VARCHAR(100) NOT NULL,
          payment_method_cod VARCHAR(1) NOT NULL,
          payment_done INT NOT NULL,
          user_comments LONGTEXT NOT NULL,
          admin_comments LONGTEXT NOT NULL,
          email VARCHAR(150) NOT NULL,
          phone VARCHAR(100) NOT NULL,
          extra_field_1 LONGTEXT DEFAULT NULL,
          extra_field_2 LONGTEXT DEFAULT NULL,
          custom_extra_fields JSON DEFAULT NULL,
          order_page VARCHAR(150) NOT NULL,
          pick_state INT NOT NULL,
          pack_state INT NOT NULL,
          status_date DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          discount_value INT DEFAULT NULL,
          discount_name VARCHAR(255) DEFAULT NULL,
          full_price INT DEFAULT NULL,
          paid TINYINT(1) NOT NULL,
          cancelled TINYINT(1) NOT NULL,
          UNIQUE INDEX UNIQ_F52993988D9F6D38 (order_id),
          INDEX IDX_F529939815F9D748 (internal_status_id_id),
          INDEX IDX_F5299398F00D725C (mark_flag_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE order_delivery (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          order_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          delivery_method VARCHAR(100) NOT NULL,
          delivery_price INT NOT NULL,
          delivery_package_module VARCHAR(20) NOT NULL,
          delivery_package_nr VARCHAR(40) NOT NULL,
          delivery_fullname VARCHAR(100) NOT NULL,
          delivery_company VARCHAR(100) NOT NULL,
          delivery_address VARCHAR(100) NOT NULL,
          delivery_postcode VARCHAR(100) NOT NULL,
          delivery_city VARCHAR(100) NOT NULL,
          delivery_state VARCHAR(20) NOT NULL,
          delivery_country VARCHAR(50) NOT NULL,
          delivery_country_code VARCHAR(2) NOT NULL,
          delivery_point_id VARCHAR(40) NOT NULL,
          delivery_point_name VARCHAR(100) NOT NULL,
          delivery_point_address VARCHAR(100) NOT NULL,
          delivery_point_postcode VARCHAR(100) NOT NULL,
          delivery_point_city VARCHAR(100) NOT NULL,
          delivery_id VARCHAR(255) DEFAULT NULL,
          manual_cod_price DOUBLE PRECISION DEFAULT NULL,
          UNIQUE INDEX UNIQ_D6790EA18D9F6D38 (order_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE order_delivery_shipment_data (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          order_delivery_id_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          carrier_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          carrier_type VARCHAR(255) NOT NULL,
          label TINYINT(1) DEFAULT NULL,
          invoice TINYINT(1) DEFAULT NULL,
          invoice_mail TINYINT(1) DEFAULT NULL,
          shipment_mail TINYINT(1) DEFAULT NULL,
          shipment_id VARCHAR(200) DEFAULT NULL,
          tracking_id VARCHAR(200) DEFAULT NULL,
          shipment_status VARCHAR(255) DEFAULT NULL,
          shipment_status_date VARCHAR(255) DEFAULT NULL,
          label_date_created VARCHAR(255) DEFAULT NULL,
          shipment_date VARCHAR(255) DEFAULT NULL,
          INDEX IDX_6B8A067273FA875 (order_delivery_id_id),
          INDEX IDX_6B8A067221DFC797 (carrier_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE order_email_on_status (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          status_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          email_template_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          email_from_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          INDEX IDX_7BEFE9C46BF700BD (status_id),
          INDEX IDX_7BEFE9C4131A730F (email_template_id),
          INDEX IDX_7BEFE9C4C50EB1C4 (email_from_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE order_flag (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          flag_name VARCHAR(20) NOT NULL,
          flag_color VARCHAR(10) NOT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE order_history_change_review (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          parent_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          user_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          date_of_change DATETIME NOT NULL,
          data LONGTEXT NOT NULL COMMENT \'(DC2Type:array)\',
          INDEX IDX_6E22DB9D727ACA70 (parent_id),
          INDEX IDX_6E22DB9DA76ED395 (user_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE order_invoice (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          order_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          invoice_fullname VARCHAR(200) NOT NULL,
          invoice_company VARCHAR(200) NOT NULL,
          invoice_nip VARCHAR(100) NOT NULL,
          invoice_address VARCHAR(250) NOT NULL,
          invoice_postcode VARCHAR(20) NOT NULL,
          invoice_city VARCHAR(100) NOT NULL,
          invoice_state VARCHAR(20) NOT NULL,
          invoice_country VARCHAR(50) NOT NULL,
          invoice_country_code VARCHAR(2) NOT NULL,
          want_invoice VARCHAR(1) NOT NULL,
          invoice_number VARCHAR(50) DEFAULT NULL,
          additional_number VARCHAR(255) DEFAULT NULL,
          mm_number VARCHAR(255) DEFAULT NULL,
          UNIQUE INDEX UNIQ_661FBE0F8D9F6D38 (order_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE order_product (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          order_id_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          storage VARCHAR(9) NOT NULL,
          storage_id INT NOT NULL,
          order_product_id INT NOT NULL,
          product_id VARCHAR(50) NOT NULL,
          variant_id VARCHAR(30) NOT NULL,
          name VARCHAR(255) NOT NULL,
          sku VARCHAR(50) NOT NULL,
          ean VARCHAR(32) NOT NULL,
          location VARCHAR(50) NOT NULL,
          warehouse_id INT NOT NULL,
          auction_id VARCHAR(50) NOT NULL,
          attributes VARCHAR(150) NOT NULL,
          price_brutto INT NOT NULL,
          tax_rate INT NOT NULL,
          quantity INT NOT NULL,
          weight DOUBLE PRECISION NOT NULL,
          bundle_id INT NOT NULL,
          cover_image_url VARCHAR(255) DEFAULT NULL,
          full_price INT DEFAULT NULL,
          width VARCHAR(20) DEFAULT NULL,
          height VARCHAR(20) DEFAULT NULL,
          depth VARCHAR(20) DEFAULT NULL,
          INDEX IDX_2530ADE6FCDAEAAA (order_id_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE order_status (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          tab_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          short_name VARCHAR(20) NOT NULL,
          full_name VARCHAR(250) NOT NULL,
          color VARCHAR(10) NOT NULL,
          INDEX IDX_B88F75C98D0C9323 (tab_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE order_status_tab (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE product (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          sku VARCHAR(255) NOT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE product_reservation (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          product_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          shelf_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          user_completed_id BINARY(16) DEFAULT NULL COMMENT \'(DC2Type:uuid)\',
          ean_shelf_quantity_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          id_order_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          quantity INT NOT NULL,
          status VARCHAR(255) NOT NULL,
          date_created DATETIME NOT NULL,
          date_completed DATETIME DEFAULT NULL,
          INDEX IDX_EEE7D74A4584665A (product_id),
          INDEX IDX_EEE7D74A7C12FBC0 (shelf_id),
          INDEX IDX_EEE7D74A1AC3754 (user_completed_id),
          INDEX IDX_EEE7D74A27D5BCDB (ean_shelf_quantity_id),
          INDEX IDX_EEE7D74ADD4481AD (id_order_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE quantity_log (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          id_order_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          id_product_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          id_esq_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          id_storehouse_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          quantity_required INT NOT NULL,
          quantity_available INT NOT NULL,
          date_checked DATETIME NOT NULL,
          INDEX IDX_509E352BDD4481AD (id_order_id),
          INDEX IDX_509E352BE00EE68D (id_product_id),
          INDEX IDX_509E352B3B3DCB63 (id_esq_id),
          INDEX IDX_509E352BB8DF54EA (id_storehouse_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE rack (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          storehouse_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          rack_no VARCHAR(255) NOT NULL,
          ean13 VARCHAR(13) NOT NULL,
          INDEX IDX_3DD796A86F7858F (storehouse_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE rule (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          event VARCHAR(255) NOT NULL,
          conditions JSON NOT NULL,
          actions JSON NOT NULL,
          parameters JSON DEFAULT NULL,
          weight INT NOT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE settings (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          name VARCHAR(255) NOT NULL,
          value LONGTEXT DEFAULT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE shelf (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          rack_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          shelf_no VARCHAR(255) NOT NULL,
          type VARCHAR(255) NOT NULL,
          ean13 VARCHAR(13) NOT NULL,
          INDEX IDX_A5475BE38E86A33E (rack_id),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE storehouse (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          type VARCHAR(255) NOT NULL,
          internal_name VARCHAR(255) NOT NULL,
          id_external VARCHAR(255) DEFAULT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_login_history (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          date_login DATETIME NOT NULL,
          ip_address VARCHAR(50) NOT NULL,
          firewall VARCHAR(50) NOT NULL,
          result VARCHAR(50) NOT NULL,
          password VARCHAR(255) NOT NULL,
          username VARCHAR(255) NOT NULL,
          user_agent VARCHAR(255) NOT NULL,
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE users (
          id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\',
          username VARCHAR(180) NOT NULL,
          email VARCHAR(255) NOT NULL,
          password VARCHAR(255) NOT NULL,
          roles JSON NOT NULL,
          last_login DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          enabled TINYINT(1) NOT NULL,
          UNIQUE INDEX UNIQ_1483A5E9F85E0677 (username),
          UNIQUE INDEX UNIQ_1483A5E9E7927C74 (email),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE messenger_messages (
          id BIGINT AUTO_INCREMENT NOT NULL,
          body LONGTEXT NOT NULL,
          headers LONGTEXT NOT NULL,
          queue_name VARCHAR(190) NOT NULL,
          created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          available_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          delivered_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\',
          INDEX IDX_75EA56E0FB7336F0 (queue_name),
          INDEX IDX_75EA56E0E3BD61CE (available_at),
          INDEX IDX_75EA56E016BA31DB (delivered_at),
          PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE
          accounant_account_settings
        ADD
          CONSTRAINT FK_5695655F9B6B5FBA FOREIGN KEY (account_id) REFERENCES accounant_account (id)');
        $this->addSql('ALTER TABLE
          basket
        ADD
          CONSTRAINT FK_2246507BA76ED395 FOREIGN KEY (user_id) REFERENCES users (id)');
        $this->addSql('ALTER TABLE
          basket_order
        ADD
          CONSTRAINT FK_A351D8A28D9F6D38 FOREIGN KEY (order_id) REFERENCES `order` (id)');
        $this->addSql('ALTER TABLE
          basket_order
        ADD
          CONSTRAINT FK_A351D8A2293CD56D FOREIGN KEY (basket_id_id) REFERENCES basket (id)');
        $this->addSql('ALTER TABLE
          basket_order
        ADD
          CONSTRAINT FK_A351D8A2A76ED395 FOREIGN KEY (user_id) REFERENCES users (id)');
        $this->addSql('ALTER TABLE
          basket_order_product
        ADD
          CONSTRAINT FK_27137617E93062E8 FOREIGN KEY (basket_order_id) REFERENCES basket_order (id)');
        $this->addSql('ALTER TABLE
          basket_order_product
        ADD
          CONSTRAINT FK_27137617737BADD9 FOREIGN KEY (order_product_id_id) REFERENCES order_product (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE
          carrier_settings
        ADD
          CONSTRAINT FK_9D169A4121DFC797 FOREIGN KEY (carrier_id) REFERENCES carrier (id)');
        $this->addSql('ALTER TABLE
          document
        ADD
          CONSTRAINT FK_D8698A76C54C8C93 FOREIGN KEY (type_id) REFERENCES document_type (id)');
        $this->addSql('ALTER TABLE
          document
        ADD
          CONSTRAINT FK_D8698A766BF700BD FOREIGN KEY (status_id) REFERENCES document_status (id)');
        $this->addSql('ALTER TABLE
          document
        ADD
          CONSTRAINT FK_D8698A7658C0EBBC FOREIGN KEY (seller_company_id) REFERENCES document_seller_company (id)');
        $this->addSql('ALTER TABLE
          document_item
        ADD
          CONSTRAINT FK_B8AFA98DC33F7837 FOREIGN KEY (document_id) REFERENCES document (id)');
        $this->addSql('ALTER TABLE
          document_shelf_stocktaking
        ADD
          CONSTRAINT FK_904241AC7C12FBC0 FOREIGN KEY (shelf_id) REFERENCES shelf (id)');
        $this->addSql('ALTER TABLE
          document_shelf_stocktaking
        ADD
          CONSTRAINT FK_904241ACF3F59E45 FOREIGN KEY (document_stocktaking_id) REFERENCES document_stocktaking (id)');
        $this->addSql('ALTER TABLE
          document_shelf_stocktaking_data
        ADD
          CONSTRAINT FK_64F61D03F50FDA5 FOREIGN KEY (ean_id) REFERENCES ean (id)');
        $this->addSql('ALTER TABLE
          document_shelf_stocktaking_data
        ADD
          CONSTRAINT FK_64F61D03C7933F5C FOREIGN KEY (document_shelf_stocktaking_id) REFERENCES document_shelf_stocktaking (id)');
        $this->addSql('ALTER TABLE
          document_stocktaking
        ADD
          CONSTRAINT FK_922B45F16F7858F FOREIGN KEY (storehouse_id) REFERENCES storehouse (id)');
        $this->addSql('ALTER TABLE
          ean
        ADD
          CONSTRAINT FK_67B1C6604584665A FOREIGN KEY (product_id) REFERENCES product (id)');
        $this->addSql('ALTER TABLE
          ean_shelf_quantity
        ADD
          CONSTRAINT FK_F0B915B1F50FDA5 FOREIGN KEY (ean_id) REFERENCES ean (id)');
        $this->addSql('ALTER TABLE
          ean_shelf_quantity
        ADD
          CONSTRAINT FK_F0B915B17C12FBC0 FOREIGN KEY (shelf_id) REFERENCES shelf (id)');
        $this->addSql('ALTER TABLE
          email_template
        ADD
          CONSTRAINT FK_9C0600CA37D8AD65 FOREIGN KEY (email_account_id) REFERENCES email_account (id)');
        $this->addSql('ALTER TABLE
          integration
        ADD
          CONSTRAINT FK_FDE96D9B7E3C61F9 FOREIGN KEY (owner_id) REFERENCES users (id)');
        $this->addSql('ALTER TABLE
          integration_data
        ADD
          CONSTRAINT FK_986DCE789E82DDEA FOREIGN KEY (integration_id) REFERENCES integration (id)');
        $this->addSql('ALTER TABLE
          integration_data
        ADD
          CONSTRAINT FK_986DCE78B51E65F3 FOREIGN KEY (fetch_status_id) REFERENCES order_status (id)');
        $this->addSql('ALTER TABLE
          integration_data
        ADD
          CONSTRAINT FK_986DCE78DC565623 FOREIGN KEY (cancel_status_id) REFERENCES order_status (id)');
        $this->addSql('ALTER TABLE
          integration_settings
        ADD
          CONSTRAINT FK_F79F652F9E82DDEA FOREIGN KEY (integration_id) REFERENCES integration (id)');
        $this->addSql('ALTER TABLE
          integration_status_mapping
        ADD
          CONSTRAINT FK_81572AA35AD91B65 FOREIGN KEY (integration_data_id) REFERENCES integration_data (id)');
        $this->addSql('ALTER TABLE
          integration_status_mapping
        ADD
          CONSTRAINT FK_81572AA3D7707B45 FOREIGN KEY (order_status_id) REFERENCES order_status (id)');
        $this->addSql('ALTER TABLE
          `log`
        ADD
          CONSTRAINT FK_8F3F68C5A76ED395 FOREIGN KEY (user_id) REFERENCES users (id)');
        $this->addSql('ALTER TABLE
          `order`
        ADD
          CONSTRAINT FK_F529939815F9D748 FOREIGN KEY (internal_status_id_id) REFERENCES order_status (id)');
        $this->addSql('ALTER TABLE
          `order`
        ADD
          CONSTRAINT FK_F5299398F00D725C FOREIGN KEY (mark_flag_id) REFERENCES order_flag (id)');
        $this->addSql('ALTER TABLE
          order_delivery
        ADD
          CONSTRAINT FK_D6790EA18D9F6D38 FOREIGN KEY (order_id) REFERENCES `order` (id)');
        $this->addSql('ALTER TABLE
          order_delivery_shipment_data
        ADD
          CONSTRAINT FK_6B8A067273FA875 FOREIGN KEY (order_delivery_id_id) REFERENCES order_delivery (id)');
        $this->addSql('ALTER TABLE
          order_delivery_shipment_data
        ADD
          CONSTRAINT FK_6B8A067221DFC797 FOREIGN KEY (carrier_id) REFERENCES carrier (id)');
        $this->addSql('ALTER TABLE
          order_email_on_status
        ADD
          CONSTRAINT FK_7BEFE9C46BF700BD FOREIGN KEY (status_id) REFERENCES order_status (id)');
        $this->addSql('ALTER TABLE
          order_email_on_status
        ADD
          CONSTRAINT FK_7BEFE9C4131A730F FOREIGN KEY (email_template_id) REFERENCES email_template (id)');
        $this->addSql('ALTER TABLE
          order_email_on_status
        ADD
          CONSTRAINT FK_7BEFE9C4C50EB1C4 FOREIGN KEY (email_from_id) REFERENCES email_account (id)');
        $this->addSql('ALTER TABLE
          order_history_change_review
        ADD
          CONSTRAINT FK_6E22DB9D727ACA70 FOREIGN KEY (parent_id) REFERENCES `order` (id)');
        $this->addSql('ALTER TABLE
          order_history_change_review
        ADD
          CONSTRAINT FK_6E22DB9DA76ED395 FOREIGN KEY (user_id) REFERENCES users (id)');
        $this->addSql('ALTER TABLE
          order_invoice
        ADD
          CONSTRAINT FK_661FBE0F8D9F6D38 FOREIGN KEY (order_id) REFERENCES `order` (id)');
        $this->addSql('ALTER TABLE
          order_product
        ADD
          CONSTRAINT FK_2530ADE6FCDAEAAA FOREIGN KEY (order_id_id) REFERENCES `order` (id)');
        $this->addSql('ALTER TABLE
          order_status
        ADD
          CONSTRAINT FK_B88F75C98D0C9323 FOREIGN KEY (tab_id) REFERENCES order_status_tab (id)');
        $this->addSql('ALTER TABLE
          product_reservation
        ADD
          CONSTRAINT FK_EEE7D74A4584665A FOREIGN KEY (product_id) REFERENCES product (id)');
        $this->addSql('ALTER TABLE
          product_reservation
        ADD
          CONSTRAINT FK_EEE7D74A7C12FBC0 FOREIGN KEY (shelf_id) REFERENCES shelf (id)');
        $this->addSql('ALTER TABLE
          product_reservation
        ADD
          CONSTRAINT FK_EEE7D74A1AC3754 FOREIGN KEY (user_completed_id) REFERENCES users (id)');
        $this->addSql('ALTER TABLE
          product_reservation
        ADD
          CONSTRAINT FK_EEE7D74A27D5BCDB FOREIGN KEY (ean_shelf_quantity_id) REFERENCES ean_shelf_quantity (id)');
        $this->addSql('ALTER TABLE
          product_reservation
        ADD
          CONSTRAINT FK_EEE7D74ADD4481AD FOREIGN KEY (id_order_id) REFERENCES `order` (id)');
        $this->addSql('ALTER TABLE
          quantity_log
        ADD
          CONSTRAINT FK_509E352BDD4481AD FOREIGN KEY (id_order_id) REFERENCES `order` (id)');
        $this->addSql('ALTER TABLE
          quantity_log
        ADD
          CONSTRAINT FK_509E352BE00EE68D FOREIGN KEY (id_product_id) REFERENCES order_product (id)');
        $this->addSql('ALTER TABLE
          quantity_log
        ADD
          CONSTRAINT FK_509E352B3B3DCB63 FOREIGN KEY (id_esq_id) REFERENCES ean_shelf_quantity (id)');
        $this->addSql('ALTER TABLE
          quantity_log
        ADD
          CONSTRAINT FK_509E352BB8DF54EA FOREIGN KEY (id_storehouse_id) REFERENCES storehouse (id)');
        $this->addSql('ALTER TABLE
          rack
        ADD
          CONSTRAINT FK_3DD796A86F7858F FOREIGN KEY (storehouse_id) REFERENCES storehouse (id)');
        $this->addSql('ALTER TABLE
          shelf
        ADD
          CONSTRAINT FK_A5475BE38E86A33E FOREIGN KEY (rack_id) REFERENCES rack (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE accounant_account_settings DROP FOREIGN KEY FK_5695655F9B6B5FBA');
        $this->addSql('ALTER TABLE basket DROP FOREIGN KEY FK_2246507BA76ED395');
        $this->addSql('ALTER TABLE basket_order DROP FOREIGN KEY FK_A351D8A28D9F6D38');
        $this->addSql('ALTER TABLE basket_order DROP FOREIGN KEY FK_A351D8A2293CD56D');
        $this->addSql('ALTER TABLE basket_order DROP FOREIGN KEY FK_A351D8A2A76ED395');
        $this->addSql('ALTER TABLE basket_order_product DROP FOREIGN KEY FK_27137617E93062E8');
        $this->addSql('ALTER TABLE basket_order_product DROP FOREIGN KEY FK_27137617737BADD9');
        $this->addSql('ALTER TABLE carrier_settings DROP FOREIGN KEY FK_9D169A4121DFC797');
        $this->addSql('ALTER TABLE document DROP FOREIGN KEY FK_D8698A76C54C8C93');
        $this->addSql('ALTER TABLE document DROP FOREIGN KEY FK_D8698A766BF700BD');
        $this->addSql('ALTER TABLE document DROP FOREIGN KEY FK_D8698A7658C0EBBC');
        $this->addSql('ALTER TABLE document_item DROP FOREIGN KEY FK_B8AFA98DC33F7837');
        $this->addSql('ALTER TABLE document_shelf_stocktaking DROP FOREIGN KEY FK_904241AC7C12FBC0');
        $this->addSql('ALTER TABLE document_shelf_stocktaking DROP FOREIGN KEY FK_904241ACF3F59E45');
        $this->addSql('ALTER TABLE document_shelf_stocktaking_data DROP FOREIGN KEY FK_64F61D03F50FDA5');
        $this->addSql('ALTER TABLE document_shelf_stocktaking_data DROP FOREIGN KEY FK_64F61D03C7933F5C');
        $this->addSql('ALTER TABLE document_stocktaking DROP FOREIGN KEY FK_922B45F16F7858F');
        $this->addSql('ALTER TABLE ean DROP FOREIGN KEY FK_67B1C6604584665A');
        $this->addSql('ALTER TABLE ean_shelf_quantity DROP FOREIGN KEY FK_F0B915B1F50FDA5');
        $this->addSql('ALTER TABLE ean_shelf_quantity DROP FOREIGN KEY FK_F0B915B17C12FBC0');
        $this->addSql('ALTER TABLE email_template DROP FOREIGN KEY FK_9C0600CA37D8AD65');
        $this->addSql('ALTER TABLE integration DROP FOREIGN KEY FK_FDE96D9B7E3C61F9');
        $this->addSql('ALTER TABLE integration_data DROP FOREIGN KEY FK_986DCE789E82DDEA');
        $this->addSql('ALTER TABLE integration_data DROP FOREIGN KEY FK_986DCE78B51E65F3');
        $this->addSql('ALTER TABLE integration_data DROP FOREIGN KEY FK_986DCE78DC565623');
        $this->addSql('ALTER TABLE integration_settings DROP FOREIGN KEY FK_F79F652F9E82DDEA');
        $this->addSql('ALTER TABLE integration_status_mapping DROP FOREIGN KEY FK_81572AA35AD91B65');
        $this->addSql('ALTER TABLE integration_status_mapping DROP FOREIGN KEY FK_81572AA3D7707B45');
        $this->addSql('ALTER TABLE `log` DROP FOREIGN KEY FK_8F3F68C5A76ED395');
        $this->addSql('ALTER TABLE `order` DROP FOREIGN KEY FK_F529939815F9D748');
        $this->addSql('ALTER TABLE `order` DROP FOREIGN KEY FK_F5299398F00D725C');
        $this->addSql('ALTER TABLE order_delivery DROP FOREIGN KEY FK_D6790EA18D9F6D38');
        $this->addSql('ALTER TABLE order_delivery_shipment_data DROP FOREIGN KEY FK_6B8A067273FA875');
        $this->addSql('ALTER TABLE order_delivery_shipment_data DROP FOREIGN KEY FK_6B8A067221DFC797');
        $this->addSql('ALTER TABLE order_email_on_status DROP FOREIGN KEY FK_7BEFE9C46BF700BD');
        $this->addSql('ALTER TABLE order_email_on_status DROP FOREIGN KEY FK_7BEFE9C4131A730F');
        $this->addSql('ALTER TABLE order_email_on_status DROP FOREIGN KEY FK_7BEFE9C4C50EB1C4');
        $this->addSql('ALTER TABLE order_history_change_review DROP FOREIGN KEY FK_6E22DB9D727ACA70');
        $this->addSql('ALTER TABLE order_history_change_review DROP FOREIGN KEY FK_6E22DB9DA76ED395');
        $this->addSql('ALTER TABLE order_invoice DROP FOREIGN KEY FK_661FBE0F8D9F6D38');
        $this->addSql('ALTER TABLE order_product DROP FOREIGN KEY FK_2530ADE6FCDAEAAA');
        $this->addSql('ALTER TABLE order_status DROP FOREIGN KEY FK_B88F75C98D0C9323');
        $this->addSql('ALTER TABLE product_reservation DROP FOREIGN KEY FK_EEE7D74A4584665A');
        $this->addSql('ALTER TABLE product_reservation DROP FOREIGN KEY FK_EEE7D74A7C12FBC0');
        $this->addSql('ALTER TABLE product_reservation DROP FOREIGN KEY FK_EEE7D74A1AC3754');
        $this->addSql('ALTER TABLE product_reservation DROP FOREIGN KEY FK_EEE7D74A27D5BCDB');
        $this->addSql('ALTER TABLE product_reservation DROP FOREIGN KEY FK_EEE7D74ADD4481AD');
        $this->addSql('ALTER TABLE quantity_log DROP FOREIGN KEY FK_509E352BDD4481AD');
        $this->addSql('ALTER TABLE quantity_log DROP FOREIGN KEY FK_509E352BE00EE68D');
        $this->addSql('ALTER TABLE quantity_log DROP FOREIGN KEY FK_509E352B3B3DCB63');
        $this->addSql('ALTER TABLE quantity_log DROP FOREIGN KEY FK_509E352BB8DF54EA');
        $this->addSql('ALTER TABLE rack DROP FOREIGN KEY FK_3DD796A86F7858F');
        $this->addSql('ALTER TABLE shelf DROP FOREIGN KEY FK_A5475BE38E86A33E');
        $this->addSql('DROP TABLE accounant_account');
        $this->addSql('DROP TABLE accounant_account_settings');
        $this->addSql('DROP TABLE basket');
        $this->addSql('DROP TABLE basket_order');
        $this->addSql('DROP TABLE basket_order_product');
        $this->addSql('DROP TABLE block_placements');
        $this->addSql('DROP TABLE carrier');
        $this->addSql('DROP TABLE carrier_settings');
        $this->addSql('DROP TABLE document');
        $this->addSql('DROP TABLE document_item');
        $this->addSql('DROP TABLE document_seller_company');
        $this->addSql('DROP TABLE document_shelf_stocktaking');
        $this->addSql('DROP TABLE document_shelf_stocktaking_data');
        $this->addSql('DROP TABLE document_status');
        $this->addSql('DROP TABLE document_stocktaking');
        $this->addSql('DROP TABLE document_type');
        $this->addSql('DROP TABLE ean');
        $this->addSql('DROP TABLE ean_shelf_quantity');
        $this->addSql('DROP TABLE email_account');
        $this->addSql('DROP TABLE email_template');
        $this->addSql('DROP TABLE integration');
        $this->addSql('DROP TABLE integration_data');
        $this->addSql('DROP TABLE integration_settings');
        $this->addSql('DROP TABLE integration_status_mapping');
        $this->addSql('DROP TABLE `log`');
        $this->addSql('DROP TABLE module_states');
        $this->addSql('DROP TABLE `order`');
        $this->addSql('DROP TABLE order_delivery');
        $this->addSql('DROP TABLE order_delivery_shipment_data');
        $this->addSql('DROP TABLE order_email_on_status');
        $this->addSql('DROP TABLE order_flag');
        $this->addSql('DROP TABLE order_history_change_review');
        $this->addSql('DROP TABLE order_invoice');
        $this->addSql('DROP TABLE order_product');
        $this->addSql('DROP TABLE order_status');
        $this->addSql('DROP TABLE order_status_tab');
        $this->addSql('DROP TABLE product');
        $this->addSql('DROP TABLE product_reservation');
        $this->addSql('DROP TABLE quantity_log');
        $this->addSql('DROP TABLE rack');
        $this->addSql('DROP TABLE rule');
        $this->addSql('DROP TABLE settings');
        $this->addSql('DROP TABLE shelf');
        $this->addSql('DROP TABLE storehouse');
        $this->addSql('DROP TABLE user_login_history');
        $this->addSql('DROP TABLE users');
        $this->addSql('DROP TABLE messenger_messages');
    }
}
