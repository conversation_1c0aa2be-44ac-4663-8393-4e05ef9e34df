{"doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.12", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.12", "ref": "ed5e0ed0b451d6ade844d3a28e21fe70d20daee7"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "friendsofphp/php-cs-fixer": {"version": "3.68", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "knplabs/knp-snappy-bundle": {"version": "1.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.5", "ref": "c81bdcf4a9d4e7b1959071457f9608631865d381"}, "files": ["config/packages/knp_snappy.yaml"]}, "lexik/jwt-authentication-bundle": {"version": "2.20", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "e9481b233a11ef7e15fe055a2b21fd3ac1aa2bb7"}, "files": ["config/packages/lexik_jwt_authentication.yaml"]}, "nelmio/cors-bundle": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "sca/rules-bundle": {"version": "dev-master"}, "squizlabs/php_codesniffer": {"version": "3.11", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.6", "ref": "1019e5c08d4821cb9b77f4891f8e9c31ff20ac6f"}, "files": ["phpcs.xml.dist"]}, "symfony-cmf/routing-bundle": {"version": "3.1.1"}, "symfony/asset-mapper": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "5ad1308aa756d58f999ffbe1540d1189f5d7d14a"}, "files": ["assets/app.js", "assets/styles/app.css", "config/packages/asset_mapper.yaml", "importmap.php"]}, "symfony/console": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/flex": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/form": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "7d86a6723f4a623f59e2bf966b6aad2fc461d36b"}, "files": ["config/packages/csrf.yaml"]}, "symfony/framework-bundle": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "6356c19b9ae08e7763e4ba2d9ae63043efc75db5"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/lock": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "8e937ff2b4735d110af1770f242c1107fdab4c8e"}, "files": ["config/packages/lock.yaml"]}, "symfony/mailer": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "df66ee1f226c46f01e85c29c2f7acce0596ba35a"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.57", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/routing": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/scheduler": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "caea3c928ee9e1b21288fd76aef36f16ea355515"}, "files": ["src/Schedule.php"]}, "symfony/security-bundle": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/stimulus-bundle": {"version": "2.28", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.20", "ref": "e058471c5502e549c1404ebdd510099107bb5549"}, "files": ["assets/bootstrap.js", "assets/controllers.json", "assets/controllers/csrf_protection_controller.js", "assets/controllers/hello_controller.js"]}, "symfony/translation": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "620a1b84865ceb2ba304c8f8bf2a185fbf32a843"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/uid": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "0df5844274d871b37fc3816c57a768ffc60a43a5"}}, "symfony/ux-turbo": {"version": "2.28", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.20", "ref": "287f7c6eb6e9b65e422d34c00795b360a787380b"}, "files": ["config/packages/ux_turbo.yaml"]}, "symfony/validator": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfonycasts/sass-bundle": {"version": "v0.8.2"}, "symfonycasts/verify-email-bundle": {"version": "v1.17.0"}, "twig/extra-bundle": {"version": "v3.21.0"}}