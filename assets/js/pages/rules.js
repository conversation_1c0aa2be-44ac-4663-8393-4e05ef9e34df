import * as bootstrap from 'bootstrap';

document.addEventListener('DOMContentLoaded', function () {
    const I18N = JSON.parse(document.getElementById('ruleModalTranslations').textContent);

    const modalEl = document.getElementById('ruleModal');
    const modal = new bootstrap.Modal(modalEl, { backdrop: 'static' });
    const formRoot = modalEl.querySelector('#rule-form-root');

    // Nowa reguła
    document.addEventListener('click', (event) => {
        const createBtn = event.target.closest('[data-role="create-rule"]');
        if (createBtn) {
            window.RuleForm.openNew({
                container: formRoot,
                createUrl: createBtn.dataset.createUrl,
                defaults: {
                    name: 'NoName',
                    weight: 0,
                },
            });
            modal.show();
        }
    });

    // Edycja reguły
    document.addEventListener('click', (event) => {
        const editBtn = event.target.closest('[data-role="edit-rule"]');
        if (editBtn) {
            window.RuleForm.openEdit({
                container: formRoot,
                editUrl: editBtn.dataset.editUrl,
                rule: JSON.parse(editBtn.getAttribute('data-rule')),
            });
            modal.show();
        }
    });

    // Usuwanie reguł
    document.addEventListener('click', (event) => {
        const deleteBtn = event.target.closest('[data-role="delete-rule"]');
        if (deleteBtn && confirm('Na pewno chcesz usunąć?')) {
            fetch(deleteBtn.dataset.deleteUrl, {
                method: 'POST',
                headers: { 'X-Requested-With': 'XMLHttpRequest' },
            })
                .then((resp) => {
                    if (resp.redirected) {
                        window.location.href = resp.url;
                    }
                })
                .catch((err) => console.error(err));
        }
    });

    function createElement(tagName, attributes = {}, ...children) {
        const element = document.createElement(tagName);

        if (attributes) {
            Object.keys(attributes).forEach((attrKey) => {
                if (attrKey === 'dataset') {
                    // Ustaw atrybuty data-*
                    Object.keys(attributes.dataset).forEach((dataKey) => {
                        element.dataset[dataKey] = attributes.dataset[dataKey];
                    });
                } else if (attrKey === 'class') {
                    // Ustaw klasy CSS
                    element.className = attributes[attrKey];
                } else if (attrKey === 'style') {
                    // Ustaw style inline
                    Object.assign(element.style, attributes[attrKey]);
                } else if (attrKey.startsWith('on') && typeof attributes[attrKey] === 'function') {
                    // Ustaw listener zdarzenia (np. onclick, onchange)
                    const eventType = attrKey.slice(2); // usuwa "on"
                    element.addEventListener(eventType, attributes[attrKey]);
                } else {
                    // Ustaw zwykły atrybut HTML
                    element.setAttribute(attrKey, attributes[attrKey]);
                }
            });
        }

        // Dodaj dzieci elementu (tekst lub inne elementy)
        children.forEach((child) => {
            if (typeof child === 'string') {
                element.appendChild(document.createTextNode(child));
            } else {
                element.appendChild(child);
            }
        });

        return element;
    }

    function ensureArray(value) {
        // Jeśli wartość jest już tablicą → zwróć ją bez zmian
        if (Array.isArray(value)) {
            return value;
        }

        // Jeśli wartość jest zdefiniowana (nie null / undefined) → opakuj w tablicę
        if (value) {
            return [value];
        }

        // W przeciwnym wypadku → zwróć pustą tablicę
        return [];
    }

    function getSelectedValues(selectElement) {
        // Pobierz wszystkie wybrane opcje jako tablicę
        const selectedOptions = Array.from(selectElement.selectedOptions);

        // Zmapuj opcje na ich wartości (atrybut value)
        const values = selectedOptions.map((option) => option.value);

        return values;
    }

    function setValue(element, value) {
        // Jeśli element nie istnieje — przerwij
        if (!element) return;

        // Obsługa pól SELECT z możliwością wyboru wielu opcji
        if (element.tagName === 'SELECT' && element.multiple) {
            const desiredValues = Array.isArray(value) ? value.map(String) : [String(value)];
            Array.from(element.options).forEach((option) => {
                option.selected = desiredValues.includes(String(option.value));
            });
            return;
        }

        // Obsługa pól SELECT (pojedynczy wybór)
        if (element.tagName === 'SELECT') {
            element.value = (value ?? '').toString();
            return;
        }

        // Obsługa pól numerycznych
        if (element.type === 'number') {
            element.value = value === null || value === undefined || value === '' ? '' : Number(value);
            return;
        }

        // Obsługa pozostałych typów pól (np. text, email)
        element.value = value === null || value === undefined ? '' : String(value);
    }

    /**
     * Pobiera i parsuje dane JSON zapisane w treści elementu HTML.
     *
     * @param {string} elementId - ID elementu, z którego ma być pobrany JSON.
     * @returns {any|null} Zwraca sparsowany obiekt lub null, jeśli element nie istnieje.
     */
    function readGlobalJson(elementId) {
        // Znajdź element o podanym ID
        const targetElement = document.getElementById(elementId);

        // Jeśli element istnieje, zwróć sparsowany JSON, w przeciwnym razie null
        return targetElement ? JSON.parse(targetElement.textContent) : null;
    }

    const EVENTS = readGlobalJson('events-json') || [];
    const CONDITIONS = readGlobalJson('conditions-json') || [];
    const ACTIONS = readGlobalJson('actions-json') || [];

    let FILTERED_CONDITIONS = CONDITIONS;
    let FILTERED_ACTIONS = ACTIONS;

    function allowedByContext(requiredKeys, providedKeys) {
        // empty required => usable anywhere
        const req = Array.isArray(requiredKeys) ? requiredKeys : [];
        if (req.length === 0) return true;
        const provided = Array.isArray(providedKeys) ? providedKeys : [];
        return req.every((k) => provided.includes(k));
    }

    function applyFilterForEvent(eventName) {
        const ev = ensureArray(EVENTS).find((e) => e.name === eventName);
        const provided = ev && Array.isArray(ev.providedContext) ? ev.providedContext : [];

        FILTERED_CONDITIONS = ensureArray(CONDITIONS).filter((c) => allowedByContext(c.requiredContext, provided));
        FILTERED_ACTIONS = ensureArray(ACTIONS).filter((a) => allowedByContext(a.requiredContext, provided));
    }

    function rebuildSelectOptions(selectEl, items, placeholder) {
        const current = selectEl.value;
        selectEl.innerHTML = '';
        selectEl.appendChild(createElement('option', { value: '' }, placeholder));
        items.forEach((it) => {
            selectEl.appendChild(createElement('option', { value: it.name }, it.label || it.name));
        });
        // keep value if still allowed
        if (items.some((it) => it.name === current)) selectEl.value = current;
    }

    // ------------------- UI builders -------------------
    /**
     * Buduje listę opcji dla elementu <select> z dostępnymi zdarzeniami.
     *
     * @param {HTMLSelectElement} selectElement - Element <select>, do którego zostaną dodane opcje.
     */
    function buildEventSelect(selectElement) {
        // Wyczyść wszystkie istniejące opcje
        selectElement.innerHTML = '';

        // Dodaj opcję domyślną (placeholder)
        selectElement.appendChild(createElement('option', { value: '' }, `-- ${I18N?.placeholders?.chooseEvent} --`));

        // Iteruj po liście dostępnych zdarzeń i dodaj je jako opcje
        ensureArray(EVENTS).forEach((eventItem) => {
            const optionValue = typeof eventItem === 'string' ? eventItem : eventItem.name || eventItem.value || '';
            const optionLabel = typeof eventItem === 'string' ? eventItem : eventItem.label || optionValue;

            selectElement.appendChild(createElement('option', { value: optionValue }, optionLabel));
        });
    }

    /**
     * Normalizuje definicje pól dla warunku, wyciągając wszystkie pola z allowedValues.
     *
     * @param {Object} conditionDefinition - Definicja warunku (może zawierać allowedValues).
     * @returns {Array} Tablica definicji pól (bez wartości null/undefined).
     */
    function normalizeFieldDefsFromCondition(def) {
        // Debug: co przyszło z backendu
        console.log('[normalizeCondition] raw def:', def);

        // Preferuj `settings`
        if (def && def.settings) {
            const s = def.settings;
            const list = Array.isArray(s) ? s : s.fields ? s.fields : [];
            const out = [];
            list.forEach((item) => {
                if (!item) return;
                // element już jest definicją pola
                if (item.name || item.type || item.label || item.options || item.allowedOperators) {
                    out.push(item);
                }
                // lub element zawiera "fields"
                if (Array.isArray(item.fields)) {
                    out.push(...item.fields);
                }
            });
            console.log('[normalizeCondition] from settings ->', out);
            return out.filter(Boolean);
        }

        // Wsteczna kompatybilność: allowedValues
        const out = [];
        const av = def && def.allowedValues ? def.allowedValues : [];
        const arr = Array.isArray(av) ? av : [av];
        arr.forEach((v) => {
            if (!v) return;
            if (Array.isArray(v.fields)) out.push(...v.fields);
            else out.push(v);
        });
        console.log('[normalizeCondition] from allowedValues ->', out);
        return out.filter(Boolean);
    }

    /**
     * Normalizuje definicje pól dla akcji, wyciągając wszystkie pola z allowedValues.
     *
     * @param {Object} actionDefinition - Definicja akcji (może zawierać allowedValues).
     * @returns {Array} Tablica definicji pól (bez wartości null/undefined).
     */
    function normalizeFieldDefsFromAction(actionDefinition) {
        // Pobierz allowedValues lub pustą tablicę
        const allowedValues = actionDefinition?.allowedValues ?? [];

        // Jeśli allowedValues samo w sobie zawiera "fields", zwróć je bezpośrednio
        if (allowedValues && allowedValues.fields) {
            return allowedValues.fields;
        }

        // Wynikowa tablica z polami
        const normalizedFields = [];

        // Iteruj po wszystkich elementach allowedValues
        ensureArray(allowedValues).forEach((value) => {
            if (value && value.fields) {
                // Jeśli obiekt posiada własne pola — dodaj wszystkie
                normalizedFields.push(...value.fields);
            } else {
                // W przeciwnym razie — dodaj wartość jako pojedynczy element
                normalizedFields.push(value);
            }
        });

        // Usuń falsy wartości
        return normalizedFields.filter(Boolean);
    }

    /**
     * Tworzy element <select> z listą dozwolonych operatorów.
     *
     * @param {Array|Object} allowedOperators - Lista operatorów lub obiekt z właściwością "options".
     * @returns {HTMLSelectElement} Element select z wypełnionymi opcjami.
     */
    function makeOperatorSelect(allowedOperators) {
        // Utwórz pusty element <select> z klasą i atrybutem data-role
        const operatorSelect = createElement('select', {
            class: 'form-select',
            'data-role': 'operator',
        });

        // Pobierz listę operatorów z allowedOperators.options,
        // lub potraktuj allowedOperators jako tablicę
        const operatorsList = allowedOperators?.options ?? ensureArray(allowedOperators);

        // Dodaj opcje do selecta
        operatorsList.forEach((operatorItem) => {
            // Jeśli element jest obiektem, pobierz value i label
            const optionValue = operatorItem && typeof operatorItem === 'object' ? operatorItem.value ?? operatorItem : operatorItem;

            const optionLabel = operatorItem && typeof operatorItem === 'object' ? operatorItem.label ?? String(optionValue) : String(optionValue);

            // Dodaj opcję do <select>
            operatorSelect.appendChild(createElement('option', { value: optionValue }, optionLabel));
        });

        return operatorSelect;
    }

    /**
     * Tworzy element formularza (<input> lub <select>) na podstawie definicji pola.
     *
     * @param {Object} fieldDef - Definicja pola (typ, opcje, wartości min/max).
     * @returns {HTMLElement} Element formularza gotowy do osadzenia w DOM.
     */
    function makeValueInput(fieldDef) {
        // Ustal typ pola, domyślnie "text"
        const fieldType = fieldDef.type || 'text';

        // --- Obsługa pól select ---
        if (fieldType === 'select' || fieldType === 'selectMultiple') {
            // Tworzymy <select>
            const selectElement = createElement('select', {
                class: 'form-select',
                'data-role': 'value',
            });

            // Ustaw tryb multi-select, jeśli dotyczy
            if (fieldType === 'selectMultiple') {
                selectElement.multiple = true;
            }

            // Dodaj opcje do selecta
            (fieldDef.options || []).forEach((optionItem) => {
                const optionValue = optionItem && typeof optionItem === 'object' ? optionItem.value ?? optionItem : optionItem;

                const optionLabel = optionItem && typeof optionItem === 'object' ? optionItem.label ?? String(optionValue) : String(optionValue);

                selectElement.appendChild(createElement('option', { value: optionValue }, optionLabel));
            });

            return selectElement;
        }

        // --- Obsługa pól liczbowych ---
        if (fieldType === 'number') {
            const numberInput = createElement('input', {
                type: 'number',
                class: 'form-control',
                'data-role': 'value',
            });

            // Dodaj ograniczenia min/max, jeśli są zdefiniowane
            if (fieldDef.min !== undefined) numberInput.min = fieldDef.min;
            if (fieldDef.max !== undefined) numberInput.max = fieldDef.max;

            return numberInput;
        }

        // --- Domyślnie zwracamy input tekstowy ---
        return createElement('input', {
            type: 'text',
            class: 'form-control',
            'data-role': 'value',
        });
    }

    /**
     * Buduje wiersz formularza dla pojedynczego pola wraz z etykietą, polem wartości
     * i opcjonalnym polem operatora.
     *
     * @param {Object} fieldDef - Definicja pola (nazwa, etykieta, typ, opcje operatorów).
     * @param {boolean} includeOperator - Czy dołączyć kolumnę z operatorem.
     * @returns {HTMLElement} - Gotowy element <div> reprezentujący wiersz.
     */
    function buildFieldRow(fieldDef, includeOperator) {
        // Kontener główny dla wiersza pola
        const fieldRow = createElement('div', {
            class: 'row g-2 align-items-center',
            dataset: {
                fieldName: fieldDef.name,
                fieldType: fieldDef.type || 'text',
                withOperator: includeOperator ? '1' : '0',
            },
        });

        // Kolumna z etykietą
        const labelColumn = createElement('div', { class: 'col-md-3' }, createElement('label', { class: 'form-label mb-0' }, fieldDef.label || fieldDef.name));

        // Kolumna z polem wartości
        const valueColumn = createElement('div', { class: 'col-md-6' });
        valueColumn.appendChild(makeValueInput(fieldDef));

        // Dodanie kolumny z etykietą i kolumny z polem wartości do wiersza
        fieldRow.appendChild(labelColumn);
        fieldRow.appendChild(valueColumn);

        // Opcjonalna kolumna z operatorem
        if (includeOperator && fieldDef.allowedOperators) {
            const operatorColumn = createElement('div', { class: 'col-md-3' });
            operatorColumn.appendChild(makeOperatorSelect(fieldDef.allowedOperators));
            fieldRow.appendChild(operatorColumn);
        }

        return fieldRow;
    }

    /**
     * Tworzy blok pojedynczego warunku (condition) z listą wyboru i dynamicznie
     * generowanymi polami w zależności od wybranej opcji.
     *
     * @returns {HTMLElement} - Element kontenera warunku.
     */
    function buildConditionBlock() {
        // Główny kontener całego bloku warunku
        const conditionBlock = createElement('div', {
            class: 'border rounded p-2 mb-2',
            dataset: { type: 'condition' },
        });

        // Nagłówek bloku (select + przycisk usuń)
        const blockHeader = createElement('div', { class: 'd-flex gap-2 align-items-center mb-2' });

        // Lista wyboru warunku
        const conditionSelect = createElement('select', { class: 'form-select' });
        conditionSelect.appendChild(createElement('option', { value: '' }, `-- ${I18N?.placeholders?.chooseCondition} --`));

        // Dodanie opcji z globalnej listy CONDITIONS
        ensureArray(FILTERED_CONDITIONS).forEach((conditionDef) => {
            conditionSelect.appendChild(createElement('option', { value: conditionDef.name }, conditionDef.label || conditionDef.name));
        });

        // Przycisk usuwania bloku
        const removeButton = createElement(
            'button',
            {
                type: 'button',
                class: 'btn btn-sm btn-outline-danger ms-2',
                onclick: () => conditionBlock.remove(),
            },
            createElement('i', { class: 'fa fa-trash me-2' }),
            I18N.common.remove
        );

        // Dodanie selecta i przycisku do nagłówka
        blockHeader.appendChild(conditionSelect);
        blockHeader.appendChild(removeButton);

        // Kontener na pola dynamiczne
        const fieldsContainer = createElement('div', { class: 'mt-2' });

        // Obsługa zmiany wybranego warunku
        conditionSelect.addEventListener('change', function () {
            // Czyścimy poprzednie pola
            fieldsContainer.innerHTML = '';

            // Szukamy definicji wybranego warunku
            const selectedDefinition = ensureArray(CONDITIONS).find((conditionDef) => conditionDef.name === this.value);
            console.log('Selected condition definition:', selectedDefinition);

            if (!selectedDefinition) return;

            // Tworzymy pola na podstawie definicji
            normalizeFieldDefsFromCondition(selectedDefinition).forEach((fieldDef) => {
                fieldsContainer.appendChild(buildFieldRow(fieldDef, !!fieldDef.allowedOperators));
            });
        });

        // Złożenie całego bloku
        conditionBlock.appendChild(blockHeader);
        conditionBlock.appendChild(fieldsContainer);

        return conditionBlock;
    }

    /**
     * Tworzy blok pojedynczej akcji (action) z listą wyboru i dynamicznie
     * generowanymi polami w zależności od wybranej opcji.
     *
     * @returns {HTMLElement} - Element kontenera akcji.
     */
    function buildActionBlock() {
        // Główny kontener całego bloku akcji
        const actionBlock = createElement('div', {
            class: 'border rounded p-2 mb-2',
            dataset: { type: 'action' },
        });

        // Nagłówek bloku (select + przycisk usuń)
        const blockHeader = createElement('div', { class: 'd-flex gap-2 align-items-center mb-2' });

        // Lista wyboru akcji
        const actionSelect = createElement('select', { class: 'form-select' });
        actionSelect.appendChild(createElement('option', { value: '' }, `-- ${I18N?.placeholders?.chooseAction} --`));

        // Dodanie opcji z globalnej listy ACTIONS
        ensureArray(FILTERED_ACTIONS).forEach((actionDef) => {
            actionSelect.appendChild(createElement('option', { value: actionDef.name }, actionDef.label || actionDef.name));
        });

        // Przycisk usuwania bloku
        const removeButton = createElement(
            'button',
            {
                type: 'button',
                class: 'btn btn-sm btn-outline-danger ms-2',
                onclick: () => actionBlock.remove(),
            },
            createElement('i', { class: 'fa fa-trash me-2' }),
            I18N.common.remove
        );

        // Dodanie selecta i przycisku do nagłówka
        blockHeader.appendChild(actionSelect);
        blockHeader.appendChild(removeButton);

        // Kontener na pola dynamiczne
        const fieldsContainer = createElement('div', { class: 'mt-2' });

        // Obsługa zmiany wybranej akcji
        actionSelect.addEventListener('change', function () {
            // Czyścimy poprzednie pola
            fieldsContainer.innerHTML = '';

            // Szukamy definicji wybranej akcji
            const selectedDefinition = ensureArray(ACTIONS).find((actionDef) => actionDef.name === this.value);
            if (!selectedDefinition) return;

            // Tworzymy pola na podstawie definicji
            normalizeFieldDefsFromAction(selectedDefinition).forEach((fieldDef) => {
                fieldsContainer.appendChild(buildFieldRow(fieldDef, false));
            });
        });

        // Złożenie całego bloku
        actionBlock.appendChild(blockHeader);
        actionBlock.appendChild(fieldsContainer);

        return actionBlock;
    }

    // ------------------- serialization -------------------
    /**
     * Odczytuje wszystkie warunki (conditions) z podanego kontenera
     * i zwraca je w postaci tablicy obiektów.
     *
     * @param {HTMLElement} container - Element zawierający bloki warunków.
     * @returns {Array} - Lista obiektów { name, fields } reprezentujących warunki.
     */
    function readConditions(container) {
        const conditionsList = [];

        // Przechodzimy po wszystkich blokach oznaczonych jako "condition"
        container.querySelectorAll('[data-type="condition"]').forEach((conditionBlock) => {
            const conditionSelect = conditionBlock.querySelector('select');

            // Pomijamy, jeśli brak wybranej nazwy
            if (!conditionSelect || !conditionSelect.value) return;

            const conditionFields = [];

            // Pobieramy wszystkie pola w obrębie warunku
            conditionBlock.querySelectorAll('[data-field-name]').forEach((fieldRow) => {
                const fieldName = fieldRow.dataset.fieldName;
                const hasOperator = fieldRow.dataset.withOperator === '1';

                const valueElement = fieldRow.querySelector('[data-role="value"]');
                const operatorElement = fieldRow.querySelector('[data-role="operator"]');

                // Domyślna wartość pola
                let fieldValue = null;

                // Odczyt wartości w zależności od typu elementu
                if (valueElement) {
                    if (valueElement.tagName === 'SELECT' && valueElement.multiple) {
                        fieldValue = getSelectedValues(valueElement);
                    } else if (valueElement.type === 'number') {
                        fieldValue = valueElement.value === '' ? null : Number(valueElement.value);
                    } else {
                        fieldValue = valueElement.value;
                    }
                }

                // Tworzymy obiekt pola
                const fieldData = { name: fieldName, value: fieldValue };

                // Dodajemy operator, jeśli występuje
                if (hasOperator && operatorElement) {
                    fieldData.operator = operatorElement.value;
                }

                conditionFields.push(fieldData);
            });

            // Dodajemy obiekt warunku do listy
            conditionsList.push({
                name: conditionSelect.value,
                fields: conditionFields,
            });
        });

        return conditionsList;
    }

    /**
     * Odczytuje wszystkie akcje (actions) z podanego kontenera
     * i zwraca je w postaci tablicy obiektów.
     *
     * @param {HTMLElement} container - Element zawierający bloki akcji.
     * @returns {Array} - Lista obiektów { name, fields } reprezentujących akcje.
     */
    function readActions(container) {
        const actionsList = [];

        // Przechodzimy po wszystkich blokach oznaczonych jako "action"
        container.querySelectorAll('[data-type="action"]').forEach((actionBlock) => {
            const actionSelect = actionBlock.querySelector('select');

            // Pomijamy, jeśli brak wybranej nazwy akcji
            if (!actionSelect || !actionSelect.value) return;

            const actionFields = {};

            // Pobieramy wszystkie pola w obrębie akcji
            actionBlock.querySelectorAll('[data-field-name]').forEach((fieldRow) => {
                const fieldName = fieldRow.dataset.fieldName;
                const valueElement = fieldRow.querySelector('[data-role="value"]');

                // Domyślna wartość pola
                let fieldValue = null;

                // Odczyt wartości w zależności od typu elementu
                if (valueElement) {
                    if (valueElement.tagName === 'SELECT' && valueElement.multiple) {
                        fieldValue = getSelectedValues(valueElement);
                    } else if (valueElement.type === 'number') {
                        fieldValue = valueElement.value === '' ? null : Number(valueElement.value);
                    } else {
                        fieldValue = valueElement.value;
                    }
                }

                actionFields[fieldName] = fieldValue;
            });

            // Dodajemy obiekt akcji do listy
            actionsList.push({
                name: actionSelect.value,
                fields: actionFields,
            });
        });

        return actionsList;
    }

    // ------------------- public API -------------------
    /**
     * Inicjalizuje wspólne elementy formularza reguły:
     * - listę dostępnych eventów
     * - przyciski dodawania warunków i akcji
     *
     * @param {HTMLElement} container - Główny kontener formularza reguły.
     */
    function initCommon(container) {
        // Pobranie referencji do kluczowych elementów formularza
        const eventSelectElement = container.querySelector('#eventSelect');
        const addConditionButton = container.querySelector('#addConditionBtn');
        const addActionButton = container.querySelector('#addActionBtn');
        const conditionsContainerElement = container.querySelector('#conditionsContainer');
        const actionsContainerElement = container.querySelector('#actionsContainer');

        // Wypełnienie selecta eventów dostępnymi opcjami
        buildEventSelect(eventSelectElement);

        // 🔹 disable buttons until event chosen
        addConditionButton.disabled = true;
        addActionButton.disabled = true;

        // 🔹 when event changes, filter and refresh selects
        eventSelectElement.addEventListener('change', () => {
            const evName = eventSelectElement.value || '';
            applyFilterForEvent(evName);

            // 🔹 refresh existing blocks after event change
            refreshExistingBlocks(conditionsContainerElement, actionsContainerElement);
            clearActionsAndConditions();

            // enable/disable add buttons
            const enabled = !!evName;
            addConditionButton.disabled = !enabled;
            addActionButton.disabled = !enabled;
        });

        // Obsługa przycisku dodawania nowego warunku
        addConditionButton.onclick = () => {
            const newConditionBlock = buildConditionBlock();
            conditionsContainerElement.appendChild(newConditionBlock);
        };

        // Obsługa przycisku dodawania nowej akcji
        addActionButton.onclick = () => {
            const newActionBlock = buildActionBlock();
            actionsContainerElement.appendChild(newActionBlock);
        };
    }

    /**
     * Wypełnia formularz reguły danymi z obiektu rule.
     *
     * @param {HTMLElement} container - Główny kontener formularza.
     * @param {Object} rule - Obiekt z danymi reguły.
     */
    function hydrate(container, rule) {
        // Ustawienie podstawowych pól: nazwa, waga, event
        container.querySelector('#ruleName').value = rule.name ?? 'NoName';
        container.querySelector('#ruleWeight').value = rule.weight ?? 0;

        const eventSelectElement = container.querySelector('#eventSelect');
        setValue(eventSelectElement, rule.event || '');

        // 🔹 apply filter before populating selects
        applyFilterForEvent(rule.event || '');

        // Kontenery na warunki i akcje
        const conditionsContainerElement = container.querySelector('#conditionsContainer');
        const actionsContainerElement = container.querySelector('#actionsContainer');

        // Wyczyść zawartość przed ponownym wypełnieniem
        conditionsContainerElement.innerHTML = '';
        actionsContainerElement.innerHTML = '';

        // --- Wypełnianie sekcji warunków ---
        ensureArray(rule.conditions).forEach((condition) => {
            const conditionBlockElement = buildConditionBlock();
            conditionsContainerElement.appendChild(conditionBlockElement);

            // Ustawienie nazwy warunku w select
            const conditionNameSelectElement = conditionBlockElement.querySelector('select');
            setValue(conditionNameSelectElement, condition.name || '');
            conditionNameSelectElement.dispatchEvent(new Event('change'));

            // Wypełnienie pól w obrębie danego warunku
            (condition.fields || []).forEach((field) => {
                const fieldRowElement = Array.from(conditionBlockElement.querySelectorAll('[data-field-name]')).find((row) => row.dataset.fieldName === field.name);

                if (!fieldRowElement) return;

                setValue(fieldRowElement.querySelector('[data-role="value"]'), field.value);

                const operatorElement = fieldRowElement.querySelector('[data-role="operator"]');
                if (operatorElement && field.operator) {
                    setValue(operatorElement, field.operator);
                }
            });
        });

        // --- Wypełnianie sekcji akcji ---
        ensureArray(rule.actions).forEach((action) => {
            const actionBlockElement = buildActionBlock();
            actionsContainerElement.appendChild(actionBlockElement);

            // Ustawienie nazwy akcji w select
            const actionNameSelectElement = actionBlockElement.querySelector('select');
            setValue(actionNameSelectElement, action.name || '');
            actionNameSelectElement.dispatchEvent(new Event('change'));

            // Wypełnienie pól w obrębie danej akcji
            const actionFields = action.fields || {};
            Object.keys(actionFields).forEach((fieldName) => {
                const fieldRowElement = Array.from(actionBlockElement.querySelectorAll('[data-field-name]')).find((row) => row.dataset.fieldName === fieldName);

                if (!fieldRowElement) return;

                setValue(fieldRowElement.querySelector('[data-role="value"]'), actionFields[fieldName]);
            });
        });
    }
    /**
     * Podpina obsługę submitu formularza reguły:
     * - ustawia URL akcji formularza,
     * - serializuje wszystkie dane formularza do jednego JSON-a,
     * - wkłada JSON do ukrytego pola #payload,
     * - pozwala na zwykły submit POST (bez preventDefault).
     *
     * @param {HTMLElement} container - Główny kontener formularza w modalu.
     * @param {string} formActionUrl - URL endpointu (create/edit), na który formularz ma być wysłany.
     */
    function wireSubmit(container, formActionUrl) {
        // Referencje do formularza i ukrytego pola, które przeniesie JSON na backend
        const formElement = container.querySelector('#ruleForm');
        const hiddenPayloadInput = container.querySelector('#payload');

        // Ustaw docelowy adres akcji
        formElement.setAttribute('action', formActionUrl);

        // Obsługa wysyłki formularza
        formElement.onsubmit = function (e) {
            // Zbierz dane z pól „statycznych”
            const nameValue = (container.querySelector('#ruleName').value || '').trim() || 'NoName';
            const eventValue = container.querySelector('#eventSelect').value || null;
            const weightValue = Number(container.querySelector('#ruleWeight').value || 0);

            // 🔹 reapply filter for current event
            applyFilterForEvent(eventValue || '');

            // Zbierz dane z sekcji dynamicznych (warunki + akcje)
            const conditionsPayload = readConditions(container.querySelector('#conditionsContainer'));
            const actionsPayload = readActions(container.querySelector('#actionsContainer'));

            // 🔹 validate compatibility with event's providedContext
            const allowedCond = new Set(ensureArray(FILTERED_CONDITIONS).map((c) => c.name));
            const allowedAct = new Set(ensureArray(FILTERED_ACTIONS).map((a) => a.name));

            const badConds = conditionsPayload.filter((c) => !allowedCond.has(c.name));
            const badActs = actionsPayload.filter((a) => !allowedAct.has(a.name));

            if (!eventValue) {
                e.preventDefault();
                alert(I18N?.errors?.eventRequired || 'Select an event first.');
                return false;
            }
            if (badConds.length || badActs.length) {
                e.preventDefault();
                const msg = (I18N?.errors?.incompatible || 'Some items are not compatible with this event.') + '\n\n' + (badConds.length ? `Conditions: ${badConds.map((x) => x.name).join(', ')}` : '') + (badConds.length && badActs.length ? '\n' : '') + (badActs.length ? `Actions: ${badActs.map((x) => x.name).join(', ')}` : '');
                alert(msg);
                return false;
            }

            // Złóż kompletny obiekt przekazywany na backend
            const payload = {
                name: nameValue,
                event: eventValue,
                weight: weightValue,
                conditions: conditionsPayload,
                actions: actionsPayload,
            };

            // Umieść JSON w ukrytym polu (backend odczyta go z requestu)
            hiddenPayloadInput.value = JSON.stringify(payload);

            // Brak preventDefault → standardowy POST + redirect po stronie serwera
        };
    }

    // Publiczny interfejs edytora reguł (wstrzykiwany globalnie)
    window.RuleForm = {
        /**
         * Otwiera modal w trybie tworzenia nowej reguły.
         *
         * @param {Object} options
         * @param {HTMLElement} options.container - Główny kontener formularza w modalu.
         * @param {string} options.createUrl - Endpoint do utworzenia nowej reguły (action formularza).
         * @param {Object} [options.defaults] - Wartości domyślne dla nowej reguły.
         */
        openNew({ container: containerElement, createUrl, defaults = {} }) {
            // Inicjalizacja wspólnych elementów UI
            initCommon(containerElement);

            // Hydratacja pustą regułą z domyślnymi wartościami
            hydrate(containerElement, {
                name: defaults.name ?? 'NoName',
                event: '',
                conditions: [],
                actions: [],
                weight: defaults.weight ?? 0,
            });

            // Podpięcie submitu do endpointu tworzenia
            wireSubmit(containerElement, createUrl);

            // Ustawienie tytułu modala (i18n)
            const modalTitleElement = containerElement.closest('.modal-content').querySelector('.modal-title');
            if (modalTitleElement) {
                modalTitleElement.textContent = I18N?.modal?.title?.create || 'Create Rule';
            }
        },

        /**
         * Otwiera modal w trybie edycji istniejącej reguły.
         *
         * @param {Object} options
         * @param {HTMLElement} options.container - Główny kontener formularza w modalu.
         * @param {string} options.editUrl - Endpoint do edycji reguły (action formularza).
         * @param {Object} options.rule - Dane istniejącej reguły do wypełnienia formularza.
         */
        openEdit({ container: containerElement, editUrl, rule }) {
            // Inicjalizacja wspólnych elementów UI
            initCommon(containerElement);

            // Hydratacja danymi reguły (fallback na pusty obiekt)
            hydrate(containerElement, rule || {});

            // Podpięcie submitu do endpointu edycji
            wireSubmit(containerElement, editUrl);

            // Ustawienie tytułu modala (i18n)
            const modalTitleElement = containerElement.closest('.modal-content').querySelector('.modal-title');
            if (modalTitleElement) {
                modalTitleElement.textContent = I18N?.modal?.title?.edit || 'Edit Rule';
            }
        },
    };
    function refreshExistingBlocks(conditionsContainerElement, actionsContainerElement) {
        // 🔹 rebuild condition selects
        conditionsContainerElement.querySelectorAll('[data-type="condition"] select').forEach((sel) => {
            rebuildSelectOptions(sel, FILTERED_CONDITIONS, `-- ${I18N?.placeholders?.chooseCondition} --`);
        });
        // 🔹 rebuild action selects
        actionsContainerElement.querySelectorAll('[data-type="action"] select').forEach((sel) => {
            rebuildSelectOptions(sel, FILTERED_ACTIONS, `-- ${I18N?.placeholders?.chooseAction} --`);
        });
    }

    function clearActionsAndConditions() {
        modalEl.querySelectorAll('[data-type="condition"]').forEach((el) => el.remove());
        modalEl.querySelectorAll('[data-type="action"]').forEach((el) => el.remove());
    }
});
