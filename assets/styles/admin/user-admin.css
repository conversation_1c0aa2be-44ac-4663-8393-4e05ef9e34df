/* User Administration Styles */

.user-admin-container {
    padding: 20px;
}

.user-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.user-table td {
    vertical-align: middle;
    padding: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
    margin-right: 10px;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-details h6 {
    margin: 0;
    font-weight: 600;
    color: #495057;
}

.user-details small {
    color: #6c757d;
}

.role-badge {
    margin-right: 4px;
    margin-bottom: 2px;
    font-size: 0.75em;
}

.status-badge {
    font-size: 0.8em;
    padding: 4px 8px;
}

.btn-group-actions {
    white-space: nowrap;
}

.btn-group-actions .btn {
    margin-right: 2px;
}

.btn-group-actions .btn:last-child {
    margin-right: 0;
}

/* Form Styles */
.user-form {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-form .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
}

.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
}

/* Role Selection */
.role-selection {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
}

.role-selection .form-check {
    margin-bottom: 8px;
}

.role-selection .form-check:last-child {
    margin-bottom: 0;
}

.role-description {
    font-size: 0.875em;
    color: #6c757d;
    margin-left: 24px;
    margin-top: -5px;
}

/* User Info Card */
.user-info-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
}

.user-info-card .info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.user-info-card .info-item:last-child {
    margin-bottom: 0;
}

.user-info-card .info-label {
    font-weight: 600;
    color: #495057;
}

.user-info-card .info-value {
    color: #6c757d;
}

/* Responsive */
@media (max-width: 768px) {
    .user-table {
        font-size: 0.875rem;
    }
    
    .user-avatar {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }
    
    .btn-group-actions {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }
    
    .btn-group-actions .btn {
        margin-right: 0;
        font-size: 0.75rem;
        padding: 4px 8px;
    }
}

/* Animation */
.user-table tbody tr {
    transition: background-color 0.2s ease;
}

.user-table tbody tr:hover {
    background-color: #f8f9fa;
}

.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Delete Modal */
.modal-content {
    border-radius: 8px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}
