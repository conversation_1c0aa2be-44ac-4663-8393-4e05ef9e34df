/* Module Administration Styles */

.module-section {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 2rem;
}

.module-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.module-section h4 {
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.module-section h4 i {
    margin-right: 0.5rem;
}

.module-section .badge {
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

/* Module cards */
.card.border-success {
    border-width: 2px;
}

.card.border-warning {
    border-width: 2px;
}

.card.border-secondary {
    border-width: 2px;
}

.card-title .badge {
    font-size: 0.7rem;
    margin-left: 0.5rem;
}

.card-text {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* Button groups */
.btn-sm {
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

/* Status indicators */
.text-success {
    color: #198754 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-secondary {
    color: #6c757d !important;
}

/* Modal customizations */
.modal-body h5 {
    color: #495057;
    margin-bottom: 1rem;
}

.modal-body p {
    margin-bottom: 0.5rem;
}

.modal-body .badge {
    font-size: 0.8rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .module-section h4 {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-sm {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

/* Animation for status changes */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Loading states */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Alert customizations */
.alert {
    border-radius: 0.375rem;
    border: none;
    font-size: 0.9rem;
}

.alert i {
    margin-right: 0.5rem;
}
