/* AssetFlow Block Administration Styles */

.min-height-200 {
    min-height: 200px;
}

.block-item, .placed-block {
    cursor: move;
    transition: all 0.2s ease;
}

.block-item:hover, .placed-block:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.region-dropzone {
    background-color: #f8f9fa;
    transition: all 0.2s ease;
}

.region-dropzone.drag-over {
    background-color: #e3f2fd;
    border-color: #2196f3 !important;
    box-shadow: 0 0 10px rgba(33, 150, 243, 0.3);
}

.drop-placeholder {
    opacity: 0.5;
    transition: opacity 0.2s ease;
}

.region-dropzone:not(:empty) .drop-placeholder {
    display: none;
}

.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    z-index: 1000;
}

.placed-block {
    transition: transform 0.2s ease;
}

.placed-block:not(.dragging):hover {
    transform: translateY(-2px);
}

.sortable-region .placed-block {
    margin-bottom: 8px;
}

.sortable-region .placed-block:last-child {
    margin-bottom: 0;
}

/* Block categories */
.badge.bg-secondary {
    font-size: 0.7rem;
}

/* Available blocks area */
.available-blocks {
    min-height: 120px;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border: 2px dashed #dee2e6;
}

.available-blocks .block-item {
    max-width: 200px;
    min-width: 150px;
}

/* Region containers */
.region-container {
    margin-bottom: 2rem;
}

.region-container h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Animation for drag feedback */
@keyframes dragPulse {
    0% { box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(33, 150, 243, 0); }
    100% { box-shadow: 0 0 0 0 rgba(33, 150, 243, 0); }
}

.region-dropzone.drag-over {
    animation: dragPulse 1.5s infinite;
}

/* Block configuration buttons */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .available-blocks {
        flex-direction: column;
    }
    
    .available-blocks .block-item {
        max-width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .region-container {
        margin-bottom: 1.5rem;
    }
}
