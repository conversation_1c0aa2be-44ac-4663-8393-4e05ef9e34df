import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
    static targets = ['container', 'template']

    connect() {
        this.index = this.containerTarget.children.length
    }

    add(event) {
        event.preventDefault()
        const content = this.templateTarget.innerHTML.replace(/__name__/g, this.index)
        this.containerTarget.insertAdjacentHTML('beforeend', content)
        this.index++
    }

    remove(event) {
        event.preventDefault()
        const item = event.target.closest('[data-collection-item]')
        if (item) {
            item.remove()
        }
    }
}