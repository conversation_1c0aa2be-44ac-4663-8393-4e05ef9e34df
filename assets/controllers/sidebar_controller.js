import { Controller } from '@hotwired/stimulus';

/*
 * Sidebar controller for mobile menu toggle
 */
export default class extends Controller {
    static targets = ["sidebar", "toggle"]

    connect() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768) {
                if (!this.sidebarTarget.contains(e.target) && 
                    !this.toggleTarget.contains(e.target)) {
                    this.closeSidebar();
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                this.closeSidebar();
            }
        });
    }

    toggle() {
        this.sidebarTarget.classList.toggle('show');
    }

    closeSidebar() {
        this.sidebarTarget.classList.remove('show');
    }
}
