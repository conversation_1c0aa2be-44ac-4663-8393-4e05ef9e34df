import { Controller } from "@hotwired/stimulus"
import * as bootstrap from 'bootstrap'

export default class extends Controller {
    static targets = ["modal", "translations"]
    static values = { 
        createUrl: String,
        editUrl: String 
    }

    connect() {
        this.I18N = JSON.parse(this.translationsTarget.textContent)
        this.modalElement = this.modalTarget.querySelector('#ruleModal')
        this.modal = new bootstrap.Modal(this.modalElement, { backdrop: 'static' })
        this.formRoot = this.modalElement.querySelector('#rule-form-root')

        // Load the RuleForm functionality
        this.loadRuleFormLogic()
    }

    // Handle create rule button click
    createRule(event) {
        const createBtn = event.currentTarget
        window.RuleForm.openNew({
            container: this.formRoot,
            createUrl: createBtn.dataset.createUrl,
            defaults: {
                name: 'NoName',
                weight: 0,
            },
        })
        this.modal.show()
    }

    // Handle edit rule button click  
    editRule(event) {
        const editBtn = event.currentTarget
        window.RuleForm.openEdit({
            container: this.formRoot,
            editUrl: editBtn.dataset.editUrl,
            rule: JSON.parse(editBtn.getAttribute('data-rule')),
        })
        this.modal.show()
    }

    // Handle delete rule button click
    deleteRule(event) {
        const deleteBtn = event.currentTarget
        if (confirm('Na pewno chcesz usunąć?')) {
            fetch(deleteBtn.dataset.deleteUrl, {
                method: 'POST',
                headers: { 'X-Requested-With': 'XMLHttpRequest' },
            })
            .then((resp) => {
                if (resp.redirected) {
                    window.location.href = resp.url
                }
            })
            .catch((err) => console.error(err))
        }
    }

    loadRuleFormLogic() {
        // This contains all the complex RuleForm logic from the original rules.js
        // For now, we'll keep it as a separate method to maintain the existing functionality
        
        const EVENTS = JSON.parse(document.getElementById('events-json').textContent)
        const CONDITIONS = JSON.parse(document.getElementById('conditions-json').textContent)
        const ACTIONS = JSON.parse(document.getElementById('actions-json').textContent)
        
        // Helper functions (simplified versions of the original)
        function ensureArray(value) {
            return Array.isArray(value) ? value : []
        }

        function createElement(tag, attributes = {}) {
            const element = document.createElement(tag)
            Object.entries(attributes).forEach(([key, value]) => {
                if (key === 'class') {
                    element.className = value
                } else {
                    element.setAttribute(key, value)
                }
            })
            return element
        }

        // Initialize common form elements
        function initCommon(container) {
            const eventSelectElement = container.querySelector('#eventSelect')
            const addConditionButton = container.querySelector('#addConditionBtn')
            const addActionButton = container.querySelector('#addActionBtn')
            const conditionsContainerElement = container.querySelector('#conditionsContainer')
            const actionsContainerElement = container.querySelector('#actionsContainer')

            // Populate events dropdown
            eventSelectElement.innerHTML = '<option value="">Wybierz event...</option>'
            ensureArray(EVENTS).forEach(event => {
                const option = createElement('option', { value: event.name })
                option.textContent = event.name
                eventSelectElement.appendChild(option)
            })

            // Add condition button handler
            addConditionButton?.addEventListener('click', () => {
                const conditionBlock = buildConditionBlock()
                conditionsContainerElement.appendChild(conditionBlock)
            })

            // Add action button handler
            addActionButton?.addEventListener('click', () => {
                const actionBlock = buildActionBlock()
                actionsContainerElement.appendChild(actionBlock)
            })
        }

        // Simplified condition block builder
        const buildConditionBlock = () => {
            const block = createElement('div', { class: 'condition-block border p-3 mb-3' })
            
            const header = createElement('div', { class: 'd-flex justify-content-between align-items-center mb-2' })
            
            const select = createElement('select', { class: 'form-select' })
            select.innerHTML = '<option value="">Wybierz warunek...</option>'
            ensureArray(CONDITIONS).forEach(condition => {
                const option = createElement('option', { value: condition.name })
                option.textContent = condition.name
                select.appendChild(option)
            })
            
            const removeBtn = createElement('button', { 
                type: 'button', 
                class: 'btn btn-sm btn-outline-danger' 
            })
            removeBtn.innerHTML = '<i class="fa fa-trash"></i>'
            removeBtn.addEventListener('click', () => block.remove())
            
            header.appendChild(select)
            header.appendChild(removeBtn)
            block.appendChild(header)
            
            return block
        }

        // Simplified action block builder
        const buildActionBlock = () => {
            const block = createElement('div', { class: 'action-block border p-3 mb-3' })
            
            const header = createElement('div', { class: 'd-flex justify-content-between align-items-center mb-2' })
            
            const select = createElement('select', { class: 'form-select' })
            select.innerHTML = '<option value="">Wybierz akcję...</option>'
            ensureArray(ACTIONS).forEach(action => {
                const option = createElement('option', { value: action.name })
                option.textContent = action.name
                select.appendChild(option)
            })
            
            const removeBtn = createElement('button', { 
                type: 'button', 
                class: 'btn btn-sm btn-outline-danger' 
            })
            removeBtn.innerHTML = '<i class="fa fa-trash"></i>'
            removeBtn.addEventListener('click', () => block.remove())
            
            header.appendChild(select)
            header.appendChild(removeBtn)
            block.appendChild(header)
            
            return block
        }

        // Store reference to I18N for use in nested functions
        const I18N = this.I18N

        // Expose RuleForm API globally (for compatibility)
        window.RuleForm = {
            openNew: ({ container, createUrl, defaults = {} }) => {
                initCommon(container)
                
                // Set form values
                container.querySelector('#ruleName').value = defaults.name || 'NoName'
                container.querySelector('#ruleWeight').value = defaults.weight || 0
                container.querySelector('#eventSelect').value = ''
                
                // Clear containers
                container.querySelector('#conditionsContainer').innerHTML = ''
                container.querySelector('#actionsContainer').innerHTML = ''
                
                // Set form action
                container.querySelector('#ruleForm').setAttribute('action', createUrl)
                
                // Set modal title
                const modalTitle = container.closest('.modal-content').querySelector('.modal-title')
                if (modalTitle) {
                    modalTitle.textContent = I18N?.modal?.title?.create || 'Create Rule'
                }
            },
            
            openEdit: ({ container, editUrl, rule }) => {
                initCommon(container)
                
                // Set form values
                container.querySelector('#ruleName').value = rule.name || ''
                container.querySelector('#ruleWeight').value = rule.weight || 0
                container.querySelector('#eventSelect').value = rule.event || ''
                
                // Clear and populate containers
                const conditionsContainer = container.querySelector('#conditionsContainer')
                const actionsContainer = container.querySelector('#actionsContainer')
                
                conditionsContainer.innerHTML = ''
                actionsContainer.innerHTML = ''
                
                // Add existing conditions
                ensureArray(rule.conditions).forEach(() => {
                    conditionsContainer.appendChild(buildConditionBlock())
                })

                // Add existing actions
                ensureArray(rule.actions).forEach(() => {
                    actionsContainer.appendChild(buildActionBlock())
                })
                
                // Set form action
                container.querySelector('#ruleForm').setAttribute('action', editUrl)
                
                // Set modal title
                const modalTitle = container.closest('.modal-content').querySelector('.modal-title')
                if (modalTitle) {
                    modalTitle.textContent = I18N?.modal?.title?.edit || 'Edit Rule'
                }
            }
        }
    }
}
