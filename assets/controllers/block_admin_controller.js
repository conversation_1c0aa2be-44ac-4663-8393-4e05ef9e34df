import { Controller } from '@hotwired/stimulus';

/*
 * Block administration controller for drag & drop functionality
 */
export default class extends Controller {
    static targets = ["availableBlock", "placedBlock", "regionDropzone", "dropPlaceholder"]
    static values = { 
        addUrl: String,
        updateUrl: String,
        removeUrl: String
    }

    connect() {
        this.setupDragAndDrop();
        this.enableSortableRegions();
    }

    setupDragAndDrop() {
        // Setup available blocks
        this.availableBlockTargets.forEach(item => {
            item.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData("block-id", item.dataset.blockId);
            });
        });

        // Setup placed blocks
        this.placedBlockTargets.forEach(item => {
            item.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData("placement-id", item.dataset.placementId);
                e.dataTransfer.setData("block-id", item.dataset.blockId);
                item.classList.add('dragging');
            });

            item.addEventListener('dragend', (e) => {
                item.classList.remove('dragging');
            });
        });

        // Setup region dropzones
        this.regionDropzoneTargets.forEach(zone => {
            zone.addEventListener('dragleave', (e) => {
                if (!zone.contains(e.relatedTarget)) {
                    zone.classList.remove('drag-over');
                }
            });
        });
    }

    enableSortableRegions() {
        this.regionDropzoneTargets.forEach(zone => {
            this.enableSortableRegion(zone);
        });
    }

    enableSortableRegion(regionElement) {
        regionElement.addEventListener('dragover', (e) => {
            e.preventDefault();
            const draggingElement = document.querySelector('.dragging');
            if (!draggingElement) return;

            const afterElement = this.getDragAfterElement(regionElement, e.clientY);
            if (afterElement == null) {
                regionElement.appendChild(draggingElement);
            } else {
                regionElement.insertBefore(draggingElement, afterElement);
            }
        });

        regionElement.addEventListener('drop', (e) => {
            e.preventDefault();
            this.updateRegionWeights(regionElement);
        });
    }

    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.placed-block:not(.dragging)')];

        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;

            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    updateRegionWeights(regionElement) {
        const placedBlocks = regionElement.querySelectorAll('.placed-block');
        const updates = [];

        placedBlocks.forEach((block, index) => {
            const placementId = block.dataset.placementId;
            const newWeight = index * 10; // Use increments of 10

            updates.push({
                placementId: placementId,
                weight: newWeight,
                region: regionElement.dataset.region
            });

            // Update displayed weight
            const weightDisplay = block.querySelector('small');
            if (weightDisplay) {
                weightDisplay.textContent = `Weight: ${newWeight}`;
            }
        });

        this.batchUpdateWeights(updates);
    }

    batchUpdateWeights(updates) {
        const promises = updates.map(update => {
            const url = this.updateUrlValue.replace('__ID__', update.placementId);
            return fetch(url, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    weight: update.weight,
                    region: update.region
                })
            });
        });

        Promise.all(promises)
            .then(responses => {
                console.log('Weights updated successfully');
            })
            .catch(error => {
                console.error('Error updating weights:', error);
            });
    }

    // Action methods for template
    allowDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.add('drag-over');
    }

    dropBlock(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('drag-over');

        const blockId = event.dataTransfer.getData("block-id");
        const placementId = event.dataTransfer.getData("placement-id");
        const targetRegion = event.currentTarget.dataset.region;

        if (blockId && !placementId) {
            // Adding new block from available blocks
            this.addBlockToRegion(blockId, targetRegion);
        } else if (placementId) {
            // Moving existing block between regions
            const sourceRegion = document.querySelector(`[data-placement-id="${placementId}"]`)
                .closest('.region-dropzone').dataset.region;
            if (sourceRegion !== targetRegion) {
                this.moveBlockToRegion(placementId, targetRegion);
            }
        }
    }

    addBlockToRegion(blockId, region) {
        fetch(this.addUrlValue, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                block_id: blockId,
                region: region,
                weight: 0
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        });
    }

    moveBlockToRegion(placementId, region) {
        const url = this.updateUrlValue.replace('__ID__', placementId);
        fetch(url, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                region: region,
                weight: 0
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error moving block');
            }
        });
    }

    removeBlock(event) {
        const placementId = event.params.placementId;
        if (confirm('Are you sure you want to remove this block?')) {
            const url = this.removeUrlValue.replace('__ID__', placementId);
            fetch(url, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error removing block');
                }
            });
        }
    }
}
