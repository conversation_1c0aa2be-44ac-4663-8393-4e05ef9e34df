{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-curl": "*", "ext-iconv": "*", "ext-intl": "*", "ext-libxml": "*", "ext-simplexml": "*", "ext-soap": "*", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.12", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^3.1", "friendsofphp/php-cs-fixer": "^3.68", "knplabs/knp-snappy-bundle": "*", "lexik/jwt-authentication-bundle": "^2.20", "nelmio/cors-bundle": "*", "sca/fedex-api": "dev-master", "sca/infakt": "dev-master", "sca/rules-bundle": "dev-master", "squizlabs/php_codesniffer": "^3.11", "symfony-cmf/routing-bundle": "^3.1", "symfony/asset": "7.2", "symfony/asset-mapper": "7.2", "symfony/console": "7.2", "symfony/doctrine-messenger": "7.2", "symfony/dotenv": "7.2", "symfony/flex": "^2", "symfony/form": "7.2", "symfony/framework-bundle": "7.2", "symfony/http-client": "^7.2", "symfony/lock": "7.2.*", "symfony/mailer": "7.2", "symfony/maker-bundle": "^1.57", "symfony/messenger": "7.2.*", "symfony/monolog-bundle": "^3.10", "symfony/property-info": "7.2", "symfony/rate-limiter": "7.2", "symfony/runtime": "7.2", "symfony/scheduler": "7.2", "symfony/security-bundle": "7.2", "symfony/serializer": "7.2", "symfony/stimulus-bundle": "^2.28", "symfony/translation": "7.2", "symfony/twig-bundle": "7.2", "symfony/uid": "7.2", "symfony/ux-turbo": "^2.28", "symfony/validator": "7.2", "symfony/var-exporter": "7.2", "symfony/yaml": "7.2", "symfonycasts/sass-bundle": "^0.8.2", "symfonycasts/verify-email-bundle": "^1.17", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^3.8"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2", "docker": false}}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^3.5", "symfony/stopwatch": "7.2", "symfony/web-profiler-bundle": "7.2"}, "repositories": [{"type": "git", "url": "**************:us1312/fedex-api.git"}, {"type": "git", "url": "**************:us1312/rules-bundle.git"}, {"type": "git", "url": "**************:us1312/infakt.git"}]}