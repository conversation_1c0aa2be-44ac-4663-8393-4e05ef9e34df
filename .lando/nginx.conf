# Based on https://www.nginx.com/resources/wiki/start/topics/examples/full/#nginx-conf
user              root root;  ## Default: nobody

worker_processes  auto;
error_log         "/opt/bitnami/nginx/logs/error.log";
pid               "/opt/bitnami/nginx/tmp/nginx.pid";

events {
    worker_connections  1024;
}

http {
    include       mime.types;

    default_type  application/octet-stream;

    fastcgi_buffers 16 16k;
    fastcgi_buffer_size 32k;

    client_body_temp_path  "/opt/bitnami/nginx/tmp/client_body" 1 2;
    proxy_temp_path        "/opt/bitnami/nginx/tmp/proxy" 1 2;
    fastcgi_temp_path      "/opt/bitnami/nginx/tmp/fastcgi" 1 2;
    scgi_temp_path         "/opt/bitnami/nginx/tmp/scgi" 1 2;
    uwsgi_temp_path        "/opt/bitnami/nginx/tmp/uwsgi" 1 2;

    log_format    main '$remote_addr - $remote_user [$time_local] '
                       '"$request" $status  $body_bytes_sent "$http_referer" '
                       '"$http_user_agent" "$http_x_forwarded_for"';

    access_log    "/opt/bitnami/nginx/logs/access.log";

    sendfile           on;

    tcp_nopush         on;
    tcp_nodelay        off;

    keepalive_timeout  65;
    gzip               on;
    gzip_http_version  1.0;
    gzip_comp_level    2;
    gzip_proxied       any;
    gzip_types         text/plain text/css application/x-javascript text/xml application/xml application/xml+rss text/javascript;

    ssl_protocols      TLSv1 TLSv1.1 TLSv1.2;

    map $http_x_forwarded_proto $lando_https {
      default '';
      https on;
    }

    map $http_x_forwarded_proto $http_user_agent_https {
      default '';
      https ON;
    }

    client_max_body_size 80M;
    server_tokens off;
    include  "/opt/bitnami/nginx/conf/vhosts/*.conf";
    fastcgi_read_timeout 600;

    # HTTP Server
    server {
        # port to listen on. Can also be set to an IP:PORT
        listen  8080;

        root /app/public;
        index index.php index.html index.htm;

        location / {
            # Obsługa preflight OPTIONS
            if ($request_method = OPTIONS) {
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET,POST,OPTIONS,PUT,DELETE,PATCH' always;
                add_header 'Access-Control-Allow-Headers' 'Authorization,Accept,Origin,DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Content-Range,Range' always;
                add_header 'Access-Control-Max-Age' 1728000 always;
                add_header 'Content-Type' 'text/plain charset=UTF-8' always;
                add_header 'Content-Length' 0 always;
                return 204;
            }

            # Dla normalnych requestów
            add_header 'Access-Control-Allow-Origin' '*' always;
            try_files $uri /index.php$is_args$args;
        }
        location ~ \.php$ {
                fastcgi_pass appserver:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                include fastcgi_params;
        }

        location /status {
            stub_status on;
            access_log   off;
            allow 127.0.0.1;
            deny all;
        }

    }
}
