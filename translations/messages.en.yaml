YES: 'Yes'
NO: 'No'
app.id: 'Application ID'
app.secret: 'Application Key'
api.url: 'API URL'
api.key: 'API Key'
api.login: 'API Login'
api.password: 'API Password'
api.account.number: 'API Account Number'
api.meter.number: 'API Meter Number'
api.secret: 'API Secret'
print.type: 'Print Type'
company.name: 'Company Name'
contact.person: 'Contact Person'
address: 'Address'
postal.code: 'Postal Code'
city: 'City'
country: 'Country'
country.english: 'Country (in English e.g. Poland)'
phone: 'Phone'
phone.international: 'Phone - international format e.g. +48 ***********'
email: 'Email'
pickup.type: 'Pickup Type'
pickup.hours.from: 'Pickup Hours From'
pickup.hours.to: 'Pickup Hours To'
pickup.hours.dhl.from: 'DHL Pickup Hours From'
pickup.hours.dhl.to: 'DHL Pickup Hours To'
pickup.hours.dpd.from: 'DPD Pickup Hours From'
pickup.hours.dpd.to: 'DPD Pickup Hours To'
pickup.hours.fedex.from: 'FedEx Pickup Hours From'
pickup.hours.fedex.to: 'FedEx Pickup Hours To'
pickup.point.number: 'Pickup Point Number'
name: 'Name'
username: 'Username'
password: 'Password'
wsdl.url: 'WSDL URL'
sessions.json: 'JSON Sessions'
organization.id: 'Organization ID'
first.name: 'First Name'
last.name: 'Last Name'
street: 'Street'
house.number: 'House Number'
apartment.number: 'Apartment Number'
eori.number: 'EORI Number'
shipping.payer: 'Additional charges paid by'
shipping.purpose: 'FedEx International - Shipping Purpose'
limit.single.label: 'Limit number of labels to 1 per sub-package'
forward.shipping.costs: 'Forward shipping costs'
incoterms: 'Incoterms'

carrier.create.success: 'Carrier created successfully'
carrier.create.error: 'Error creating carrier'
carrier.update.success: 'Carrier updated successfully'
carrier.update.error: 'Error updating carrier'
carrier.delete.success: 'Carrier deleted successfully'
carrier.delete.error: 'Error deleting carrier'