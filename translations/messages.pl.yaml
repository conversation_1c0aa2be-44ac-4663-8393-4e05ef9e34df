app.id: 'ID aplikacji'
app.secret: 'Klucz aplikacji'
api.url: 'Adres API'
api.key: 'Klucz API'
api.login: 'Login do API'
api.password: 'Hasło do API'
api.account.number: 'Numer konta API'
api.meter.number: 'Numer licznika API'
api.secret: 'API Secret'
print.type: 'Typ wydruku'
company.name: 'Nazwa firmy'
contact.person: 'Osoba kontaktowa'
address: 'Adres'
postal.code: 'Kod pocztowy'
city: 'Miasto'
country: 'Kraj'
country.english: '<PERSON>raj (po angielsku np. Poland)'
phone: 'Telefon'
phone.international: 'Telefon - format międzynarodowy np. +48 ***********'
email: 'email'
pickup.type: 'Typ odbioru'
pickup.hours.from: '<PERSON>ziny odbioru od'
pickup.hours.to: 'Godziny odbioru do'
pickup.hours.dhl.from: '<PERSON><PERSON><PERSON> odbioru DHL od'
pickup.hours.dhl.to: '<PERSON><PERSON><PERSON> odbioru DHL do'
pickup.hours.dpd.from: '<PERSON><PERSON>y odbioru DPD od'
pickup.hours.dpd.to: '<PERSON><PERSON>y odbioru DPD do'
pickup.hours.fedex.from: 'Godziny odbioru FEDEX od'
pickup.hours.fedex.to: 'Godziny odbioru FEDEX do'
pickup.point.number: 'Numer punktu odbioru'
name: 'Nazwa'
username: 'Nazwa użytkownika'
password: 'Hasło'
wsdl.url: 'URL WSDL'
sessions.json: 'Sesje JSON'
organization.id: 'ID organizacji'
first.name: 'Imię'
last.name: 'Nazwisko'
street: 'Ulica'
house.number: 'Numer domu'
apartment.number: 'Numer mieszkania'
eori.number: 'EORI Number'
shipping.payer: 'Dodatkowe opłaty ponosi'
shipping.purpose: 'FedEx zagraniczny - cel wysyłki'
limit.single.label: 'Ogranicz liczbę etykiet do 1 na każdą pod paczkę'
forward.shipping.costs: 'Przekazuj koszty przesyłki'
incoterms: 'FedEx zagraniczny - cel wysyłki'
YES: 'Yes'
NO: 'No'

carrier.create.success: 'Pomyślnie utworzono przewoźnika'
carrier.create.error: 'Błąd tworzenia przewoźnika'
carrier.update.success: 'Pomyślnie zaktualizowano przewoźnika'
carrier.update.error: 'Błąd aktualizacji przewoźnika'
carrier.delete.success: 'Pomyślnie usunięto przewoźnika'
carrier.delete.error: 'Błąd usuwania przewoźnika'