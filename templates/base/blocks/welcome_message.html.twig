<div class="alert alert-{{ message_type }} alert-dismissible fade show" role="alert">
    <div class="d-flex align-items-center">
        {% if message_type == 'success' %}
            <i class="fas fa-check-circle me-3"></i>
        {% elseif message_type == 'warning' %}
            <i class="fas fa-exclamation-triangle me-3"></i>
        {% elseif message_type == 'primary' %}
            <i class="fas fa-info-circle me-3"></i>
        {% else %}
            <i class="fas fa-info-circle me-3"></i>
        {% endif %}
        
        <div class="flex-grow-1">
            {% if custom_message %}
                <div class="fw-bold mb-1">{{ custom_message }}</div>
            {% else %}
                <div class="fw-bold mb-1">
                    {% if user %}
                        Welcome back, {{ user.username }}!
                    {% else %}
                        Welcome to the Dashboard!
                    {% endif %}
                </div>
            {% endif %}
            
            <div class="small">
                {% if show_user_info and user %}
                    <span class="me-3">
                        <i class="fas fa-user me-1"></i>
                        Logged in as {{ user.username }}
                    </span>
                {% endif %}
                
                {% if show_time %}
                    <span>
                        <i class="fas fa-clock me-1"></i>
                        {{ "now"|date("Y-m-d H:i") }}
                    </span>
                {% endif %}
            </div>
        </div>
    </div>
    
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
