<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Edit User: {{ user.username }}</h3>
                <p class="text-muted">Update user information and settings</p>
                <div class="card-tools">
                    <a href="{{ path('admin_users') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> Back to Users
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="post">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control" 
                                       id="username" 
                                       name="username" 
                                       value="{{ user.username }}" 
                                       required>
                                <div class="form-text">Must be unique and at least 3 characters long</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" 
                                       class="form-control" 
                                       id="email" 
                                       name="email" 
                                       value="{{ user.email }}" 
                                       required>
                                <div class="form-text">Must be a valid email address</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" 
                                       class="form-control" 
                                       id="password" 
                                       name="password">
                                <div class="form-text">Leave empty to keep current password</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password_confirm" class="form-label">Confirm New Password</label>
                                <input type="password" 
                                       class="form-control" 
                                       id="password_confirm" 
                                       name="password_confirm">
                                <div class="form-text">Must match the password above if provided</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Roles</label>
                                {% for role_key, role_label in available_roles %}
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="role_{{ role_key }}" 
                                               name="roles[]" 
                                               value="{{ role_key }}"
                                               {% if role_key in user.roles %}checked{% endif %}>
                                        <label class="form-check-label" for="role_{{ role_key }}">
                                            {{ role_label }}
                                            {% if role_key == 'ROLE_ADMIN' %}
                                                <span class="badge bg-danger">Admin</span>
                                            {% elseif role_key == 'ROLE_MODERATOR' %}
                                                <span class="badge bg-warning">Moderator</span>
                                            {% elseif role_key == 'ROLE_EDITOR' %}
                                                <span class="badge bg-info">Editor</span>
                                            {% endif %}
                                        </label>
                                    </div>
                                {% endfor %}
                                <div class="form-text">Select one or more roles for this user</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="enabled" 
                                           name="enabled" 
                                           value="1"
                                           {% if user.enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="enabled">
                                        User is enabled
                                    </label>
                                </div>
                                <div class="form-text">Disabled users cannot log in</div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">User Information</label>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <small class="text-muted">
                                            <strong>User ID:</strong> {{ user.id }}<br>
                                            <strong>Created:</strong> {{ user.createdAt|date('Y-m-d H:i:s') }}<br>
                                            <strong>Last Updated:</strong> {{ user.updatedAt|date('Y-m-d H:i:s') }}<br>
                                            <strong>Last Login:</strong> 
                                            {% if user.lastLogin %}
                                                {{ user.lastLogin|date('Y-m-d H:i:s') }}
                                            {% else %}
                                                Never
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update User
                            </button>
                            <a href="{{ path('admin_users') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('password_confirm').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password && password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

document.getElementById('password').addEventListener('input', function() {
    const confirmPassword = document.getElementById('password_confirm');
    if (confirmPassword.value) {
        confirmPassword.dispatchEvent(new Event('input'));
    }
});
</script>
