<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">User Management</h3>
                <p class="text-muted">Manage system users, roles and permissions</p>
                <div class="card-tools">
                    <a href="{{ path('admin_users_new') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Create New User
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if users is empty %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No users found. Create your first user to get started.
                    </div>
                {% else %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Roles</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                    <tr>
                                        <td>
                                            <strong>{{ user.username }}</strong>
                                        </td>
                                        <td>{{ user.email }}</td>
                                        <td>
                                            {% for role in user.roles %}
                                                {% if role == 'ROLE_ADMIN' %}
                                                    <span class="badge bg-danger">{{ role|replace({'ROLE_': ''})|title }}</span>
                                                {% elseif role == 'ROLE_MODERATOR' %}
                                                    <span class="badge bg-warning">{{ role|replace({'ROLE_': ''})|title }}</span>
                                                {% elseif role == 'ROLE_EDITOR' %}
                                                    <span class="badge bg-info">{{ role|replace({'ROLE_': ''})|title }}</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ role|replace({'ROLE_': ''})|title }}</span>
                                                {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td>
                                            {% if user.enabled %}
                                                <span class="badge bg-success">Active</span>
                                            {% else %}
                                                <span class="badge bg-danger">Disabled</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if user.lastLogin %}
                                                <small class="text-muted">{{ user.lastLogin|date('Y-m-d H:i') }}</small>
                                            {% else %}
                                                <small class="text-muted">Never</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ user.createdAt|date('Y-m-d H:i') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ path('admin_users_edit', {id: user.id}) }}" 
                                                   class="btn btn-outline-primary btn-sm" 
                                                   title="Edit User">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-outline-danger btn-sm" 
                                                        onclick="deleteUser('{{ user.id }}')" 
                                                        title="Delete User">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this user? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete User</button>
            </div>
        </div>
    </div>
</div>

<script>
let userToDelete = null;

function deleteUser(userId) {
    userToDelete = userId;
    new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
}

document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (userToDelete) {
        fetch(window.AssetFlow.settings.userUrls.delete.replace('__ID__', userToDelete), {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
});
</script>
