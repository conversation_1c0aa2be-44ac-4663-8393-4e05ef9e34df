
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Module Management</h3>
                <p class="text-muted">Install, enable, disable and configure system modules</p>
                <div class="card-tools">
                    <a href="{{ path('admin_modules_create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Create New Module
                    </a>
                </div>
            </div>
            <div class="card-body">

                <!-- Enabled Modules -->
                <div class="module-section mb-5">
                    <h4 class="text-success">
                        <i class="fas fa-check-circle"></i> Enabled Modules
                        <span class="badge bg-success">{{ modules_by_status.enabled|length }}</span>
                    </h4>

                    {% if modules_by_status.enabled is empty %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No modules are currently enabled.
                        </div>
                    {% else %}
                        <div class="row">
                            {% for module in modules_by_status.enabled %}
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-success">
                                        <div class="card-body">
                                            <h5 class="card-title text-success">
                                                {{ module.name }}
                                                <span class="badge bg-success">Enabled</span>
                                            </h5>
                                            <p class="card-text">{{ module.description }}</p>
                                            <small class="text-muted">
                                                Version: {{ module.version }}
                                                {% if module.dependencies is not empty %}
                                                    <br>Dependencies: {{ module.dependencies|join(', ') }}
                                                {% endif %}
                                            </small>
                                            <div class="mt-3">
                                                <button class="btn btn-warning btn-sm"
                                                        onclick="disableModule('{{ module.id }}')">
                                                    <i class="fas fa-pause"></i> Disable
                                                </button>
                                                <a href="{{ path('admin_modules_configure', {moduleId: module.id}) }}"
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-cog"></i> Configure
                                                </a>
                                                <button class="btn btn-outline-info btn-sm"
                                                        onclick="showModuleInfo('{{ module.id }}')">
                                                    <i class="fas fa-info"></i> Info
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Disabled Modules -->
                <div class="module-section mb-5">
                    <h4 class="text-warning">
                        <i class="fas fa-pause-circle"></i> Disabled Modules
                        <span class="badge bg-warning">{{ modules_by_status.disabled|length }}</span>
                    </h4>

                    {% if modules_by_status.disabled is empty %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No disabled modules.
                        </div>
                    {% else %}
                        <div class="row">
                            {% for module in modules_by_status.disabled %}
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <h5 class="card-title text-warning">
                                                {{ module.name }}
                                                <span class="badge bg-warning">Disabled</span>
                                            </h5>
                                            <p class="card-text">{{ module.description }}</p>
                                            <small class="text-muted">Version: {{ module.version }}</small>
                                            <div class="mt-3">
                                                <button class="btn btn-success btn-sm"
                                                        onclick="enableModule('{{ module.id }}')">
                                                    <i class="fas fa-play"></i> Enable
                                                </button>
                                                <button class="btn btn-danger btn-sm"
                                                        onclick="uninstallModule('{{ module.id }}')">
                                                    <i class="fas fa-trash"></i> Uninstall
                                                </button>
                                                <button class="btn btn-outline-info btn-sm"
                                                        onclick="showModuleInfo('{{ module.id }}')">
                                                    <i class="fas fa-info"></i> Info
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Not Installed Modules -->
                <div class="module-section">
                    <h4 class="text-secondary">
                        <i class="fas fa-download"></i> Available Modules
                        <span class="badge bg-secondary">{{ modules_by_status.not_installed|length }}</span>
                    </h4>

                    {% if modules_by_status.not_installed is empty %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> All available modules are installed.
                        </div>
                    {% else %}
                        <div class="row">
                            {% for module in modules_by_status.not_installed %}
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-secondary">
                                        <div class="card-body">
                                            <h5 class="card-title text-secondary">
                                                {{ module.name }}
                                                <span class="badge bg-secondary">Not Installed</span>
                                            </h5>
                                            <p class="card-text">{{ module.description }}</p>
                                            <small class="text-muted">Version: {{ module.version }}</small>
                                            <div class="mt-3">
                                                <button class="btn btn-primary btn-sm"
                                                        onclick="installModule('{{ module.id }}')">
                                                    <i class="fas fa-download"></i> Install
                                                </button>
                                                <button class="btn btn-outline-info btn-sm"
                                                        onclick="showModuleInfo('{{ module.id }}')">
                                                    <i class="fas fa-info"></i> Info
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Module Info Modal -->
<div class="modal fade" id="moduleInfoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Module Information</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="moduleInfoContent">
                <!-- Content loaded via AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// Module management functions
function installModule(moduleId) {
    if (confirm('Are you sure you want to install this module?')) {
        fetch(window.AssetFlow.settings.moduleUrls.install.replace('__ID__', moduleId), {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}

function uninstallModule(moduleId) {
    if (confirm('Are you sure you want to uninstall this module? This action cannot be undone.')) {
        fetch(window.AssetFlow.settings.moduleUrls.uninstall.replace('__ID__', moduleId), {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}

function enableModule(moduleId) {
    fetch(window.AssetFlow.settings.moduleUrls.enable.replace('__ID__', moduleId), {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
}

function disableModule(moduleId) {
    if (confirm('Are you sure you want to disable this module?')) {
        fetch(window.AssetFlow.settings.moduleUrls.disable.replace('__ID__', moduleId), {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }
}

function showModuleInfo(moduleId) {
    fetch(`{{ path('admin_modules_info', {moduleId: '__ID__'}) }}`.replace('__ID__', moduleId))
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error: ' + data.error);
            return;
        }

        const content = `
            <h5>${data.name}</h5>
            <p><strong>Description:</strong> ${data.description}</p>
            <p><strong>Version:</strong> ${data.version}</p>
            <p><strong>Module ID:</strong> ${data.id}</p>
            ${data.dependencies.length > 0 ? `<p><strong>Dependencies:</strong> ${data.dependencies.join(', ')}</p>` : ''}
            ${data.hooks.length > 0 ? `<p><strong>Hooks:</strong> ${data.hooks.join(', ')}</p>` : ''}
            <p><strong>Status:</strong>
                ${data.installed ? (data.enabled ? '<span class="badge bg-success">Enabled</span>' : '<span class="badge bg-warning">Disabled</span>') : '<span class="badge bg-secondary">Not Installed</span>'}
            </p>
        `;

        document.getElementById('moduleInfoContent').innerHTML = content;
        new bootstrap.Modal(document.getElementById('moduleInfoModal')).show();
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
}
</script>
