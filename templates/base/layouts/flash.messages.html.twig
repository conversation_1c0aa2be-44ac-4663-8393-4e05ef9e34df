<!-- Flash Messages Styles -->
<style>
    #flash-messages .alert {
        animation: slideInRight 0.3s ease-out;
        border: none;
        border-left: 4px solid;
    }

    #flash-messages .alert-success {
        background-color: #d1e7dd;
        color: #0a3622;
        border-left-color: #198754;
    }

    #flash-messages .alert-danger {
        background-color: #f8d7da;
        color: #58151c;
        border-left-color: #dc3545;
    }

    #flash-messages .alert-warning {
        background-color: #fff3cd;
        color: #664d03;
        border-left-color: #ffc107;
    }

    #flash-messages .alert-info {
        background-color: #cff4fc;
        color: #055160;
        border-left-color: #0dcaf0;
    }

    #flash-messages .alert-primary {
        background-color: #cfe2ff;
        color: #084298;
        border-left-color: #0d6efd;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .alert.fade-out {
        animation: slideOutRight 0.3s ease-in forwards;
    }
</style>
<div id="flash-messages" class="position-fixed top-0 end-0 p-3" style="z-index: 1050; max-width: 400px;">
    {% for label, messages in app.flashes %}
        {% for message in messages %}
            <div class="alert alert-{{ label == 'error' ? 'danger' : (label == 'warning' ? 'warning' : (label == 'success' ? 'success' : (label == 'info' ? 'info' : 'primary'))) }} alert-dismissible fade show mb-2 shadow-sm" role="alert">
                <div class="d-flex align-items-center">
                    {% if label == 'success' %}
                        <i class="fas fa-check-circle me-2"></i>
                    {% elseif label == 'error' %}
                        <i class="fas fa-exclamation-triangle me-2"></i>
                    {% elseif label == 'warning' %}
                        <i class="fas fa-exclamation-circle me-2"></i>
                    {% elseif label == 'info' %}
                        <i class="fas fa-info-circle me-2"></i>
                    {% else %}
                        <i class="fas fa-bell me-2"></i>
                    {% endif %}
                    <div class="flex-grow-1">
                        {{ message }}
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endfor %}
</div>
