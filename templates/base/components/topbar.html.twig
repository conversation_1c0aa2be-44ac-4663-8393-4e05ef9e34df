<div class="d-flex justify-content-between align-items-center h-100">
    <!-- Left side - Page title and breadcrumb -->
    <div class="d-flex align-items-center flex-grow-1">
        <!-- Mobile menu toggle -->
        <button class="mobile-menu-toggle d-md-none me-3"
                data-sidebar-target="toggle"
                data-action="click->sidebar#toggle">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Page title and breadcrumb -->
        <div class="flex-grow-1">
            <h1 class="h4 mb-1 text-dark">
                {{ page_title|default('Dashboard') }}
            </h1>

            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 small">
                    {% if breadcrumbs is defined and breadcrumbs %}
                        {% for breadcrumb in breadcrumbs %}
                            {% if breadcrumb.active|default(false) %}
                                <li class="breadcrumb-item active text-muted" aria-current="page">
                                    {{ breadcrumb.label }}
                                </li>
                            {% else %}
                                <li class="breadcrumb-item">
                                    <a href="{{ breadcrumb.url|default('#') }}" class="text-decoration-none text-muted">
                                        {% if breadcrumb.icon is defined %}
                                            <i class="{{ breadcrumb.icon }}"></i>
                                        {% else %}
                                            {{ breadcrumb.label }}
                                        {% endif %}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}
                    {% else %}
                        <li class="breadcrumb-item">
                            <a href="{{ path('app_homepage') }}" class="text-decoration-none text-muted">
                                <i class="fas fa-home"></i>
                            </a>
                        </li>
                        <li class="breadcrumb-item active text-muted" aria-current="page">
                            {{ page_title|default('Dashboard') }}
                        </li>
                    {% endif %}
                </ol>
            </nav>
        </div>
    </div>

    <!-- Right side - Tools and user menu -->
    <div class="d-flex align-items-center gap-2">
        <!-- Quick actions -->
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary btn-sm" title="Quick Add">
                <i class="fas fa-plus"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" title="Search">
                <i class="fas fa-search"></i>
            </button>
        </div>

        <!-- Notifications -->
        <div class="dropdown">
            <button class="btn btn-outline-secondary btn-sm position-relative" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-bell"></i>
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.6em;">
                    3
                </span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><h6 class="dropdown-header">Notifications</h6></li>
                <li><a class="dropdown-item" href="#"><i class="fas fa-envelope me-2"></i>New message received</a></li>
                <li><a class="dropdown-item" href="#"><i class="fas fa-download me-2"></i>System update available</a></li>
                <li><a class="dropdown-item" href="#"><i class="fas fa-check me-2"></i>Task completed</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-center" href="#">View all notifications</a></li>
            </ul>
        </div>

        <!-- User menu -->
        <div class="dropdown">
            <button class="btn btn-outline-secondary btn-sm d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2"
                     style="width: 24px; height: 24px; font-size: 0.75rem; font-weight: bold;">
                    {{ user and user.username ? user.username|first|upper : 'U' }}
                </div>
                <span class="d-none d-lg-inline small">
                    {{ user and user.username ? user.username : 'User' }}
                </span>
                <i class="fas fa-chevron-down ms-2 small"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><h6 class="dropdown-header">Account</h6></li>
                <li><a class="dropdown-item" href="#">
                    <i class="fas fa-user me-2"></i>Profile
                </a></li>
                <li><a class="dropdown-item" href="#">
                    <i class="fas fa-cog me-2"></i>Settings
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="{{ path('app_logout') }}">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a></li>
            </ul>
        </div>
    </div>
</div>
