<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            width: 100%;
            margin: auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4rem;
            align-items: center;
        }
        .header img {
            max-width: 100%;
            height: auto;
        }
        .invoice__details {
            display: flex;
            justify-content: space-between;
        }
        p {
            margin: 0;
            line-height: 1.3em;
        }
        .invoice-number {
            text-align: center;
            margin-bottom: 20px;
            margin-top: 1.5rem;
        }
        .invoice-number > p {
            line-height: 2;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        thead {
            display: table-header-group; /* Ensures the header repeats on each page */
        }
        tfoot {
            display: table-footer-group;
        }
        table th, table td {
            border: 1px solid black;
            text-align: center;
            padding: 0.25rem 0.1rem;
        }
        table th {
            background-color: #dcdcdc;
            font-weight: 400;
            padding: 0.5rem;
        }
        tr {
            page-break-inside: avoid; /* Prevents row splitting across pages */
        }
        .totals {
            text-align: right;
        }
        .totals p {
            margin: 0;
        }
        .totals .amount {
            font-weight: bold;
        }
        .signatures {
            display: flex;
            justify-content: space-between;
            margin-top: 1.5rem;
        }
        .signatures > div {
            width: 46%;
            text-align: center;
        }
        .signatures .signatures__box {
            border: 1px solid;
            padding: 0.25rem;
            min-height: 8rem;
            margin-bottom: 0.25rem;
        }
        .signatures__header {
            background-color: #dcdcdc;
            padding: 0.25rem 0 0.1rem;
            margin-bottom: 0.25rem;
        }
        .invoice__top-info {
            text-align: center;
            display: flex;
            align-items: flex-end;
            flex-direction: column;
        }
        .invoice__top-info .invoice__container {
            width: 80%;
        }
        .invoice__top-info .invoice__container:not(:last-child) {
            margin-bottom: 0.5rem;
        }
        .invoice__header {
            background: #dcdcdc;
            border-top: 2px solid;
            padding: 0.25rem 0 0.1rem;
            text-align: center;
        }
        .invoice__info {
            padding: 0.25rem 0;
        }
        .invoice__top-logo, .invoice__top-info {
            width: 46%;
        }
        .invoice__top-info .invoice__container:last-child {
            border-bottom: 1px solid;
        }
        .border-bottom {
            border-bottom: 1px solid;
        }
        .invoice__details {
            display: flex;
        }
        .invoice__details > div {
            width: 46%;
        }
        .text-center {
            text-align: center;
        }
        .text-left {
            text-align: left !important;
        }
        .text-right {
            text-align: right !important;
        }
        .vat-table {
            width: 65%;
            margin-left: auto;
        }
        .vat-table td {
            text-align: right;
        }
        .vat-table tr:last-child td {
            border: 0;
        }
        .vat-table tr:last-child td:first-child {
            padding-right: 2rem;
        }
        .invoice__summary {
            width: 50%;
            margin-left: auto;
            border-bottom: 2px solid;
            padding-bottom: 1.5rem;
        }
        .invoice__summary .invoice__header {
            display: flex;
            justify-content: space-between;
            padding: 0.25rem 0.1rem 0.5rem;
        }
        .invoice__summary .invoice__info span {
            margin-right: 2rem;
        }
        .invoice__paid span {
            margin-right: 2rem;
        }
        @media print {
            .page-break {
                page-break-before: always;
            }
        }

        .column__index{
            width: 2ch;
        }

        .column__name{
            width: 40ch;
        }

        .column__ean{
            width: 13ch;
        }

         .column__qty{
            width: 10ch;
        }

         .column__unit{
            width: 5ch;
        }

        .column__priceBrutto{
            width: 10ch;
        }

        .column__vat{
            width: 8ch;
        }

        .column__totalNetto{
            width: 10ch;
        }

        .column__totalVat{
            width: 9ch;
        }

        .column__totalBrutto{
            width: 10ch;
        }

        .footer{
            display: block;
            break-inside: avoid;
            page-break-inside: avoid;
        }

        .invoice__row{
            height: calc(1em * 1.2 * {{pagination.highestLineCount}} +5px);
        }
    </style>
</head>
<body>
<div class="header">
    <div class="invoice__top-logo">
        <img src="https://swiatsupli.pl/img/swiat-supli-logo.svg" alt="Company Logo">
    </div>
    <div class="invoice__top-info">
        <div class="invoice__container">
            <div class="invoice__header">Miejsce wystawienia:</div>
            <div class="invoice__info"> {{ docDate.city }}</div>
        </div>
        <div class="invoice__container">
            <div class="invoice__header">Data sprzedaży:</div>
            <div class="invoice__info">{{ docDate.created.date|date("Y-m-d") }}</div>

        </div>
        <div class="invoice__container">
            <div class="invoice__header">Data wystawienia:</div>
            <div class="invoice__info">{{ docDate.created.date|date("Y-m-d") }}</div>

        </div>
    </div>
</div>

<div class="invoice__details">
    <div class="invoice__container">
        <div class="invoice__header">Sprzedawca:</div>
        <div class="invoice__info border-bottom">
            <p>Świat Supli Paweł Lisowski</p>
            <p>Stoleczna 2/102</p>
            <p>15-879 Białystok</p>
            <p>NIP: **********</p>
            <p>80 1140 2004 0000 3602 5027 7059</p>
            <p>BRE Wydział Bankowości Elektronicznej</p>
        </div>
    </div>
    <div>
        <div class="invoice__header">Nabywca:</div>
        <div class="invoice__info">
            <p>{{ customer.name }}</p>
            <p>{{ customer.address }}</p>
            <p>{{ customer.postCode }} {{ customer.city }}</p>
            <p>NIP: {{ customer.taxId }}</p>
        </div>
    </div>
</div>
<div class="invoice__details">
    <div></div>
    <div>
        <div class="invoice__header">Odbiorca:</div>
        <div class="invoice__info">
            <p>{{ customer.name }}</p>
            <p>{{ customer.address }}</p>
            <p>{{ customer.postCode }} {{ customer.city }}</p>
        </div>
    </div>
</div>

<div class="invoice-number">
    <p>Faktura VAT {{ docRef }} oryginał</p>
    <p>na podstawie zamówienia {{ orderId }} z dnia {{ orderDate }}</p>
</div>
<div class="text-center">{{ orderId }}</div>

<table>
    <thead>
    <tr>
        <th class="column__index">Lp</th>
        <th class="column__name">Nazwa</th>
        <th class="column__ean">Ean</th>
        <th class="column__qty">Ilość</th>
        <th class="column__unit">j.m.</th>
        <th class="column__priceBrutto">Cena brutto</th>
        <th class="column__vat">VAT [%]</th>
        <th class="column__totalNetto">Wartość netto</th>
        <th class="column__totalVat">VAT</th>
        <th class="column__totalBrutto">Wartość brutto</th>
    </tr>
    </thead>
    <tbody>

    {% for product in products %}
    <tr class="invoice__row">
        <td class="column__index">{{ loop.index }}</td>
        <td class="column__name text-left">{{ product.name }}</td>
        <td class="column__ean">{{ product.ean ?? product.code }}</td>
        <td class="text-right column__qty">{{ product.quantity }}</td>
        <td class="column__unit">{{ product.unit }}</td>
        <td class="text-right column__priceBrutto">{{ product.priceBrutto }}</td>
        <td class="column__vat">{{ product.vat }}</td>
        <td class="text-right column__totalNetto">{{ product.totalNetto }}</td>
        <td class="text-right column__totalVat">{{ product.totalVat }}</td>
        <td class="text-right column__totalBrutto">{{ product.totalBrutto }}</td>
    </tr>
   {% if loop.index == pagination.firstPageMaxRows or (loop.index > pagination.firstPageMaxRows and (loop.index - pagination.firstPageMaxRows) % pagination.subsequentPagesMaxRows == 0) and loop.index < loop.length %}



    </tbody>
</table>
<div class="page-break"></div>
<table>
    <thead>
   <tr>
        <th class="column__index">Lp</th>
        <th class="column__name">Nazwa</th>
        <th class="column__ean">Ean</th>
        <th class="column__qty">Ilość</th>
        <th class="column__unit">j.m.</th>
        <th class="column__priceBrutto">Cena brutto</th>
        <th class="column__vat">VAT [%]</th>
        <th class="column__totalNetto">Wartość netto</th>
        <th class="column__totalVat">VAT</th>
        <th class="column__totalBrutto">Wartość brutto</th>
    </tr>
    </thead>
    <tbody>
    {% endif %}
    {% endfor %}
    </tbody>
</table>
<div class="footer">
<table class="vat-table">
    <thead>
    <tr>
        <th>według stawki VAT</th>
        <th>wartość netto</th>
        <th>kwota VAT</th>
        <th>wartość brutto</th>
    </tr>
    </thead>
    <tbody>
    {% for vatRate, vatAmount in amount.vatBreakdown %}
        <tr>
            <td class="text-left">Podatek VAT {{ vatRate }}%</td>
            <td>{{ vatAmount.netAmount }}</td>
            <td>{{ vatAmount.vatAmount }}</td>
            <td>{{ vatAmount.grossAmount }}</td>
        </tr>
    {% endfor %}
    <tr>
        <td class="padding-right">Razem:</td>
        <td class="text-right">{{ amount.netto }}</td>
        <td class="text-right">{{ amount.totalVat }}</td>
        <td class="text-right">{{ amount.brutto }}</td>
    </tr>
    </tbody>
</table>

<div class="invoice__summary">
    <div class="invoice__header">
        <div>Razem do zapłaty:</div>
        <div>{{amount.brutto}}</div>
    </div>
    <div class="invoice__info"><span>Słownie:</span> {{ amount.inWords }}</div>
</div>

{#<div class="invoice__paid">#}
{#    <span>Zapłacono przelewem:</span>WSTAW ILE ZAPŁACONO#}
{#</div>#}

<div class="signatures">
    <div>
        <div class="signatures__box">
            <div class="signatures__header">Wystawił(a):</div>
            <div>SUPEL API</div>
        </div>
        <div>Podpis osoby upoważnionej do wystawienia faktury VAT</div>
    </div>
    <div>
        <div class="signatures__box">
            <div class="signatures__header">Odebrał(a):</div>
            <div>{{ customer.name }}</div>
        </div>
        <div>Podpis osoby upoważnionej do odbioru faktury VAT</div>
    </div>
</div>
</div>
</body>
</html>
