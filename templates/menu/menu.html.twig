{% set currentPath = app.request.pathinfo %}
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container-fluid">
        <img src="{{ asset('images/logo.png') }}" alt="logo" width="50px">
        <a class="navbar-brand" href="#">Initace BWMS</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNavDropdown">
            <ul class="navbar-nav me-auto">
                {% for item in links %}
                    {% if item.children is defined and item.children is not empty %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="menuDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                {{ item.name }}

                            </a>
                            <ul class="dropdown-menu" aria-labelledby="menuDropdown">
                                {% for child in item.children %}
                                    <li><a class="dropdown-item" href="{{ child.url }}">{{ child.name }}</a></li>
                                {% endfor %}
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-link "><a href="{{ item.url }}" class="{{ item.url == currentPath ? 'active' : '' }} nav-link">{{ item.name }}</a></li>
                    {% endif %}
                {% endfor %}
            </ul>
        </div>
    </div>
</nav>