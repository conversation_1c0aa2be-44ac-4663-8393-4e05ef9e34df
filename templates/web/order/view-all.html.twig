{% block content %}
    <div id="content-container" class="container text-center">
        <div class="container">
            {% if paginator.pages > 1 %}
                <div class="paginator">
                    {% if paginator.currentPage > 1 %}
                        <a href="{{ path('web_orders_list', { page: 1 }) }}">« Pierwsza</a>
                        <a href="{{ path('web_orders_list', { page: paginator.currentPage - 1}) }}">‹ Poprzednia</a>
                    {% endif %}

                    {% for i in 1..paginator.totalPages %}
                        {% if i == paginator.currentPage %}
                            <strong>[{{ i }}]</strong>
                        {% elseif i <= paginator.currentPage + 2 and i >= paginator.currentPage - 2 %}
                            <a href="{{ path('web_orders_list', { page: i }) }}">{{ i }}</a>
                        {% endif %}
                    {% endfor %}

                    {% if paginator.currentPage < paginator.totalPages %}
                        <a href="{{ path('web_orders_list', {page: paginator.currentPage + 1}) }}">Następna ›</a>
                        <a href="{{ path('web_orders_list', {page: paginator.totalPages}) }}">Ostatnia »</a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
        <div class="mb-3 p-3 bg-secondary">
            <form method="GET">
            <label for="searchOrder">Szukaj zamówienia</label>
            <input id="searchOrder" type="text" class="form-control" placeholder="Order UUID lub Order Shop ID" name="searchOrderId">
            <a href="{{ path('web_orders_list') }}"><button type="submit" class="btn btn-primary my-3">Szukaj</button></a>
            </form>
        </div>
        <table class="table table-dark table-hover table-striped">
            <thead>
                <tr>
                    <th scope="col">ID</th>
                    <th scope="col">UUID</th>
                    <th scope="col">SHOP ID</th>
                    <th scope="col">PRICE WITH DISCOUNT</th>
                    <th scope="col">PRICE WITH DELIVERY</th>
                    <th scope="col">DATE ADD</th>
                    <th scope="col">DATE STATUS</th>
                    <th scope="col">FLAG</th>
                    <th scope="col">HISTORY LOG</th>
                </tr>
            </thead>
            <tbody>
            {% for key, order in orders %}
                <tr>
                    <td>{{ key }}</td>
                    <td>{{ order.id }}</td>
                    <td>{{ order.getShopOrderId }}</td>
                    <td>{{ order.getFullPriceWithDiscount / 100 }}</td>
                    <td>{{ order.getFullPriceWithDelivery / 100}}</td>
                    <td>{{ order.getDateAdd|date('Y-m-d H:i:s') }}</td>
                    <td>{{ order.getDateInStatus|date('Y-m-d H:i:s') }}</td>
                    {% if order.getMarkFlag %}
                        <td style="background-color: {{ order.getMarkFlag.getFlagColor }}">{{ order.getMarkFlag.getFlagName }}</td>
                    {% else %}
                        <td></td>
                    {% endif %}
                    <td><a href="{{ path('web_orders_view_by_id', {id: order.id})}}">Historia edycji {{ order.getHistory.count }}</a></td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
{% endblock %}