{% block content %}
    <div id="content-container" class="container text-center">
        <h3 class="center">{{ order.id }} - {{ order.shopOrderId }}</h3>
        <table class="table table-success table-striped">
            <thead>
                <tr>
                    <th scope="col">ID</th>
                    <th scope="col">Data</th>
                    <th scope="col">Date</th>
                    <th scope="col">User</th>
                </tr>
            </thead>
            <tbody>
            {% for key, history in order.getHistory %}
                <tr>
                    <td>
                        {{ key }}
                    </td>
                    <td>
                    {% for fieldName, fieldValue in history.getData %}
                        {% if fieldValue.newValue is defined %}
                            <ul class="field-group" style="list-style: none;">
                                <li class="fw-bold">{{ fieldName }}</li>
                                {% if fieldName in ['status_date', 'date_add', 'date_confirmed', 'date_in_status'] %}
                                    <li>{{ fieldValue.oldValue|date }} => {{ fieldValue.newValue|date }}</li>
                                {% elseif fieldName is same as('internal_status_id') %}
                                    <li>
                                        <ul class="list-group">
                                            <li class="list-group-item">wartość:</li>
                                            <li class="list-group-item">{{ fieldValue.oldValue.id }} - {{ fieldValue.oldValue.name }}</li>
                                            <li class="list-group-item">{{ fieldValue.newValue.id }} -  {{ fieldValue.newValue.name }}</li>
                                        </ul>
                                    </li>
                                {% else %}
                                    <li class="list-group-item">{{ fieldValue.oldValue }} => {{ fieldValue.newValue }}</li>
                                {% endif %}
                            </ul>
                        {% else %}
                            {% for element in fieldValue %}
                                <ul class="field-group" style="list-style: none;">
                                    <li class="fw-bold">{{ fieldName }}</li>
                                    {% if fieldName in ['status_date', 'date_add', 'date_confirmed', 'date_in_status'] %}
                                        <li>{{ element.oldValue|date }} => {{ element.newValue|date }}</li>
                                    {% elseif fieldName is same as('internal_status_id') %}
                                        <li>
                                            <ul class="list-group">
                                                <li class="list-group-item">wartość:</li>
                                                <li class="list-group-item">{{ element.oldValue.id }} - {{ element.oldValue.name }}</li>
                                                <li class="list-group-item">{{ element.newValue.id }} -  {{ element.newValue.name }}</li>
                                            </ul>
                                        </li>
                                    {% else %}
                                        <li class="list-group-item">{{ element.oldValue }} => {{ element.newValue }}</li>
                                    {% endif %}
                                </ul>
                            {% endfor %}
                        {% endif %}
                    {% endfor %}
                    </td>
                    <td>{{ history.getDateOfChange|date('Y-m-d H:i:s') }}</td>
                    <td>{{ history.getUser.getUsername }}</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
{% endblock %}