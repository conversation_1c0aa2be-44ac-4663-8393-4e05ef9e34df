{% block title %}User index{% endblock %}

{% block content %}
    <h1>User index</h1>

    <table class="table">
        <thead>
            <tr>
                <th>Id</th>
                <th>Email</th>
                <th>Roles</th>
                <th>Password</th>
                <th>IsVerified</th>
                <th>actions</th>
            </tr>
        </thead>
        <tbody>
        {% for user in users %}
            <tr>
                <td>{{ user.id }}</td>
                <td>{{ user.email }}</td>
                <td>{{ user.roles ? user.roles|json_encode : '' }}</td>
                <td>{{ user.password }}</td>
                <td>{{ user.isVerified ? 'Yes' : 'No' }}</td>
                <td>
{#                    <a href="{{ path('app_user_show', {'id': user.id}) }}">show</a>#}
{#                    <a href="{{ path('app_user_edit', {'id': user.id}) }}">edit</a>#}
                </td>
            </tr>
        {% else %}
            <tr>
                <td colspan="6">no records found</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

{#    <a href="{{ path('app_user_new') }}">Create new</a>#}
{% endblock %}
