{% extends 'base.html.twig' %}

{% block title %}User{% endblock %}
{% block content %}
    <h1>User</h1>

    <table class="table">
        <tbody>
            <tr>
                <th>Id</th>
                <td>{{ user.id }}</td>
            </tr>
            <tr>
                <th>Email</th>
                <td>{{ user.email }}</td>
            </tr>
            <tr>
                <th>Roles</th>
                <td>{{ user.roles ? user.roles|json_encode : '' }}</td>
            </tr>
            <tr>
                <th>Password</th>
                <td>{{ user.password }}</td>
            </tr>
            <tr>
                <th>IsVerified</th>
                <td>{{ user.isVerified ? 'Yes' : 'No' }}</td>
            </tr>
        </tbody>
    </table>

    <a href="{{ path('app_user_index') }}">back to list</a>

    <a href="{{ path('app_user_edit', {'id': user.id}) }}">edit</a>

    {{ include('user/_delete_form.html.twig') }}
{% endblock %}
