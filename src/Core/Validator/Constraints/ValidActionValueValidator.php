<?php

namespace App\Core\Validator\Constraints;

use App\Core\Rules\RuleService;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class ValidActionV<PERSON>ueValidator extends ConstraintValidator {

    public function __construct(private RuleService $ruleService) {}

    public function validate(mixed $value, Constraint $constraint) {
        if (!$constraint instanceof ValidActionValue) {
            throw new UnexpectedTypeException($constraint, ValidActionValue::class);
        }

        foreach ($value as $actionSent) {
            if (!$this->ruleService->getRuleActionByName($actionSent['name'])) {
                $this->context->buildViolation($constraint->message)
                    ->setParameter('{{ type }}', $actionSent['name'])
                    ->addViolation();
            }
        }
    }
}