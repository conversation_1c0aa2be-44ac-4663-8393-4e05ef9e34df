<?php

namespace App\Core\Validator\Constraints;

use Symfony\Component\Validator\Constraint;

#[\Attribute(\Attribute::TARGET_PROPERTY | \Attribute::IS_REPEATABLE)]
class ValidActionValue extends Constraint {

    public string $message = 'Invalid condition value for type {{ type }}';

    public function __construct(
        public ?string $nameField = 'name',
        string $message = null,
        array $groups = null,
        mixed $payload = null
    ) {
        parent::__construct([], $groups, $payload);
        if ($message) {
            $this->message = $message;
        }
    }

}