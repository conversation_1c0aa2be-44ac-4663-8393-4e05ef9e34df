<?php

namespace App\Core\Validator\Constraints;

use App\Core\Rules\RuleService;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class ValidConditionValueValidator extends ConstraintValidator {

    public function __construct(private RuleService $ruleService) {}

    public function validate(mixed $value, Constraint $constraint) {
        if (!$constraint instanceof ValidConditionValue) {
            throw new UnexpectedTypeException($constraint, ValidConditionValue::class);
        }

        if (!is_array($value)) {
            return $this->buildViolation($constraint->message, $value);
        }
    }

    private function buildViolation(string $message, mixed $value) {
        return $this->context->buildViolation($message)
            ->setParameter('{{ type }}', $value);
    }
}