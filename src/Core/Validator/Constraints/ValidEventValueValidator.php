<?php

namespace App\Core\Validator\Constraints;

use App\Core\Rules\RuleService;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class ValidEventValueValidator extends ConstraintValidator {

    public function __construct(private RuleService $ruleService) {}

    public function validate(mixed $value, Constraint $constraint) {
        if (!$constraint instanceof ValidEventValue) {
            throw new UnexpectedTypeException($constraint, ValidEventValue::class);
        }
        if (!$this->ruleService->getRuleEventByName($value)) {
            $this->context->buildViolation($constraint->message)
                ->setParameter('{{ type }}', $value)
                ->addViolation();
        }
    }
}