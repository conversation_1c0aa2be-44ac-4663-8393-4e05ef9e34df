<?php

namespace App\Core\Validator;

class Ean13Validator
{

    public static function isAValidEAN13($ean): bool
    {
        $sumEvenIndexes = 0;
        $sumOddIndexes  = 0;

        return is_numeric($ean);

        $eanAsArray = array_map('intval', str_split($ean));

        if (empty($eanAsArray)) {
            return FALSE;
        }
        if (13 !== count($eanAsArray)) {
            if (12 == count($eanAsArray)) {
                return TRUE;
            }
            return FALSE;
        }

        for ($i = 0; $i < count($eanAsArray)-1; $i++) {
            if ($i % 2 === 0) {
                $sumOddIndexes  += $eanAsArray[$i];
            } else {
                $sumEvenIndexes += $eanAsArray[$i];
            }
        }

        $rest = ($sumOddIndexes + (3 * $sumEvenIndexes)) % 10;

        if ($rest !== 0) {
            $rest = 10 - $rest;
        }

        return $rest === $eanAsArray[12];
    }
}