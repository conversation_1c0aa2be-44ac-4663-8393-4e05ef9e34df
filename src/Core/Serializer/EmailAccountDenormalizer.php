<?php

namespace App\Core\Serializer;

use App\Core\Entity\EmailAccount;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use S<PERSON>fony\Component\Serializer\SerializerAwareTrait;
use Symfony\Component\Serializer\SerializerInterface;

class EmailAccountDenormalizer implements DenormalizerAwareInterface, DenormalizerInterface {

    use SerializerAwareTrait;
    use DenormalizerAwareTrait;

    private const ALREADY_CALLED = 'EMAIL_ACCOUNT_DENORMALIZER_ALREADY_CALLED';

    public function getSupportedTypes(?string $format): array
    {
        return [EmailAccount::class => FALSE];
    }

    public function setSerializer(SerializerInterface $serializer): void
    {
        $this->serializer = $serializer;
    }

    public function setDenormalizer(DenormalizerInterface $denormalizer): void
    {
        $this->denormalizer = $denormalizer;
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return EmailAccount::class === $type;
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        if (is_array($data)) {
            if (isset($data['smtp_port'])) {
                $data['smtp_port'] = (int) $data['smtp_port'];
            }
        }
        if (is_object($data) && $data instanceof EmailAccount) {
            return $data;
        }

        $context[self::ALREADY_CALLED] = true;

        return $this->denormalizer->denormalize($data, $type, $format, $context);
    }

}