<?php

namespace App\Core\Serializer;

use App\Core\Entity\Document;
use App\Core\Entity\Order;
use S<PERSON><PERSON>ny\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use S<PERSON>fony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use S<PERSON>fony\Component\Serializer\Normalizer\DenormalizerInterface;
use S<PERSON>fony\Component\Serializer\SerializerAwareTrait;
use S<PERSON>fony\Component\Serializer\SerializerInterface;

class DocumentDenormalizer implements DenormalizerAwareInterface, DenormalizerInterface {

    use SerializerAwareTrait;
    use DenormalizerAwareTrait;

    private const ALREADY_CALLED = 'DOCUMENT_DENORMALIZER_ALREADY_CALLED';

    public function getSupportedTypes(?string $format): array
    {
        return [Document::class => FALSE];
    }

    public function setSerializer(SerializerInterface $serializer): void
    {
        $this->serializer = $serializer;
    }

    public function setDenormalizer(DenormalizerInterface $denormalizer): void
    {
        $this->denormalizer = $denormalizer;
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }
        return Document::class === $type;
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        $data['created_at'] = new \DateTimeImmutable();
        $data['updated_at'] = new \DateTimeImmutable();
        $context[self::ALREADY_CALLED] = true;

        return $this->denormalizer->denormalize($data, $type, $format, $context);
    }

}