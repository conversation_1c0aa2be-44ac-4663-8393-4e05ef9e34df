<?php

namespace App\Core\Serializer;

use App\Core\DTO\Rule\RuleDTO;
use Doctrine\ORM\EntityManagerInterface;
use SCA\Rules\Entity\Rule;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerAwareTrait;

class RuleNormalizer implements NormalizerInterface, DenormalizerAwareInterface, DenormalizerInterface {
    const ALREADY_CALLED_DENORMALIZER = 'RULE_DATA_DENORMALIZER_ALREADY_CALLED';
    const ALREADY_CALLED_NORMALIZER = 'RULE_DATA_NORMALIZER_ALREADY_CALLED';

    use SerializerAwareTrait;
    use DenormalizerAwareTrait;
    public function __construct(
        #[Autowire(service: 'serializer.normalizer.object')]
        private NormalizerInterface $normalizer,
        private readonly EntityManagerInterface $entityManager
    ){}

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed {
        $context[self::ALREADY_CALLED_DENORMALIZER] = true;
        if ($data instanceof RuleDTO) {
            return $this->handleDenormalizeFromDTO($data, $type, $format, $context);
        }
        return $this->denormalizer->denormalize($data, $type, $format, $context);
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool {
        if (isset($context[self::ALREADY_CALLED_DENORMALIZER]) && $context[self::ALREADY_CALLED_DENORMALIZER]) {
            return false;
        }

        return $type === Rule::class;
    }

    public function normalize(mixed $data, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null {
        $context[self::ALREADY_CALLED_NORMALIZER] = true;
        return $this->normalizer->normalize($data, $format, $context);
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED_NORMALIZER]) && $context[self::ALREADY_CALLED_NORMALIZER]) {
            return false;
        }

        return $data instanceof Rule;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            Rule::class => false,
        ];
    }

    private function handleDenormalizeFromDTO(mixed $data, string $type, ?string $format = null, array $context = []): Rule {
        if (isset($context['object_to_populate']) && $context['object_to_populate'] instanceof Rule) {
            $objectToPopulate = $context['object_to_populate'];
            $objectToPopulate->setName($data->getName());
            $objectToPopulate->setEvent($data->getEvent());
            $objectToPopulate->setConditions($data->getConditions());
            $objectToPopulate->setActions($data->getActions());
            $objectToPopulate->setWeight($data->getWeight());

            return $objectToPopulate;
        }
        return new Rule($data->getName(), $data->getEvent(), $data->getConditions(),$data->getActions(), [], $data->getWeight());
    }
}