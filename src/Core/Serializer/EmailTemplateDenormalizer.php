<?php

namespace App\Core\Serializer;

use App\Core\Entity\EmailAccount;
use App\Core\Entity\EmailTemplate;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use S<PERSON>fony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use S<PERSON>fony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\SerializerAwareTrait;
use Symfony\Component\Serializer\SerializerInterface;

class EmailTemplateDenormalizer implements DenormalizerAwareInterface, DenormalizerInterface {

    use SerializerAwareTrait;
    use DenormalizerAwareTrait;

    private const ALREADY_CALLED = 'EMAIL_TEMPLATE_DENORMALIZER_ALREADY_CALLED';

    public function __construct(private readonly EntityManagerInterface $entityManager)
    {
    }

    public function getSupportedTypes(?string $format): array
    {
        return [EmailTemplate::class => FALSE];
    }

    public function setSerializer(SerializerInterface $serializer): void
    {
        $this->serializer = $serializer;
    }

    public function setDenormalizer(DenormalizerInterface $denormalizer): void
    {
        $this->denormalizer = $denormalizer;
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }
        return EmailTemplate::class === $type;
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        if (is_object($data) && $data instanceof EmailTemplate) {
            return $data;
        }
        if (isset($data['emailAccount'])) {
            $emailAccount = $this->entityManager->getRepository(EmailAccount::class)->find($data['emailAccount']);
            $data['emailAccount'] = $emailAccount;
        }
        $context[self::ALREADY_CALLED] = true;

        return $this->denormalizer->denormalize($data, $type, $format, $context);
    }

}