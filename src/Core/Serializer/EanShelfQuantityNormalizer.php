<?php

namespace App\Core\Serializer;

use App\Core\Entity\EanShelfQuantity;
use S<PERSON>fony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

readonly class EanShelfQuantityNormalizer implements NormalizerInterface {

    public function __construct(
        #[Autowire(service: 'serializer.normalizer.object')]
        private NormalizerInterface $normalizer
    ){}

    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        $context[AbstractNormalizer::CIRCULAR_REFERENCE_HANDLER] = function($object, ?string $format, array $context): mixed {
            return $object->getId();
        };
        return $this->normalizer->normalize($object, $format, $context);
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof EanShelfQuantity;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            EanShelfQuantity::class => true
        ];
    }
}