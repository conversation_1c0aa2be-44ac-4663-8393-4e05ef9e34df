<?php

namespace App\Core\Serializer;

use App\Core\Entity\Order;
use App\Core\Entity\OrderFlag;
use App\Core\Entity\OrderStatus;
use App\Core\Taxonomy\DateTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\SerializerAwareTrait;
use Symfony\Component\Serializer\SerializerInterface;

class OrderDenormalizer implements DenormalizerAwareInterface, DenormalizerInterface
{

    use SerializerAwareTrait;
    use DenormalizerAwareTrait;

    public function __construct(private readonly EntityManagerInterface $entityManager) {}
    private const ALREADY_CALLED = 'ORDER_DENORMALIZER_ALREADY_CALLED';

    public function getSupportedTypes(?string $format): array
    {
        return [Order::class => FALSE];
    }

    public function setSerializer(SerializerInterface $serializer): void
    {
        $this->serializer = $serializer;
    }

    public function setDenormalizer(DenormalizerInterface $denormalizer): void
    {
        $this->denormalizer = $denormalizer;
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }
        return Order::class === $type;
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        if (isset($data['order_id'])) {
            $data['order_id'] = (string)$data['order_id'];
        }
        if (isset($data['phone'])) {
            //@TODO Make a phone number field normalizer
            $data['phone'] = str_replace([' ', '+48', '-'], '', trim($data['phone']));
        }
        if (isset($data['dateInStatus'])) {
            $data['dateInStatus'] = $this->formatDate($data['dateInStatus']);
        }
        if (isset($data['dateAdd'])) {
            $data['dateAdd'] = $this->formatDate($data['dateAdd']);
        }
        if (isset($data['dateConfirmed'])) {
            $data['dateConfirmed'] = $this->formatDate($data['dateConfirmed']);
        }
        if (isset($data['internalStatusId'])) {
            $data['internalStatusId'] = $this->entityManager->getRepository(OrderStatus::class)->find($data['internalStatusId']);
        }
        if (isset($data['markFlag'])) {
            $data['markFlag'] = $this->entityManager->getRepository(OrderFlag::class)->find($data['markFlag']);
        }
        $context[self::ALREADY_CALLED] = true;

        return $this->denormalizer->denormalize($data, $type, $format, $context);
    }

    private function formatDate($date): string
    {
        if (is_numeric($date) && (int) $date == $date && $date <= PHP_INT_MAX && $date >= ~PHP_INT_MAX) {
            return date(DateTaxonomy::DATE_FORMAT, (int) $date);
        }

        return $date;
    }
}