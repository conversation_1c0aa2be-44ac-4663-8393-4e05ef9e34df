<?php

namespace App\Core\Serializer;

use App\Core\Entity\OrderStatus;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use S<PERSON><PERSON>ny\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerAwareTrait;
use Symfony\Component\Uid\Uuid;

class OrderStatusNormalizer implements NormalizerInterface, DenormalizerAwareInterface, DenormalizerInterface {

    use SerializerAwareTrait;
    use DenormalizerAwareTrait;

    private const ALREADY_CALLED = 'ORDER_STATUS_DENORMALIZER_ALREADY_CALLED';

    public function __construct(
        #[Autowire(service: 'serializer.normalizer.object')]
        private NormalizerInterface $normalizer,
        private readonly EntityManagerInterface $em
    ){}

    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        $context[AbstractObjectNormalizer::CIRCULAR_REFERENCE_HANDLER] = function($object, ?string $format, array $context): mixed {
            return $object->getId();
        };
        return $this->normalizer->normalize($object, $format, $context);
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof OrderStatus;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            OrderStatus::class => true
        ];
    }

    public function setDenormalizer(DenormalizerInterface $denormalizer): void
    {
        $this->denormalizer = $denormalizer;
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        $context[self::ALREADY_CALLED] = true;

        if ($data instanceof OrderStatus) {
            return $data;
        }

        if (is_string($data) && Uuid::isValid($data)) {
            return $this->em->getRepository(OrderStatus::class)->find($data);
        }

        return $this->denormalizer->denormalize($data, $type, $format, $context);

    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        return OrderStatus::class === $type && $data instanceof OrderStatus;
    }
}