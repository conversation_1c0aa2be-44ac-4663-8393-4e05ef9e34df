<?php

namespace App\Core\Serializer;

use App\Core\Entity\OrderStatus;
use App\Core\Entity\OrderStatusTab;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use S<PERSON>fony\Component\Serializer\Normalizer\NormalizerInterface;

readonly class OrderStatusTabNormalizer implements NormalizerInterface {

    public function __construct(
        #[Autowire(service: 'serializer.normalizer.object')]
        private NormalizerInterface $normalizer
    ){}

    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        $context[AbstractObjectNormalizer::CIRCULAR_REFERENCE_HANDLER] = function($object, ?string $format, array $context): mixed {
            return $object->getId();
        };
        return $this->normalizer->normalize($object, $format, $context);
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof OrderStatusTab;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            OrderStatusTab::class => true
        ];
    }
}