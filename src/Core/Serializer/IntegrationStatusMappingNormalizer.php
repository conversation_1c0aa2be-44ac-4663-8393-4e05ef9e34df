<?php

namespace App\Core\Serializer;

use App\Core\DTO\IntegrationDataDTO;
use App\Core\DTO\IntegrationStatusMappingDTO;
use App\Core\Entity\Integration;
use App\Core\Entity\IntegrationData;
use App\Core\Entity\IntegrationStatusMapping;
use App\Core\Entity\OrderStatus;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerAwareTrait;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Uid\Uuid;

final class IntegrationStatusMappingNormalizer implements NormalizerInterface, DenormalizerAwareInterface, DenormalizerInterface {

    const ALREADY_CALLED_DENORMALIZER = 'INTEGRATION_STATUS_MAPPING_DENORMALIZER_ALREADY_CALLED';
    const ALREADY_CALLED_NORMALIZER = 'INTEGRATION_STATUS_MAPPING_NORMALIZER_ALREADY_CALLED';

    use SerializerAwareTrait;
    use DenormalizerAwareTrait;
    public function __construct(
        #[Autowire(service: 'serializer.normalizer.object')]
        private NormalizerInterface $normalizer,
        private readonly EntityManagerInterface $entityManager
    ){}

    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        $context[AbstractObjectNormalizer::CIRCULAR_REFERENCE_HANDLER] = function($object, string $format, array $context): mixed {
            return $object->getId();
        };

        $DTO = new IntegrationStatusMappingDTO(
            $object->getId()->toRfc4122(),
            $object->getIntegrationData()->getId()->toRfc4122(),
            $object->getOrderStatus()?->getId()->toRfc4122() ?? null,
            $object->getIntegrationStatusId(),
            $object->getIntegrationStatusName(),
            $object->isPaid(),
            $object->isShipped(),
            $object->isDelivered()
    );

        return $this->normalizer->normalize($DTO, $format, $context);
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED_NORMALIZER]) && $context[self::ALREADY_CALLED_NORMALIZER]) {
            return false;
        }

        return $data instanceof IntegrationStatusMapping;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            IntegrationStatusMapping::class => false,
        ];
    }

    public function setDenormalizer(DenormalizerInterface $denormalizer): void
    {
        $this->denormalizer = $denormalizer;
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        if (is_array($data)) {
            return $this->handleArrayDenormalization($data, $type, $format, $context);
        }

        $context[self::ALREADY_CALLED_DENORMALIZER] = true;
        return $this->denormalizer->denormalize($data, $type, $format, $context);
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED_DENORMALIZER]) && $context[self::ALREADY_CALLED_DENORMALIZER]) {
            return false;
        }

        return $type === IntegrationStatusMapping::class;
    }

    private function handleArrayDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): mixed {
        if (isset($data['id']) && Uuid::isValid($data['id'])) {
            if ($entity = $this->entityManager->getRepository(IntegrationStatusMapping::class)->find($data['id'])) {
                $context[AbstractNormalizer::OBJECT_TO_POPULATE] =  $entity;
                if (isset($data['integrationStatusId'])) {
                    $data['integrationStatusId'] = (string) $data['integrationStatusId'];
                }
                $context[self::ALREADY_CALLED_DENORMALIZER] = true;
                return $this->denormalizer->denormalize($data, $type, $format, $context);
            }
        }
        if (isset($data['fetchStatus']) && !empty($data['fetchStatus']) && is_string($data['fetchStatus']) && Uuid::isValid($data['fetchStatus'])) {
            $status = $this->entityManager->getRepository(OrderStatus::class)->find($data['fetchStatus']);
            $data['fetchStatus'] = $status;
        }
        if (isset($data['cancelStatus']) && !empty($data['cancelStatus']) && is_string($data['cancelStatus']) && Uuid::isValid($data['cancelStatus'])) {
            $status = $this->entityManager->getRepository(OrderStatus::class)->find($data['cancelStatus']);
            $data['cancelStatus'] = $status;
        }

        return $data;
    }

    public function setSerializer(SerializerInterface $serializer): void {
        $this->serializer = $serializer;
    }

    public function serialize($data, string $format = null, array $context = []): string {
        return $this->serializer->serialize($data, $format, $context);
    }
}