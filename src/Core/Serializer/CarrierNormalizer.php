<?php

namespace App\Core\Serializer;

use App\Core\Entity\Carrier;
use App\Core\Entity\CarrierSettings;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use S<PERSON>fony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerAwareTrait;

class CarrierNormalizer implements NormalizerInterface, DenormalizerAwareInterface, DenormalizerInterface {
    
    use SerializerAwareTrait;
    use DenormalizerAwareTrait;
    
    private const ALREADY_CALLED = 'CARRIER_DENORMALIZER_ALREADY_CALLED';
    public function __construct(
        #[Autowire(service: 'serializer.normalizer.object')]
        private readonly NormalizerInterface $normalizer
    ){}

    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        $context[AbstractObjectNormalizer::CIRCULAR_REFERENCE_HANDLER] = function($object, ?string $format, array $context): mixed {
            return $object->getId();
        };
        return $this->normalizer->normalize($object, $format, $context);
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Carrier;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            Carrier::class => false
        ];
    }
    
    public function supportsDenormalization(mixed $data, string $type, string $format = NULL, array $context = []): bool {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }
        
        return $type === Carrier::class;
    }
    
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed {
        $obj = $context['object_to_populate'];
        $settingsArray = [];
        foreach($data as $name => $value) {
            if (NULL !== ($settings = $obj->getSettingObjectByKey($name))) {
                $settingsArray[] = $settings;
                $settings->setValue($value);
            } else {
                $settings = new CarrierSettings();
                $settings->setName($name);
                $settings->setValue($value);
                $settingsArray[] = $settings;
                $obj->addCarrierSetting($settings);
            }
        }
        foreach (
            array_udiff(
                $obj->getCarrierSettings()->toArray(),
                $settingsArray,
                fn($array1, $array2) => $array1->getId() <=> $array2->getId()
            )
            as $settingsToRemove) {
            $obj->removeCarrierSetting($settingsToRemove);
        }
        
        $context[self::ALREADY_CALLED] = true;
        
        return $this->denormalizer->denormalize($data, $type, $format, $context);
    }
    
    public function setDenormalizer(DenormalizerInterface $denormalizer): void {
        $this->denormalizer = $denormalizer;
    }
}