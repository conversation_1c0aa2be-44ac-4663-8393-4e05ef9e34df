<?php

namespace App\Core\Serializer;

use App\Core\DTO\IntegrationDataDTO;
use App\Core\Entity\Integration;
use App\Core\Entity\IntegrationData;
use App\Core\Entity\IntegrationStatusMapping;
use App\Core\Entity\OrderStatus;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerAwareTrait;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Uid\Uuid;

final class IntegrationDataNormalizer implements NormalizerInterface, DenormalizerAwareInterface, DenormalizerInterface {

    const ALREADY_CALLED_DENORMALIZER = 'INTEGRATION_DATA_DENORMALIZER_ALREADY_CALLED';
    const ALREADY_CALLED_NORMALIZER = 'INTEGRATION_DATA_NORMALIZER_ALREADY_CALLED';

    use SerializerAwareTrait;
    use DenormalizerAwareTrait;
    public function __construct(
        #[Autowire(service: 'serializer.normalizer.object')]
        private NormalizerInterface $normalizer,
        private readonly EntityManagerInterface $entityManager
    ){}

    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        $context[AbstractObjectNormalizer::CIRCULAR_REFERENCE_HANDLER] = function($object, string $format, array $context): mixed {
            return $object->getId();
        };

        $DTO = new IntegrationDataDTO($object);

        return $this->normalizer->normalize($DTO, $format, $context);
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED_NORMALIZER]) && $context[self::ALREADY_CALLED_NORMALIZER]) {
            return false;
        }

        return $data instanceof IntegrationData;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            IntegrationData::class => false,
        ];
    }

    public function setDenormalizer(DenormalizerInterface $denormalizer): void
    {
        $this->denormalizer = $denormalizer;
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed
    {
        if (is_array($data)) {
            $data = $this->handleArrayDenormalization($data, $type, $format, $context);
        }

        $context[self::ALREADY_CALLED_DENORMALIZER] = true;
        return $this->denormalizer->denormalize($data, $type, $format, $context);
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED_DENORMALIZER]) && $context[self::ALREADY_CALLED_DENORMALIZER]) {
            return false;
        }

        return $type === IntegrationData::class;
    }

    private function handleArrayDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): mixed {
        if (isset($data['fetchStatus']) && !empty($data['fetchStatus']) && is_string($data['fetchStatus']) && Uuid::isValid($data['fetchStatus'])) {
            $status = $this->entityManager->getRepository(OrderStatus::class)->find($data['fetchStatus']);
            $data['fetchStatus'] = $status;
        }
//        if (isset($data['integrationStatusMappings']) && !empty($data['integrationStatusMappings'])) {
//            foreach ($data['integrationStatusMappings'] as $key => $value) {
//                if (Uuid::isValid($value['id'])) {
//                    $value = $this->entityManager->getRepository(IntegrationStatusMapping::class)->find($value);
//                }
//            }
//            unset($data['integrationStatusMappings']);
//        }
        if (isset($data['cancelStatus']) && !empty($data['cancelStatus']) && is_string($data['cancelStatus']) && Uuid::isValid($data['cancelStatus'])) {
            $status = $this->entityManager->getRepository(OrderStatus::class)->find($data['cancelStatus']);
            $data['cancelStatus'] = $status;
        }

        return $data;
    }

    public function setSerializer(SerializerInterface $serializer): void {
        $this->serializer = $serializer;
    }

    public function serialize($data, string $format = null, array $context = []): string {
        return $this->serializer->serialize($data, $format, $context);
    }
}