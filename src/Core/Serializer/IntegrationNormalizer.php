<?php

namespace App\Core\Serializer;

use App\Core\Entity\Integration;
use App\Core\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use SCA\Rules\Entity\Rule;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerAwareTrait;
use Symfony\Component\Uid\Uuid;

final class IntegrationNormalizer implements NormalizerInterface, DenormalizerAwareInterface, DenormalizerInterface {

    use SerializerAwareTrait;
    use DenormalizerAwareTrait;

    const ALREADY_CALLED_DENORMALIZER = 'INTEGRATION_DENORMALIZER_ALREADY_CALLED';
    const ALREADY_CALLED_NORMALIZER = 'INTEGRATION_NORMALIZER_ALREADY_CALLED';

    public function __construct(
        #[Autowire(service: 'serializer.normalizer.object')]
        private readonly NormalizerInterface $normalizer,
        private readonly EntityManagerInterface $entityManager,
    ){}

    public function normalize(mixed $object, ?string $format = null, array $context = []): array|string|int|float|bool|\ArrayObject|null
    {
        $context[AbstractObjectNormalizer::CIRCULAR_REFERENCE_HANDLER] = function($object, string $format, array $context): mixed {
            return $object->getId();
        };
        return $this->normalizer->normalize($object, $format, $context);
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool {
        if (isset($context[self::ALREADY_CALLED_NORMALIZER]) && $context[self::ALREADY_CALLED_NORMALIZER]) {
            return false;
        }
        return $data instanceof Integration;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            Integration::class => false,
        ];
    }

    public function setDenormalizer(DenormalizerInterface $denormalizer): void
    {
        $this->denormalizer = $denormalizer;
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): mixed {
        $context[self::ALREADY_CALLED_DENORMALIZER] = true;
        if (is_array($data) ) {
            $data = $this->handleArrayDenormalize($data, $type, $format, $context);;
        }


        return $this->denormalizer->denormalize($data, $type, $format, $context);
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED_DENORMALIZER]) && $context[self::ALREADY_CALLED_DENORMALIZER]) {
            return false;
        }

        return $type === Integration::class;
    }

    private function handleArrayDenormalize(mixed $data, string $type, ?string $format = null, array $context = []) {
        if (array_key_exists('integrationData', $data) && null === $data['integrationData']) {
            $data['integrationData'] = [];
        }
        if (array_key_exists('owner', $data)) {
            unset($data['owner']);
        }

        return $data;
    }
}