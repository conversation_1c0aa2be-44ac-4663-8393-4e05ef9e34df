<?php

namespace App\Core\Serializer\Encoder;

class XmlEncoder extends \Symfony\Component\Serializer\Encoder\XmlEncoder {

    protected array $replacementsMap = [
        '&' => '&amp;',
    ];
    public function decode($data, $format, array $context = []): array
    {
        foreach ($this->replacementsMap as $search => $replace) {
            $data = preg_replace('/' . $search . '/', $replace, $data);
        }

        return parent::decode($data, $format, $context);
    }
}