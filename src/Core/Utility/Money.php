<?php

namespace App\Core\Utility;

class Money {

    private int $amount;

    public function __construct(int $amount)
    {
        $this->amount = $amount;
    }

    public static function fromFloat(float $value): self
    {
        return new self((int) round($value * 100, PHP_ROUND_HALF_UP));
    }

    public function getAmount(): int
    {
        return $this->amount;
    }

    public function toFloat(): float
    {
        return (float) $this->amount / 100;
    }

    public function add(Money $other): Money
    {
        return new Money($this->amount + $other->amount);
    }

    public function subtract(Money $other): Money
    {
        return new Money($this->amount - $other->amount);
    }

    public function equal(Money $other): bool {
        return $this->amount === $other->amount;
    }
}