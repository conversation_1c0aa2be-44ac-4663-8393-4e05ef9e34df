<?php

namespace App\Core\Message;

use Symfony\Component\Uid\Uuid;

class SendInvoiceMailMessage {
    private Uuid $orderId;
    private Uuid $shipmentId;

    public function __construct(Uuid $orderId, Uuid $shipmentId) {
        $this->orderId = $orderId;
        $this->shipmentId = $shipmentId;
    }

    public function getOrderId(): Uuid {
        return $this->orderId;
    }

    public function getShipmentId(): Uuid {
        return $this->shipmentId;
    }
}