<?php

namespace App\Core\Message;

readonly class ChangePrestashopStatusMessenger {
    public function __construct(
        private string $orderId,
        private int $integrationId,
        private int $status
    ) {
    }

    public function getOrderId(): string {
        return $this->orderId;
    }

    public function getIntegrationId(): int {
        return $this->integrationId;
    }

    public function getStatus(): int {
        return $this->status;
    }
}
