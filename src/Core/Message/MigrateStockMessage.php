<?php

namespace App\Core\Message;

use Symfony\Component\Uid\Uuid;

class MigrateStockMessage {
    private string $ean;
    private int $quantityAfter;
    private int $quantityBefore;
    private string $shelfName;

    public function __construct(string $ean, int $quantityAfter, int $quantityBefore, string $shelfName) {
        $this->ean = $ean;
        $this->quantityAfter = $quantityAfter;
        $this->quantityBefore = $quantityBefore;
        $this->shelfName = $shelfName;
    }

    public function getEan(): string
    {
        return $this->ean;
    }

    public function getQuantityAfter(): int
    {
        return $this->quantityAfter;
    }

    public function getQuantityBefore(): int
    {
        return $this->quantityBefore;
    }

    public function getShelfName(): string
    {
        return $this->shelfName;
    }
}