<?php

namespace App\Core\Message;

use Symfony\Component\Uid\Uuid;

class EmailOnStatusMessage {
    private Uuid $orderId;
    private Uuid $statusId;

    private Uuid $emailOnStatusId;

    public function __construct(Uuid $orderId, Uuid $statusId, Uuid $emailOnStatusId) {
        $this->orderId = $orderId;
        $this->statusId = $statusId;
        $this->emailOnStatusId = $emailOnStatusId;
    }

    public function getOrderId(): Uuid {
        return $this->orderId;
    }

    public function getStatusId(): Uuid {
        return $this->statusId;
    }

    public function getEmailOnStatusId(): Uuid {
        return $this->emailOnStatusId;
    }
}