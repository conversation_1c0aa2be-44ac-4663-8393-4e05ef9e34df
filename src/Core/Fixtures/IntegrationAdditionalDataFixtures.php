<?php

namespace App\Core\Fixtures;

use App\Core\Entity\Integration;
use App\Core\Entity\IntegrationSettings;
use App\Core\Taxonomy\OrderStatusTaxonomy;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class IntegrationAdditionalDataFixtures extends Fixture implements FixtureGroupInterface, DependentFixtureInterface {

    public function load(ObjectManager $manager): void
    {
        $data = [
            'STATUS_PAID' => OrderStatusTaxonomy::PRESTASHOP_PAYMENT_ACCEPTED,
            'STATUS_CANCELLED' => OrderStatusTaxonomy::PRESTASHOP_CANCELLED_STATUS_ID,
            'STATUS_SENT' => OrderStatusTaxonomy::PRESTASHOP_SENT_STATUS_ID,
        ];

        $integration = $manager->getRepository(Integration::class)->find(1);
        foreach ($data as $name => $value) {
            $setting = new IntegrationSettings();
            $setting->setName($name);
            $setting->setValue($value);
            $integration->addIntegrationSetting($setting);
        }
        $manager->persist($integration);
        $manager->flush();
    }


    public static function getGroups(): array
    {
        return ['additionalSettingsUpdate'];
    }
    
    public  function getDependencies(): array
    {
        return [
            IntegrationFixtures::class,
        ];
    }
}