<?php

namespace App\Core\Fixtures;

use App\Core\Entity\Settings;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class BaselinkerFixure extends Fixture implements FixtureGroupInterface {


    public function __construct(private readonly UserPasswordHasherInterface $hasher){}

    public function load(ObjectManager $manager): void
    {
        $data = [
            'BASELINKER_TOKEN' => '2001444-2004465-2SUX2M4K756MDG211YRS2MUPIS6SQ8WT68RNCUR6Z7Z8MQGBZHS7FE18GYXLNAHR',
            'BASELINKER_API_URL' => 'https://api.baselinker.com/connector.php',
        ];

        foreach ($data as $name => $value) {
            $data = new Settings();
            $data->setName($name);
            $data->setValue($value);
            $manager->persist($data);
        }
        $manager->flush();

    }

    public static function getGroups(): array
    {
        return ['bsDataInsert'];
    }
}