<?php

namespace App\Core\Fixtures;

use App\Core\Service\Settings;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;

class SettingsFixtures extends Fixture implements FixtureGroupInterface {
    public function __construct(private readonly Settings $settings) {
    }
    public function load(ObjectManager $manager): void {
        $this->settings->set('PRINTNODE_API_KEY', 'M5nHlz0QfQzWoGU6X_Ac2y8g5z8DjMmYyNVuir0xJQA');
        $this->settings->set('PRINTNODE_BASE_URL', 'https://api.printnode.com/');
        $data = [
            '74105397' => [
                'admin'
            ],
        ];
        
        $this->settings->set('PRINTNODE_STATIONS', json_encode($data));
    }
    
    public static function getGroups(): array {
        return ['settingsUpdate'];
    }
}
