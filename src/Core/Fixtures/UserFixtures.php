<?php

namespace App\Core\Fixtures;

use App\Core\Entity\User;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class UserFixtures extends Fixture {
    
    public const ADMIN_USER_REFERENCE = 'admin-user';
    public const SYSTEM_USER_REFERENCE = 'system-user';

    public function __construct(private readonly UserPasswordHasherInterface $hasher){}
    
    public function load(ObjectManager $manager): void
    {
        $user = new User();
        $user->setEmail('<EMAIL>');
        $password = $this->hasher->hashPassword($user,'supertajnehaslo!bwms');
        $user->setPassword($password);
        $user->setRoles(['ROLE_ADMIN']);
        $user->setUsername('admin');
        $manager->persist($user);
        $this->addReference(self::ADMIN_USER_REFERENCE, $user);
        $manager->flush();
        
        $userSystem = new User();
        $userSystem->setEmail('system');

        $password = $this->hasher->hashPassword($userSystem,'systemsecretpassword!2025');

        $userSystem->setPassword($password);
        $userSystem->setRoles(['ROLE_ADMIN']);
        $userSystem->setUsername('system');
        $manager->persist($userSystem);
        $this->addReference(self::SYSTEM_USER_REFERENCE, $userSystem);
        $manager->flush();
    }
}