<?php

namespace App\Core\Fixtures;

use App\Core\Entity\Integration;
use App\Core\Entity\IntegrationSettings;
use App\Core\Entity\User;
use App\Core\Taxonomy\OrderStatusTaxonomy;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class IntegrationFixtures extends Fixture implements DependentFixtureInterface {

    public function load(ObjectManager $manager): void
    {
        $data = [
            [
                'name' => 'ultrasstore.com',
                'type' => 'prestashop',
                'settings' => [
                    'API_URL' => 'https://ultrasstore.com',
                    'API_TOKEN' => 'FQ7ZVZQ8HREK1C23NLRE5887G2HSB7WV',
                    'STATUS_PAID' => ORderStatusTaxonomy::PRESTASHOP_PAYMENT_ACCEPTED,
                    'STATUS_CANCELLED' => ORderStatusTaxonomy::PRESTASHOP_CANCELLED_STATUS_ID,
                    'STATUS_SENT' => ORderStatusTaxonomy::PRESTASHOP_SENT_STATUS_ID,
                ]
            ],
        ];


        foreach ($data as $integrationData) {
            $integration = new Integration();
            $integration->setName($integrationData['name']);
            $integration->setType($integrationData['type']);
            $integration->setOwner($this->getReference(UserFixtures::ADMIN_USER_REFERENCE, User::class));

            if (!empty($integrationData['settings'])) {
                foreach ($integrationData['settings'] as $k => $v) {
                    $integrationSettings = new IntegrationSettings();
                    $integrationSettings->setIntegration($integration);
                    $integrationSettings->setName($k);
                    $integrationSettings->setValue($v);
                    $integration->addIntegrationSetting($integrationSettings);
                }
            }
            $manager->persist($integration);
        }
        $manager->flush();
    }

    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
        ];
    }
}