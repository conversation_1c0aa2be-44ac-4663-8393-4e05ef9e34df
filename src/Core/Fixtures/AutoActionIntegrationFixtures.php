<?php

namespace App\Core\Fixtures;

use App\Core\Entity\Integration;
use App\Core\Entity\IntegrationData;
use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Enum\FetchProducts;
use App\Core\Enum\OrderFetchInterval;
use App\Core\Enum\OrderStatusSendPaid;
use App\Core\Enum\OrderStatusSynchronize;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;
use SCA\Rules\Entity\Rule;

class AutoActionIntegrationFixtures extends Fixture implements FixtureGroupInterface {
    public function load(ObjectManager $manager): void
    {
        $integration = $manager->getRepository(Integration::class)->find(1);
        $integrationData = new IntegrationData();


        $integrationData->setFetchStatus($manager->getRepository(OrderStatus::class)->find('0194dbdf-1b25-7693-9049-4e3639e7b96e'));
        $integrationData->setCancelStatus($manager->getRepository(OrderStatus::class)->find('0195101b-187c-7708-8f92-95b35030fdd5'));
        $integrationData->setOrderStatusSendPaid(OrderStatusSendPaid::NO);
        $integrationData->setOrderFetchInterval(OrderFetchInterval::TEN_MINUTES);
        $integrationData->setFetchProducts(FetchProducts::NO);
        $integrationData->setOrderStatusSynchronize(OrderStatusSynchronize::NO);
        $integrationData->setApiUrl('ultrasstore.com');
        $integrationData->setApiToken('FQ7ZVZQ8HREK1C23NLRE5887G2HSB7WV');
        $integrationData->setApiSecret('apiSecret');
        $integration->setIntegrationData($integrationData);
        $manager->persist($integration);

        $actions = [
            [
                'event' => 'order.fetched.after.save',
                'conditions' => json_decode('[{"name":"order.integration.condition","fields":[{"name":"integration","value":[1],"operator":"==="}]}]'),
                'actions' => json_decode('[{"name":"create.zk","fields":[]}]'),
                'parameters' => [],
            ],
            [
                'event' => 'order.is.paid',
                'conditions' => json_decode('[{"name":"order.integration.condition","fields":[{"name":"integration","value":[1],"operator":"==="}]}]'),
                'actions' => json_decode('[{"name":"create.invoice","fields":[]},{"name":"set.status.action","fields":{"status":"0195101b-187c-7708-8f92-95b352ffbb3e"}}]'),
                'parameters' => [],
            ],
            [
                'event' => 'order.status.change',
                'conditions' => json_decode('[{"name":"order.status.condition","fields":[{"name":"status","value":"0195101b-187c-7708-8f92-95b35030fdd5","operator":"==="}]},{"name":"zk.document.exists","fields":[{"name":"exists","value":"true","operator":"==="}]}]'),
                    'actions' => json_decode('[{"name":"remove.zk","fields":[]}]'),
                'parameters' => [],
            ],
            [
                'event' => 'order.is.cancelled',
                'conditions' => json_decode('[{"name":"order.integration.condition","fields":[{"name":"integration","value":[1],"operator":"==="}]}]'),
                'actions' => json_decode('[{"name":"set.status.action","fields":{"status":"0195101b-187c-7708-8f92-95b35030fdd5"}}]'),
                'parameters' => [],
            ],
            [
                'event' => 'order.status.change',
                'conditions' => json_decode('[{"name":"order.status.condition","fields":[{"name":"status","value":"0195101b-187c-7708-8f92-95b35030fdd5","operator":"==="}]},{"name":"fs.document.exists","fields":[{"name":"exists","value":"true","operator":"==="}]}]'),
                'actions' => json_decode('[{"name":"set.status.action","fields":{"status":"01958ef2-021d-7ca1-98cf-ada89cab8872"}}]'),
                'parameters' => [],
            ],
            [
                'event' => 'order.status.in.time',
                'conditions' => json_decode('[{"name":"order.status.time.condition","fields":[{"name":"days","value":3,"operator":">"},{"name":"excludeWeekends","value":"false"},{"name":"status","value":"0195101b-187c-7708-8f92-95b3503aecbf"},{"name":"minutes","value":0,"operator":">"}]}]'),
                'actions' => json_decode('[{"name":"set.status.action","fields":{"status":"0195101b-187c-7708-8f92-95b3512f74e8"}}]'),
                'parameters' => [],
            ],
            [
                'event' => 'order.is.not.paid',
                'conditions' => json_decode('[{"name":"order.integration.condition","fields":[{"name":"integration","value":[1],"operator":"==="}]}]'),
                'actions' => json_decode('[{"name":"set.status.action","fields":{"status":"0195101b-187c-7708-8f92-95b3503aecbf"}}]'),
                'parameters' => [],
            ],
            [
                'event' => 'zk.create.error',
                'conditions' => json_decode('[{"name":"order.integration.condition","fields":[{"name":"integration","value":[1],"operator":"==="}]}]'),
                'actions' => json_decode('[{"name":"set.status.action","fields":{"status":"0195101b-187c-7708-8f92-95b350f346ae"}}]'),
                'parameters' => [],
            ],
            [
                'event' => 'order.fs.create.error',
                'conditions' => json_decode('[{"name":"order.integration.condition","fields":[{"name":"integration","value":[1],"operator":"==="}]}]'),
                'actions' => json_decode('[{"name":"set.status.action","fields":{"status":"0195101b-187b-7eb1-a36b-01f1a25f8a55"}}]'),
                'parameters' => [],
            ]
        ];

        foreach ($actions as $action) {
            $actionData = new Rule($action['event'], $action['conditions'], $action['actions'], $action['parameters']);;
            $manager->persist($actionData);
        }

        $manager->flush();
    }

    public static function getGroups(): array {
        return ['AutoActionIntegrationLoad'];
    }
}