<?php

namespace App\Core\Fixtures;

use App\Core\Entity\OrderStatus;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;

class StatusFixtures extends Fixture implements FixtureGroupInterface {
    public function load(ObjectManager $manager): void
    {
        $statuses = OrderTaxonomy::getAllStatuses();
        foreach ($statuses as $statusName) {
            $orderStatus = new OrderStatus();
            $orderStatus->setName($statusName);
            $orderStatus->setShortName('shortName');
            $orderStatus->setFullName($statusName);
            $orderStatus->setColor('#C2C2C2');
            $manager->persist($orderStatus);
        }

        $manager->flush();
    }

    public static function getGroups(): array {
        return ['statusLoad'];
    }
}