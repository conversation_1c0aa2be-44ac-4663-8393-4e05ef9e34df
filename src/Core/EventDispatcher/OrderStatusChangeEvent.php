<?php

namespace App\Core\EventDispatcher;

use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use Symfony\Contracts\EventDispatcher\Event;

class OrderStatusChangeEvent extends Event {

    private Order $order;

    private OrderStatus $orderStatus;

    public function setOrder(Order $order): void {
        $this->order = $order;
    }

    public function setOrderStatus(OrderStatus $status): void {
        $this->orderStatus = $status;
    }

    public function getOrder(): Order {
        return $this->order;
    }

    public function getOrderStatus(): OrderStatus {
        return $this->orderStatus;
    }
}