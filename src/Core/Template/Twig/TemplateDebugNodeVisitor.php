<?php

namespace App\Core\Template\Twig;

use Twig\Node\Node;
use Twig\Environment;
use Twig\Node\ModuleNode;
use Twig\Node\TextNode;
use Twig\NodeVisitor\NodeVisitorInterface;

class TemplateDebugNodeVisitor implements NodeVisitorInterface
{
    public function enterNode(Node $node, Environment $env): Node
    {
        if ($node instanceof ModuleNode) {
            $templateName = $node->getTemplateName();

            $startComment = new TextNode(sprintf("<!-- START TEMPLATE: %s -->\n", $templateName), $node->getTemplateLine());

            $node->setNode('display_start', $startComment);
        }

        return $node;
    }

    public function leaveNode(Node $node, Environment $env): Node
    {
        if ($node instanceof ModuleNode) {
            $templateName = $node->getTemplateName();

            $endComment = new TextNode(sprintf("<!-- END TEMPLATE: %s -->\n", $templateName), $node->getTemplateLine());

            $node->setNode('display_end', $endComment);
        }

        return $node;
    }

    public function getPriority(): int
    {
        return 0;
    }
}
