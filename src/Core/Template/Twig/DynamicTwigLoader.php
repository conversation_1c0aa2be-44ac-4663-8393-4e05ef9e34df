<?php

namespace App\Core\Template\Twig;

use App\Engine\Service\ModuleDiscoveryService;
use Twig\Loader\FilesystemLoader;

class DynamicTwigLoader extends FilesystemLoader {
    private bool $called = false;
    private ?ModuleDiscoveryService $moduleDiscovery = null;

    public function __construct($paths = [], string $namespace = self::MAIN_NAMESPACE, ModuleDiscoveryService $moduleDiscovery = null) {
        parent::__construct($paths, $namespace);
        $this->moduleDiscovery = $moduleDiscovery;
        if ($this->moduleDiscovery) {
            $this->loadModuleTemplates();
        }
    }

    public function setTheme(string $theme): void
    {
        if (!$this->called) {
            $this->called = true;
            $this->setPaths($this->paths[self::MAIN_NAMESPACE][0] . '/' . $theme);
        }
    }

    private function loadModuleTemplates(): void
    {
        if (!$this->moduleDiscovery) {
            return;
        }

        $discoveredModules = $this->moduleDiscovery->discoverModules();

        foreach ($discoveredModules as $moduleName => $moduleInfo) {
            $templatePath = $moduleInfo['path'] . '/templates';

            if (is_dir($templatePath)) {

                $this->addPath($templatePath, $moduleName);
            }
        }
    }
}
