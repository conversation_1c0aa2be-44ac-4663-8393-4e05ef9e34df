<?php

namespace App\Core\Template;

use App\Core\Template\Render\RendererInterface;
use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;
use Twig\Environment;

class Renderer {

    public function __construct(private Environment $twig, #[AutowireIterator(RendererInterface::class)] private iterable $elements) {}

    public function render(string $template, array $data): SafeMarkup {

        foreach ($data as $key => &$value) {
            if (is_array($value) && isset($value['type'])) {
                foreach ($this->elements as $element) {
                    if ($element->supports($value['type'])) {
                        $data[$key] = new SafeMarkup($element->render($value), 'UTF-8');
                    }
                }
            }
        }
        return new SafeMarkup($this->twig->render($template, $data), 'UTF-8');
    }
}