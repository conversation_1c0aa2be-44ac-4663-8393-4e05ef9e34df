<?php

namespace App\Core\Template\EventSystem\EventListener;

use App\Core\Template\EventSystem\Event\EventStorage;
use App\Core\Template\EventSystem\Event\JavascriptEvent;
use App\Core\Template\EventSystem\Event\PageBottom;
use App\Core\Template\EventSystem\Event\PageFooterEvent;
use App\Core\Template\EventSystem\Event\PageHeaderEvent;
use App\Core\Template\EventSystem\Event\PageSidebarEvent;
use App\Core\Template\EventSystem\Event\PageTopEvent;
use App\Core\Template\EventSystem\Event\StylesheetEvent;
use App\Core\Template\Renderer;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Contracts\EventDispatcher\Event;
use Twig\Markup;


#[AsEventListener(event: KernelEvents::VIEW, method: 'onKernelView')]
final class PageView {

    public function __construct(private Renderer $renderer, private EventDispatcherInterface $eventDispatcher) {}

    public function onKernelView(Event $event): Event {
        return $event;

        $controllerResults = $event->getControllerResult();
        if ($controllerResults['template'] && $controllerResults['data']) {
            $controllerResultsRender = $this->renderer->render($controllerResults['template'], $controllerResults['data']);
            $pageRender = $this->renderer->render('web/base_theme/html.html.twig',
                [
                    'title' => $controllerResults['title'] ?? 'BWMS',
                    'body_class' => $controllerResults['body_class'] ?? '',
                    'stylesheets' => $this->getPageStylesheets(),
                    'javascripts' => $this->getPageJavascript(),
                    'page_header' => $this->getPageHeader(),
                    'page_top' => $this->getPageTop(),
                    'page_sidebar' => $this->getPageSidebar(),
                    'page_content' => $controllerResultsRender,
                    'page_bottom' => $this->getPageBottom(),
                    'page_footer' => $this->getPageFooter(),
                ]
            );
            $response = new Response();
            $response->setContent($pageRender);
            $event->setResponse($response);
        }

        return $event;
    }

    private function getPageStylesheets(): Markup {
        $pageStylesheetEventResult = $this->eventDispatcher->dispatch(new StylesheetEvent(), EventStorage::PAGE_STYLESHEET);

        return $this->renderer->render(
            'web/base_theme/stylesheet.html.twig',
            [
                'stylesheets' => $pageStylesheetEventResult->getContent() ?? []
            ]
        );
    }

    private function getPageJavascript(): Markup {
        $pageJavascriptEventResult = $this->eventDispatcher->dispatch(new JavascriptEvent(), EventStorage::PAGE_JAVASCRIPT);

        return $this->renderer->render(
            'web/base_theme/javascript.html.twig',
            [
                'javascripts' => $pageJavascriptEventResult->getContent() ?? []
            ]
        );
    }


    private function getPageHeader(): Markup {
        $pageHeaderEventResult = $this->eventDispatcher->dispatch(new PageHeaderEvent(), EventStorage::PAGE_HEADER);

        return new Markup(implode('', $pageHeaderEventResult->getContent() ?? []), 'UTF-8');
    }

    private function getPageTop(): Markup  {
        $pageTopEventResult = $this->eventDispatcher->dispatch(new PageTopEvent(), EventStorage::PAGE_TOP);

        return new Markup(implode('', $pageTopEventResult->getContent() ?? []), 'UTF-8');
    }

    private function getPageSidebar(): Markup {
        $pageSidebarEventResult = $this->eventDispatcher->dispatch(new PageSidebarEvent(), EventStorage::PAGE_SIDEBAR);

        return new Markup(implode('', $pageSidebarEventResult->getContent() ?? []), 'UTF-8');
    }

    private function getPageBottom(): Markup {
        $pageBottomEventResult = $this->eventDispatcher->dispatch(new PageBottom(), EventStorage::PAGE_BOTTOM);

        return new Markup(implode('', $pageBottomEventResult->getContent() ?? []), 'UTF-8');
    }

    private function getPageFooter(): Markup {
        $pageFooterEventResult = $this->eventDispatcher->dispatch(new PageFooterEvent(), EventStorage::PAGE_FOOTER);

        return new Markup(implode('', $pageFooterEventResult->getContent() ?? []), 'UTF-8');
    }
}