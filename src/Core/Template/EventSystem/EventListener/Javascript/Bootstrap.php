<?php

namespace App\Core\Template\EventSystem\EventListener\Javascript;

use App\Core\Template\EventSystem\Event\EventStorage;
use App\Core\Template\EventSystem\Event\JavascriptEvent;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: EventStorage::PAGE_JAVASCRIPT, method: 'onPageJavascript')]
final class Bootstrap {

    public function onPageJavascript(JavascriptEvent $event): JavascriptEvent {

        $javascript = [
            'src' => 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js',
            'crossorigin' => 'anonymous',
            'integrity' => 'sha384-BBtl+eGJRgqQAUMxJ7pMwbEyER4l1g+O15P+16Ep7Q9Q+zqX6gSbd85u4mG4QzX+',
        ];
        $event->addContent($javascript);

        return $event;
    }
}