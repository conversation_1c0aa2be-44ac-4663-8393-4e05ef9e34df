<?php

namespace App\Core\Template\EventSystem\EventListener\MainMenu;

use App\Core\Service\Menu\MenuService;
use App\Core\Template\EventSystem\Event\EventStorage;
use App\Core\Template\EventSystem\Event\PageTopEvent;
use App\Core\Template\Renderer;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;

#[AsEventListener(event: EventStorage::PAGE_TOP, method: 'onPageTop')]
final class MainMenu {

    public function __construct(
        private readonly RouterInterface $router,
        private readonly Security $security,
        private readonly Renderer $renderer,
        private readonly MenuService $menuService,
    ){}

    public function onPageTop(PageTopEvent $event): PageTopEvent {

        if (!$this->security->isGranted('ROLE_USER')) {
            return $event;
        }
        $links = $this->menuService->getMenuLinks();
//        $items = [
//            [
//                'url' => $this->router->generate('app_user_index', [], UrlGeneratorInterface::ABSOLUTE_URL),
//                'title' => 'Users'
//            ],
//            [
//                'url' => $this->router->generate('app_logout', [], UrlGeneratorInterface::ABSOLUTE_URL),
//                'title' => 'Log Out',
//            ],
//            [
//                'url' => $this->router->generate('web_orders_list', ['page' => 0], UrlGeneratorInterface::ABSOLUTE_URL),
//                'title' => 'Orders'
//            ],
//        ];
        $renderedMenu = $this->renderer->render('menu/menu.html.twig', ['links' => $links]);
        $event->addContent('mainMenu', $renderedMenu);

        return $event;
    }
}