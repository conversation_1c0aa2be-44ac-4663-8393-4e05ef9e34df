<?php

namespace App\Core\Template\EventSystem\EventListener\Stylesheet;

use App\Core\Template\EventSystem\Event\EventStorage;
use App\Core\Template\EventSystem\Event\StylesheetEvent;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

#[AsEventListener(event: EventStorage::PAGE_STYLESHEET, method: 'onPageStylesheet')]
final class Bootstrap {

    public function onPageStylesheet(StylesheetEvent $event): StylesheetEvent {
        $stylesheets = [
            'href' => 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
            'crossorigin' => 'anonymous',
            'integrity' => 'sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN',
        ];

        $event->addContent($stylesheets);

        return $event;
    }
}