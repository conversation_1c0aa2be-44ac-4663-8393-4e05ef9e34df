<?php

namespace App\Core\Carrier\CarrierType;

use App\Core\Attribute\AsCarrierType;
use App\Core\Entity\Carrier;
use App\Core\Entity\CarrierSettings;
use App\Core\Service\Carrier\DHL\DHLService;
use Doctrine\ORM\EntityManagerInterface;

#[AsCarrierType]
class DHL extends AbstractCarrierType implements CarrierTypeInterface
{
    public function __construct(
        private readonly DHLService $carrierService,
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct($entityManager);
    }

    private const REQUIRED_FIELDS = [
        [
            'name' => 'DHL24_WSDL_URL',
            'type' => 'text',
            'label' => 'URL WSDL',
        ],
        [
            'name' => 'DHL24_USERNAME',
            'type' => 'text',
            'label' => 'Nazwa użytkownika',
        ],
        [
            'name' => 'DHL24_PASSWORD',
            'type' => 'password',
            'label' => 'Hasło',
        ],
    ];

    public function getCarrierType(): string
    {
        return 'DHL';
    }

    public function createCarrier($data): Carrier {
        $carrier = new Carrier();
        $carrier->setType($this->getCarrierType());
        $carrier->setName($data['name']);

        foreach ($this->getRequiredFields() as $field) {
            $carrierSetting = new CarrierSettings();
            $carrierSetting->setName($field['name']);
            $carrierSetting->setValue($data[$field['name']]);
            $carrier->addCarrierSetting($carrierSetting);
        }

        $this->entityManager->persist($carrier);;
        $this->entityManager->flush();

        return $carrier;
    }

    public function getRequiredFields(): array
    {
        return self::REQUIRED_FIELDS;
    }

    public function getCarrierProvider(): DHLService
    {
        return $this->carrierService;
    }
}