<?php

namespace App\Core\Carrier\CarrierType;

use App\Core\Attribute\AsCarrierType;
use App\Core\Entity\Carrier;
use App\Core\Entity\CarrierSettings;
use App\Core\Service\Carrier\Apaczka\ApaczkaService;
use Doctrine\ORM\EntityManagerInterface;

#[AsCarrierType]
class <PERSON><PERSON><PERSON><PERSON> extends AbstractCarrierType implements CarrierTypeInterface
{
    public function __construct(
        private readonly ApaczkaService $carrierService,
        private readonly EntityManagerInterface $entityManager
    ) {
        parent::__construct($entityManager);
    }

    private const REQUIRED_FIELDS = [
        [
            'name' => 'APP_ID',
            'type' => 'text',
            'label' => 'ID aplikacji',
        ],
        [
            'name' => 'APP_SECRET',
            'type' => 'password',
            'label' => 'Klucz aplikacji',
        ],
        [
            'name' => 'API_URL',
            'type' => 'text',
            'label' => 'Adres API',
        ],
        [
            'name' => 'PRINT_TYPE',
            'type' => 'select',
            'options' => [
                [
                    'value' => 0,
                    'label' => 'Standardowa etykieta PDF'
                ],
                [
                    'value' => 1,
                    'label' => 'Etykieta PDF do wydruku na drukarkach Zebra'
                ],
            ],
            'label' => 'Typ wydruku',
        ],
        [
            'name' => 'COMPANY_NAME',
            'type' => 'text',
            'label' => 'Nazwa firmy',
        ],
        [
            'name' => 'CONTACT_PERSON',
            'type' => 'text',
            'label' => 'Osoba kontaktowa',
        ],
        [
            'name' => 'ADDRESS',
            'type' => 'text',
            'label' => 'Adres',
        ],
        [
            'name' => 'POSTAL_CODE',
            'type' => 'text',
            'label' => 'Kod pocztowy',
        ],
        [
            'name' => 'CITY',
            'type' => 'text',
            'label' => 'Miasto',
        ],
        [
            'name' => 'COUNTRY',
            'type' => 'text',
            'label' => 'Kraj',
        ],
        [
            'name' => 'PHONE',
            'type' => 'tel',
            'label' => 'Telefon',
        ],
        [
            'name' => 'EMAIL',
            'type' => 'text',
            'label' => 'email',
        ],
        [
            'name' => 'PICKUP_TYPE',
            'type' => 'select',
            'options' => [
                [
                    'value' => 'COURIER',
                    'label' => 'Przyjazd kuriera',
                ],
                [
                    'value' => 'SELF',
                    'label' => 'Dostarczę przesyłkę samodzielnie do punktu odbioru'
                ]
            ],
            'label' => 'Typ odbioru',
        ],
        [
            'name' => 'PICKUP_HOURS_FROM',
            'type' => 'time',
            'label' => 'Godziny odbioru od',
        ],
        [
            'name' => 'PICKUP_HOURS_TO',
            'type' => 'time',
            'label' => 'Godziny odbioru do',
        ],
    ];

    public function getCarrierType(): string
    {
        return 'Apaczka';
    }

    public function getRequiredFields(): array
    {
        return self::REQUIRED_FIELDS;
    }

    public function getCarrierProvider(): ApaczkaService
    {
        return $this->carrierService;
    }
}