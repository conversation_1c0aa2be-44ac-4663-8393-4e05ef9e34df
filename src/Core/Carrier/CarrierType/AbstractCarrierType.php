<?php

namespace App\Core\Carrier\CarrierType;

use App\Core\Entity\Carrier;
use App\Core\Entity\CarrierSettings;
use Doctrine\ORM\EntityManagerInterface;

abstract class AbstractCarrierType implements CarrierTypeInterface
{
    public function __construct(private readonly EntityManagerInterface $entityManager) {}
    
    public function getCarrierType(): string
    {
        // TODO: Implement getCarrierType() method.
    }

    public function createCarrier($data)
    {
        $carrier = new Carrier();
        $carrier->setType($this->getCarrierType());
        $carrier->setName($data['name']);

        foreach ($this->getRequiredFields() as $field) {
            $carrierSetting = new CarrierSettings();
            if ('select' === $field['type']) {
                $carrierSetting->setName($field['name']);
            } else {
                $carrierSetting->setName($field['name']);
            }
            $carrierSetting->setValue($data[$field['name']]);
            $carrier->addCarrierSetting($carrierSetting);
        }

        $this->entityManager->persist($carrier);;
        $this->entityManager->flush();

        return $carrier;
    }

    public function getRequiredFields(): array
    {
        // TODO: Implement getRequiredFields() method.
    }

    public function getCarrierProvider()
    {
        // TODO: Implement getCarrierProvider() method.
    }
}