<?php

namespace App\Core\Carrier\CarrierType;

use App\Core\Attribute\AsCarrierType;
use App\Core\Entity\Carrier;
use App\Core\Entity\CarrierSettings;
use App\Core\Service\Carrier\Inpost\InpostService;
use Doctrine\ORM\EntityManagerInterface;

#[AsCarrierType]
class InPost extends  AbstractCarrierType implements CarrierTypeInterface
{
    public function __construct(
        private readonly InpostService $carrierService,
        private readonly EntityManagerInterface $entityManager
    ) {
        parent::__construct($entityManager);
    }

    private const REQUIRED_FIELDS = [
        [
            'name' => 'INPOST_API_URL',
            'type' => 'text',
            'label' => 'Adres API',
        ],
        [
            'name' => 'INPOST_API_KEY',
            'type' => 'password',
            'label' => 'Klucz API',
        ],
        [
            'name' => 'INPOST_API_ORG_ID',
            'type' => 'text',
            'label' => 'ID organizacji',
        ],
    ];

    public function getCarrierType(): string
    {
        return 'InPost';
    }

    public function getRequiredFields(): array
    {
        return self::REQUIRED_FIELDS;
    }

    public function getCarrierProvider(): InpostService
    {
        return $this->carrierService;
    }
}