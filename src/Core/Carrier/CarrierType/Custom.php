<?php

namespace App\Core\Carrier\CarrierType;

use App\Core\Attribute\AsCarrierType;
use App\Core\Entity\Carrier;
use App\Core\Entity\CarrierSettings;
use App\Core\Service\Carrier\Custom\CustomCarrierService;
use App\Core\Service\Carrier\Inpost\InpostService;
use Doctrine\ORM\EntityManagerInterface;

#[AsCarrierType]
class Custom extends  AbstractCarrierType implements CarrierTypeInterface
{
    public function __construct(
        private readonly CustomCarrierService $carrierService,
        private readonly EntityManagerInterface $entityManager
    ) {
        parent::__construct($entityManager);
    }

    private const REQUIRED_FIELDS = [
        [
            'name' => 'CUSTOM_NAME',
            'type' => 'text',
            'label' => 'Nazwa',
        ],
    ];

    public function getCarrierType(): string
    {
        return 'Custom';
    }

    public function getRequiredFields(): array
    {
        return self::REQUIRED_FIELDS;
    }

    public function getCarrierProvider(): CustomCarrierService
    {
        return $this->carrierService;
    }
}