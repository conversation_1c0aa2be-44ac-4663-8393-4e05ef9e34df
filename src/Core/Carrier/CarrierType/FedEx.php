<?php

namespace App\Core\Carrier\CarrierType;

use App\Core\Attribute\AsCarrierType;
use App\Core\Service\Carrier\Fedex\FedexService;
use Doctrine\ORM\EntityManagerInterface;

#[AsCarrierType]
class FedEx extends AbstractCarrierType implements CarrierTypeInterface
{
    public function __construct(
        private readonly FedexService $carrierService,
        EntityManagerInterface $entityManager
    ) {
        parent::__construct($entityManager);
    }

    private const  REQUIRED_FIELDS = [
        [
            'name' => 'API_KEY',
            'type' => 'text',
            'label' => 'Klucz API',
        ],
        [
            'name' => 'API_ACCOUNT_NO',
            'type' => 'text',
            'label' => 'Numer konta API',
        ],
        [
            'name' => 'API_ACCOUNT_METER_NO',
            'type' => 'text',
            'label' => 'Numer licznika API',
            'required' => false,
        ],
        [
            'name' => 'API_SECRET',
            'type' => 'password',
            'label' => 'API Secret',
        ],
        [
            'name' => 'API_URL',
            'type' => 'select',
            'label' => 'Url Api',
            'options' => [
                [
                    'label' => 'Produkcja',
                    'value' => 'https://apis.fedex.com',
                ],
                [
                    'label' => 'Sandbox',
                    'value' => 'https://apis-sandbox.fedex.com',
                ]
            ],
        ],
        [
            'name' => 'COMPANY_NAME',
            'type' => 'text',
            'label' => 'Nazwa firmy',
        ],
        [
            'name' => 'CONTACT_PERSON',
            'type' => 'text',
            'label' => 'Osoba kontaktowa',
        ],
        [
            'name' => 'ADDRESS',
            'type' => 'text',
            'label' => 'Adres',
        ],
        [
            'name' => 'POSTAL_CODE',
            'type' => 'text',
            'label' => 'Kod pocztowy',
        ],
        [
            'name' => 'CITY',
            'type' => 'text',
            'label' => 'Miasto',
        ],
        [
            'name' => 'COUNTRY',
            'type' => 'text',
            'label' => 'Kraj (po angielsku np. Poland)',
        ],
        [
            'name' => 'PHONE',
            'type' => 'tel',
            'label' => 'Telefon - format międzynarodowy np. +48 ***********',
        ],
        [
            'name' => 'EMAIL',
            'type' => 'text',
            'label' => 'email',
        ],
        [
            'name' => 'EORI_NUMBER',
            'type' => 'text',
            'label' => 'EORI Number',
        ],
        [
            'name' => 'PAYER_FOR_SHIPPING',
            'type' => 'select',
            'options' => [
                [
                    'value' => 'SENDER',
                    'label' => 'Nadawca',
                ],
                [
                    'value' => 'RECIPIENT',
                    'label' => 'Odbiorca'
                ],
                [
                    'value' => 'THIRD_PARTY',
                    'label' => 'Trzecia strona'
                ]
            ],
            'label' => 'Dodatkowe opłaty ponosi',
        ],
        [
            'name' => 'PICKUP_HOURS_FROM',
            'type' => 'time',
            'label' => 'Godziny odbioru od',
        ],
        [
            'name' => 'PICKUP_HOURS_TO',
            'type' => 'time',
            'label' => 'Godziny odbioru do',
        ],
        [
            'name' => 'SHIPPING_PURPOSE',
            'type' => 'select',
            'options' => [
                [
                    'value' => 'SOLD',
                    'label' => 'Cel handlowy',
                ],
                [
                    'value' => 'GIFT',
                    'label' => 'Prezent',
                ],
                [
                    'value' => 'SAMPLE',
                    'label' => 'Próbka',
                ],
                [
                    'value' => 'NOT SOLD',
                    'label' => 'Rzeczy do użytku własnego',
                ],
                [
                    'value' => 'PERSONAL_EFFECTS',
                    'label' => 'Przedmioty osobiste',
                ],
                [
                    'value' => 'REPAIR_AND_RETURN',
                    'label' => 'Naprawa i zwrot',
                ],
            ],
            'label' => 'FedEx zagraniczny - cel wysyłki'
        ],
        [
            'name' => 'LIMIT_SINGLE_LABEL',
            'type' => 'select',
            'options' => [
                [
                    'value' => '0',
                    'label' => 'NIE',
                ],
                [
                    'value' => '1',
                    'label' => 'TAK',
                ],
            ],
            'label' => 'Ogranicz liczbę etykiet do 1 na każdą pod paczkę'
        ],
        [
            'name' => 'FORWARD_SHIPPING_COSTS',
            'type' => 'select',
            'options' => [
                [
                    'value' => '0',
                    'label' => 'NIE',
                ],
                [
                    'value' => '1',
                    'label' => 'TAK',
                ],
            ],
            'label' => 'Przekazuj koszty przesyłki'
        ],
        [
            'name' => 'INCOTERMS',
            'type' => 'select',
            'options' => [
                [
                    'value' => '0',
                    'label' => '-----WYBIERZ-----',
                ],
                [
                    'value' => 'FCA',
                    'label' => 'FCA',
                ],
                [
                    'value' => 'CIP',
                    'label' => 'CIP',
                ],
                [
                    'value' => 'CPT',
                    'label' => 'CPT',
                ],
                [
                    'value' => 'EXW',
                    'label' => 'EXW',
                ],
                [
                    'value' => 'DDU',
                    'label' => 'DDU',
                ],
                [
                    'value' => 'DDP',
                    'label' => 'DDP',
                ],
                [
                    'value' => 'DAP',
                    'label' => 'DAP',
                ],
                [
                    'value' => 'DPU',
                    'label' => 'DPU',
                ],
            ],
            'label' => 'FedEx zagraniczny - cel wysyłki'
        ],

    ];

    public function getCarrierType(): string
    {
        return 'Fedex';
    }

    public function getRequiredFields(): array
    {
        return self::REQUIRED_FIELDS;
    }

    public function getCarrierProvider(): FedexService
    {
        return $this->carrierService;
    }
}