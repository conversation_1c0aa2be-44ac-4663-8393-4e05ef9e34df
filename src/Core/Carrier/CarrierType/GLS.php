<?php

namespace App\Core\Carrier\CarrierType;

use App\Core\Attribute\AsCarrierType;
use App\Core\Entity\Carrier;
use App\Core\Entity\CarrierSettings;
use App\Core\Service\Carrier\GLS\GLSService;
use Doctrine\ORM\EntityManagerInterface;

#[AsCarrierType]
class GLS extends AbstractCarrierType implements CarrierTypeInterface
{
    public function __construct(
        private readonly GLSService $carrierService,
        private readonly EntityManagerInterface $entityManager
    ) {
        parent::__construct($entityManager);
    }

    private const REQUIRED_FIELDS = [
        [
            'name' => 'GLS_USERNAME',
            'type' => 'text',
            'label' => 'Nazwa użytkownika',
        ],
        [
            'name' => 'GLS_PASSWORD',
            'type' => 'password',
            'label' => 'Hasło',
        ],
        [
            'name' => 'GLS_WSDL_URL',
            'type' => 'text',
            'label' => 'URL WSDL',
        ],
        [
            'name' => 'GLS_SESSIONS_JSON',
            'type' => 'text',
            'label' => 'Sesje JSON',
        ],
    ];

    public function getCarrierType(): string
    {
        return 'DHL';
    }

    public function getRequiredFields(): array
    {
        return self::REQUIRED_FIELDS;
    }

    public function getCarrierProvider(): GLSService
    {
        return $this->carrierService;
    }
}