<?php

namespace App\Core\Carrier\CarrierType;

use App\Core\Attribute\AsCarrierType;
use App\Core\Service\Carrier\Epaka\EpakaService;
use Doctrine\ORM\EntityManagerInterface;

#[AsCarrierType]
class Epaka extends AbstractCarrierType implements CarrierTypeInterface
{

    private CONST REQUIRED_FIELDS = [
        [
            'name' => 'LOGIN',
            'type' => 'text',
            'label' => 'Login do API',
        ],
        [
            'name' => 'PASSWORD',
            'type' => 'password',
            'label' => 'Hasło do API',
        ],
        [
            'name' => 'API_URL',
            'type' => 'text',
            'label' => 'Adres API',
        ],
        [
            'name' => 'NAME',
            'type' => 'text',
            'label' => 'Imię',
        ],
        [
            'name' => 'SURNAME',
            'type' => 'text',
            'label' => 'Nazwisko',
        ],
        [
            'name' => 'COMPANY_NAME',
            'type' => 'text',
            'label' => 'Nazwa firmy',
        ],
        [
            'name' => 'ADDRESS',
            'type' => 'text',
            'label' => 'Adres',
        ],
        [
            'name' => 'STREET',
            'type' => 'text',
            'label' => 'Ulica',
        ],
        [
            'name' => 'HOUSE_NUMBER',
            'type' => 'text',
            'label' => 'Numer domu',
        ],
        [
            'name' => 'APARTMENT_NUMBER',
            'type' => 'text',
            'label' => 'Numer mieszkania',
        ],
        [
            'name' => 'POSTAL_CODE',
            'type' => 'text',
            'label' => 'Kod pocztowy',
        ],
        [
            'name' => 'CITY',
            'type' => 'text',
            'label' => 'Miasto',
        ],
        [
            'name' => 'COUNTRY',
            'type' => 'text',
            'desc' => 'Dwuliterowy Kod ISO kraju',
            'label' => 'Kraj',
        ],
        [
            'name' => 'PHONE',
            'type' => 'tel',
            'label' => 'Telefon',
        ],
        [
            'name' => 'EMAIL',
            'type' => 'email',
            'label' => 'Email',
        ],
        [
            'name' => 'PICKUP_POINT_NUMBER',
            'type' => 'text',
            'label' => 'Numer punktu odbioru',
        ],
        [
            'name' => 'PICKUP_TYPE',
            'type' => 'select',
            'options' => [
                [
                    'value' => 'COURIER',
                    'label' => 'Przyjazd kuriera',
                ],
                [
                    'value' => 'SELF',
                    'label' => 'Dostarczę przesyłkę samodzielnie do punktu odbioru',
                ]
            ],
            'label' => 'Typ odbioru',
        ],
        [
            'name' => 'PICKUP_HOURS_DHL_FROM',
            'type' => 'time',
            'label' => 'Godziny odbioru DHL od',
            'desc' => 'Format HH:MM',
        ],
        [
            'name' => 'PICKUP_HOURS_DHL_TO',
            'type' => 'time',
            'label' => 'Godziny odbioru DHL do',
            'desc' => 'Format HH:MM',
        ],
        [
            'name' => 'PICKUP_HOURS_DPD_FROM',
            'type' => 'time',
            'desc' => 'Format HH:MM',
            'label' => 'Godziny odbioru DPD od',
        ],
        [
            'name' => 'PICKUP_HOURS_DPD_TO',
            'type' => 'time',
            'desc' => 'Format HH:MM',
            'label' => 'Godziny odbioru DPD do',
        ],
        [
            'name' => 'PICKUP_HOURS_FEDEX_FROM',
            'type' => 'time',
            'desc' => 'Format HH:MM',
            'label' => 'Godziny odbioru FEDEX od',
        ],
        [
            'name' => 'PICKUP_HOURS_FEDEX_TO',
            'type' => 'time',
            'desc' => 'Format HH:MM',
            'label' => 'Godziny odbioru FEDEX do',
        ],
        [
            'name' => 'PRINT_TYPE',
            'type' => 'select',
            'options' => [
                [
                    'value' => 'PDF',
                    'label' => 'Etykieta PDF',
                ],
                [
                    'value' => 'ZEBRA',
                    'label'=> 'Etykieta PDF (Zebra)'
                ]
            ],
            'label' => 'Typ wydruku',
        ],
    ];

    public function __construct(
        private readonly EpakaService $carrierService,
        private readonly EntityManagerInterface $entityManager
    ) {
        parent::__construct($entityManager);
    }

    public function getCarrierType(): string
    {
        return 'Epaka';
    }

    public function getRequiredFields(): array
    {
        return self::REQUIRED_FIELDS;
    }

    public function getCarrierProvider(): EpakaService
    {
        return $this->carrierService;
    }
}