<?php

namespace App\Core\Rules;

use App\Core\DTO\Rule\RuleDTO;
use SCA\Rules\Domain\Rule\RuleEngine;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class Validator {

    public function __construct(private RuleEngine $ruleEngine, private ValidatorInterface $validator) {
    }

    public function validateRule(RuleDTO $ruleDTO) {
        return $this->validator->validate($ruleDTO);
    }

    private function validateConditions() {
    }

    private function validateCondition() {
    }

    private function validateActions() {
    }
    private function validateAction() {
    }
}