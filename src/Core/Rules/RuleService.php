<?php

namespace App\Core\Rules;

use App\Core\DTO\Rule\RuleDTO;
use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Taxonomy\DateTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use SCA\Rules\Domain\Action\ActionInterface;
use SCA\Rules\Domain\Condition\ConditionInterface;
use SCA\Rules\Domain\Event\EventInterface;
use SCA\Rules\Domain\Rule\RuleEngine;
use SCA\Rules\Entity\Rule;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

class RuleService {

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly DenormalizerInterface $denormalizer,
        public readonly RuleEngine $ruleEngine,
        private readonly Validator $validator,
        private RuleTrigger $ruleTrigger
    ) {}

    public function createRule(array $data): Rule {
        $ruleDTO = $this->denormalizer->denormalize($data, RuleDTO::class, 'array');
        if (!$this->validateRule($ruleDTO)) {
            return $ruleDTO;
        }
        $ruleEntity = $this->denormalizer->denormalize($ruleDTO, Rule::class);
        $this->entityManager->persist($ruleEntity);
        $this->entityManager->flush();

        return $ruleEntity;
    }

    public function editRule(Rule $ruleEntity, array $data): Rule {
        $ruleDTO = $this->denormalizer->denormalize($data, RuleDTO::class, 'array');
        if (!$this->validateRule($ruleDTO)) {
            return $ruleEntity;
        }
        $ruleEntity = $this->denormalizer->denormalize($ruleDTO, Rule::class, 'array', [AbstractNormalizer::OBJECT_TO_POPULATE => $ruleEntity]);
        $this->entityManager->persist($ruleEntity);
        $this->entityManager->flush();

        return $ruleEntity;
    }

    public function deleteRule(Rule $ruleEntity): bool {
        $this->entityManager->remove($ruleEntity);
        $this->entityManager->flush();

        return true;
    }

    public function readAll(): ?array {
        return $this->entityManager->getRepository(Rule::class)->findAll();
    }

    public function getRuleEventByName(string $eventName): ?EventInterface {
        return $this->ruleEngine->getEventByName($eventName);
    }

    public function getRuleConditionByName(string $ruleCondition): ?ConditionInterface {
        $conditions = $this->ruleEngine->getConditions();

        foreach ($conditions as $condition) {
            if ($condition->getName() === $ruleCondition) {
                return $condition;
            }
        }

        return null;
    }

    public function getRuleActionByName(string $ruleAction): ?ActionInterface {
        $actions = $this->ruleEngine->getActions();

        foreach ($actions as $action) {
            if ($action->getName() === $ruleAction) {
                return $action;
            }
        }

        return null;
    }


    private function validateRule(RuleDTO $ruleDTO): bool {
        $errors = $this->validator->validateRule($ruleDTO);
        if ($errors->count() > 0) {
            $exceptionMessage = '';
            foreach ($errors as $error) {
                $exceptionMessage .= $error->getMessage();
            }

            throw new \Exception($exceptionMessage);
        }

        return true;
    }

    public function checkOrderInStatusTimeRules() {
        $rules = $this->entityManager->getRepository(Rule::class)->findBy(['event' => 'order.status.in.time']);
        foreach ($rules as $rule) {
            foreach ($rule->getConditions() as $condition) {
                    if ($condition['name'] === 'order.status.time.condition') {
                        $status = '';
                        $days = 0;
                        $hours = 0;
                        $minutes = 0;
                        $excludeWeekends = false;
                        foreach ($condition['fields'] as $field) {
                            if ($field['name'] === 'days') {
                                $days = $field['value'];
                            } elseif ($field['name'] === 'excludeWeekends') {
                                $excludeWeekends = $field['value'];
                            } elseif ($field['name'] === 'status') {
                                $status = $field['value'];
                            } elseif ($field['name'] === 'hours') {
                                $hours = $field['value'];
                            } elseif ($field['name'] === 'minutes') {
                                $minutes = $field['value'];
                            }
                        }
                        if ($excludeWeekends) {
                            $dateFrom = DateTaxonomy::substractTime(new \DateTime('now'), $days, $hours, $minutes)->format(DateTaxonomy::DATE_FORMAT);
                        } else {
                            $dateFrom = DateTaxonomy::subtractBusinessTime(new \DateTime('now'), $days, $hours, $minutes)->format(DateTaxonomy::DATE_FORMAT);
                        }
                        $statusEntity = $this->entityManager->getRepository(OrderStatus::class)->find($status);
                        foreach($this->entityManager->getRepository(Order::class)->getOrderByStatusAndDate(['date' => $dateFrom, 'status' => $statusEntity]) as $order) {
                            $this->ruleTrigger->triggerEvent('order.status.in.time', ['order' => $order]);
                        }
                    }
            }
        }

    }
}