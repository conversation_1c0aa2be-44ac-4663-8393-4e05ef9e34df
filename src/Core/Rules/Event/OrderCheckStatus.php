<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderCheckStatus implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.check.status';
    }

    public function getLabel(): string {
        return 'Sprawdź status zamówienia';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}