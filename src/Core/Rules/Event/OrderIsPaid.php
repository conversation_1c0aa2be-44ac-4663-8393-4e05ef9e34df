<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderIsPaid implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.is.paid';
    }

    public function getLabel(): string {
        return 'Zamówienie zostało opłacone w sklepie';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}