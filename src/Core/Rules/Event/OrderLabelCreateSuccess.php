<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderLabelCreateSuccess  implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.label.create.success';
    }

    public function getLabel(): string {
        return 'Pomyślnie utworzono etykietę dla zamówienia';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}