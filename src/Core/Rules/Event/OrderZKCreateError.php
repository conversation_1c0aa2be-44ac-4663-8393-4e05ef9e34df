<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderZKCreateError implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'zk.create.error';
    }

    public function getLabel(): string {
        return 'Błąd przy tworzeniu dokumentu ZK';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}