<?php

namespace App\Core\Rules\Event\Infakt;

use SCA\Rules\Domain\Event\EventInterface;

class OrderInvoiceC<PERSON>Suc<PERSON>  implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.fs.infakt.create.success';
    }

    public function getLabel(): string {
        return 'Poprawne stworzenie faktury w systemie INFAKT';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}