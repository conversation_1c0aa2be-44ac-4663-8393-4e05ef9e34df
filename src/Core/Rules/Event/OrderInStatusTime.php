<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderInStatusTime implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.status.in.time';
    }

    public function getLabel(): string {
        return '<PERSON><PERSON><PERSON><PERSON>ie w statusie dni (sprawdzane co 30 minut)';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}