<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderStatusChange  implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.status.change';
    }

    public function getLabel(): string {
        return 'Zamówienie zmieniło status';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}