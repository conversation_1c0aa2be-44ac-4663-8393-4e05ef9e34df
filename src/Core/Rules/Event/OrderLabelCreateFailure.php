<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderLabelCreateFailure  implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.label.create.failure';
    }

    public function getLabel(): string {
        return 'Błąd tworzenia etykiety dla zamówienia';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}