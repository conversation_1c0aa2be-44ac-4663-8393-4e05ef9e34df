<?php

namespace App\Core\Rules\Event;
use SCA\Rules\Domain\Event\EventInterface;

class OrderFetchedAfterSaveEvent implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.fetched.after.save';
    }

    public function getLabel(): string {
        return 'Pobrano zamówienie - po zapisie';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void
    {
       $this->context = $data;
    }
}