<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderInvoiceCreateFailure  implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.fs.create.success';
    }

    public function getLabel(): string {
        return 'Stworzono dokument FS';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}