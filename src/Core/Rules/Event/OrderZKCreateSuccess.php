<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderZKCreateSuccess implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.zk.create.success';
    }

    public function getLabel(): string {
        return 'Stworzono dokument ZK';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}