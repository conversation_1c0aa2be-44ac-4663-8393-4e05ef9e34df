<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderIsNotPaid implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.is.not.paid';
    }

    public function getLabel(): string {
        return 'Zamówienie sprawdzone i nie opłacone w sklepie';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}