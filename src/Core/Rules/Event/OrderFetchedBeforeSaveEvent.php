<?php

namespace App\Core\Rules\Event;
use SCA\Rules\Domain\Event\EventInterface;

class OrderFetchedBeforeSaveEvent implements EventInterface
{
    private array $context;
    public function getName(): string
    {
        return 'order.fetched.before.save';
    }

    public function getLabel(): string {
        return '<PERSON><PERSON>no zamówienie - przed zapisem (unavailable)';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}