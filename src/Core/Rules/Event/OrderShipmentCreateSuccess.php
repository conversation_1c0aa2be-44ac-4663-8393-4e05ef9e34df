<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderShipmentCreateSuc<PERSON>  implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.shipment.create.success';
    }

    public function getLabel(): string {
        return 'Pomyślnie utworzono przesyłkę dla zamówienia';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}