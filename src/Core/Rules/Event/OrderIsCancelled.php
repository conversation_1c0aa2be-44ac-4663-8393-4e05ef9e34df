<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderIsCancelled implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.is.cancelled';
    }

    public function getLabel(): string {
        return 'Zamówienie zostało anulowane w sklepie';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}