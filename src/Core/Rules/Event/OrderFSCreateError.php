<?php

namespace App\Core\Rules\Event;

use SCA\Rules\Domain\Event\EventInterface;

class OrderFSCreateError implements EventInterface
{
    private array $context;

    public function getName(): string
    {
        return 'order.fs.create.error';
    }

    public function getLabel(): string {
        return 'Błąd przy wystawianiu faktury FS';
    }

    public function getContext(): array {
        return $this->context;
    }

    public function setContext(array $data): void {
        $this->context = $data;
    }
}