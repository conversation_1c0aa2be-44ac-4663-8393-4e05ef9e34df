<?php

namespace App\Core\Rules;

use SCA\Rules\Domain\Rule\RuleEngine;

final readonly class RuleTrigger {

    public function __construct(private RuleEngine $ruleEngine) {}

    public function triggerEvent(string $eventName, array $data): void {
        $event = $this->ruleEngine->getEventByName($eventName);
        if (!$event) {
            return;
        }
        $event->setContext($data);
        $this->ruleEngine->handle($event);
    }
}