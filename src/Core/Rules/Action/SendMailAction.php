<?php

namespace App\Core\Rules\Action;

use App\Core\Entity\EmailAccount;
use App\Core\Entity\EmailTemplate;
use App\Core\Service\EmailEvent\EmailDataParser;
use App\Core\Service\EmailEvent\EmailSender;
use Doctrine\ORM\EntityManagerInterface;
use SCA\Rules\Domain\Action\AbstractAction;
use SCA\Rules\Domain\Action\ActionInterface;

final class SendMailAction extends AbstractAction implements ActionInterface {

    public function __construct(
        private EmailSender $emailSender,
        private EmailDataParser $emailDataParser,
        protected readonly EntityManagerInterface $entityManager,
    ) {}

    public function getName(): string
    {
        return 'sendmail.action';
    }

    public function execute(array $context): void {
        $template = $this->entityManager->getRepository(EmailTemplate::class)->find($context['actionValue']['fields']['template']);
        $sender = $this->entityManager->getRepository(EmailAccount::class)->find($context['actionValue']['fields']['sender']);
        $emails = $context['actionValue']['fields']['recipient'];
        if (in_array('client', $emails)) {
            $clientEmailKey = array_search('client', $emails);
            $clientEmail = $context['order']->getEmail();
            $emails[$clientEmailKey] = $clientEmail;
        }
        $to = implode(', ', $emails);
        $emailContent = $this->emailDataParser->parseData($template->getContent(), $context['order']);
        $this->emailSender->send($emailContent, $sender, $to, $template->getSubject());
    }

    public function getLabel(): string
    {
        return 'Wyślij email';
    }

    public function getAllowedValues(): array
    {
        return [
            'fields' => [
                [
                    'name' => 'template',
                    'type' => 'select',
                    'label' => 'Szablon',
                    'options' => $this->getOptionsForTemplates(),
                ],
                [
                    'name' => 'sender',
                    'type' => 'select',
                    'label' => 'Od:',
                    'options' => $this->getOptionsForSender(),
                ],
                [
                    'name' => 'recipient',
                    'type' => 'selectMultipleTags',
                    'label' => 'Do:',
                    'options' => [
                        [
                            'value' => 'client',
                            'label' => 'Klient',
                        ]
                    ],
                ],
            ]
        ];
    }

    private function getOptionsForSender(): array
    {
        $options = [];
        foreach ($this->getEmailAccounts() as $account) {
            $options[] = [
                'value' => $account->getId()->toRfc4122(),
                'label' => $account->getFullName(),
            ];
        }

        return $options;
    }

    private function getEmailAccounts(): array
    {
        return $this->entityManager->getRepository(EmailAccount::class)->findAll();
    }
    private function getOptionsForTemplates(): array {
        $options = [];
        foreach ($this->getEmailTemplates() as $template) {
            $options[] = [
                'value' => $template->getId()->toRfc4122(),
                'label' => $template->getName(),
            ];
        }

        return $options;
    }
    private function getEmailTemplates(): array
    {
        return $this->entityManager->getRepository(EmailTemplate::class)->findAll();
    }
}