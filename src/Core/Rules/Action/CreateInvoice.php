<?php

namespace App\Core\Rules\Action;

use App\Core\Message\CreateInvoiceMessage;
use Doctrine\ORM\EntityManagerInterface;
use SCA\Rules\Domain\Action\AbstractAction;
use Symfony\Component\Messenger\MessageBusInterface;

final class CreateInvoice extends AbstractAction  {

    public function __construct(
        private MessageBusInterface $messageBus,
        protected readonly EntityManagerInterface $entityManager,
    ) {}

    public function getName(): string
    {
        return 'create.invoice';
    }

    public function execute(array $context): void {
        $message = new CreateInvoiceMessage($context['order']->getId());
        $this->messageBus->dispatch($message);
    }

    public function getLabel(): string
    {
        return 'Stwórz fakturę';
    }

    public function getAllowedValues(): array
    {
        return [
            'fields' => []
        ];
    }

}