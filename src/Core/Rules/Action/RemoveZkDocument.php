<?php

namespace App\Core\Rules\Action;

use App\Core\Rules\RuleTrigger;
use App\Core\Service\Subiekt\Documents\SubiektDocumentsService;
use App\Core\Service\Subiekt\Orders\SubiektOrdersService;
use Doctrine\ORM\EntityManagerInterface;
use SCA\Rules\Domain\Action\AbstractAction;

final class RemoveZkDocument extends AbstractAction {

    public function __construct(
        private SubiektDocumentsService $subiektDocumentsService,
        protected readonly EntityManagerInterface $entityManager,
    ) {}

    public function getName(): string
    {
        return 'remove.zk';
    }

    public function execute(array $context): void {
        $orderEntity = $context['order'];
        try {
            $this->subiektDocumentsService->updateZkReservation($orderEntity, false);
        } catch (\Exception $exception) {

        }
    }

    public function getLabel(): string
    {
        return 'Usuń dokument ZK';
    }

    public function getAllowedValues(): array
    {
        return [
            'fields' => []
        ];
    }

}