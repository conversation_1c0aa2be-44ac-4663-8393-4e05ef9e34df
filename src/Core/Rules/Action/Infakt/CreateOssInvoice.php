<?php

namespace App\Core\Rules\Action\Infakt;

use App\Core\Entity\Order;
use App\Core\Rules\RuleTrigger;
use App\Core\Taxonomy\CountryIsoCodeEnum;
use Doctrine\ORM\EntityManagerInterface;
use SCA\InFakt\Model\OssInvoiceModel;
use SCA\InFakt\Model\OssInvoiceProductModel;

final class CreateOssInvoice extends InfaktAbstractAction {

    public function __construct(RuleTrigger $ruleTrigger, private readonly EntityManagerInterface $entityManager) {
        parent::__construct($ruleTrigger, $entityManager);
    }

    public function getName(): string {
        return 'infakt.create.oss.invoice';
    }

    public function execute(array $context): void {
        /** @var Order $orderEntity */
        $orderEntity = $context['order'];
        $integration = $context['integration'];
        $this->createApiClient($integration);
        try {
            $ossInvoice = new OssInvoiceModel();
            $ossInvoice->country = CountryIsoCodeEnum::getIsoCodeByCountry($orderEntity->getOrderInvoice()->getInvoiceCountry());
            $ossInvoice->clientFirstName = $orderEntity->getOrderInvoice()->getInvoiceFullname();
            $ossInvoice->clientLastName = $orderEntity->getOrderInvoice()->getInvoiceFullname();
            $ossInvoice->clientEmail = $orderEntity->getEmail();
            $ossInvoice->clientStreet = $orderEntity->getOrderInvoice()->getInvoiceAddress();
            $ossInvoice->clientCity = $orderEntity->getOrderInvoice()->getInvoiceCity();
            $ossInvoice->clientPostCode = $orderEntity->getOrderInvoice()->getInvoicePostcode();
            $ossInvoice->saleType = 'merchandise';
            $ossInvoice->servicePlacePrimary = 'customer_country';
            $ossInvoice->currency = $orderEntity->getCurrency();
            $ossInvoice->issueDate = date('Y-m-d');
            $ossInvoice->serviceDate = date('Y-m-d');
            $ossInvoice->paymentDate = $ossInvoice->serviceDate;
            $taxes = $this->getApiClient()->ossTaxRates->getOssTaxRates(CountryIsoCodeEnum::getIsoCodeByCountry($orderEntity->getOrderInvoice()->getInvoiceCountry()));
            foreach ($orderEntity->getProducts() as $product) {
                $ossProduct = new OssInvoiceProductModel();
                $ossProduct->name = $product->getName();
                $ossProduct->grossPrice = $product->getPriceBrutto();
                $ossProduct->quantity = $product->getQuantity();
                $ossProduct->taxRate = $taxes[array_key_first($taxes)]['value'];
                $ossProduct->unit = 'pcs';
                $ossInvoice->services[] = $ossProduct->getAll($ossProduct);
            }
            $errors = $ossInvoice->validate();
            if (count($errors) > 0) {
                throw new \Exception('Validation failed: ' . implode(", ", $errors));
            }

            $invoiceData = ['oss_invoice' => $ossInvoice->getAll($ossInvoice)];

            $createdInvoice = $this->getApiClient()->ossInvoiceModule->create($invoiceData);

            if (isset($response['error']) && $response['error']) {
                throw new \Exception('Failed to create invoice: ' . $response['error']);
            }
            $orderEntity->getOrderInvoice()->setInvoiceNumber($createdInvoice['uuid']);
            $this->entityManager->persist($orderEntity);
            $this->entityManager->flush();
            $this->ruleRrigger->triggerEvent('order.fs.infakt.create.success', ['order' => $orderEntity]);

        } catch (\Exception $exception) {
            $message = $exception->getMessage();
            $this->ruleRrigger->triggerEvent('order.fs.infakt.create.failure', ['order' => $orderEntity, 'message' => $message]);
        }
    }

    public function getLabel(): string
    {
        return 'Stwórz fakturę OSS Infakt';
    }

    public function getAllowedValues(): array
    {
        return [
            'fields' => []
        ];
    }

}