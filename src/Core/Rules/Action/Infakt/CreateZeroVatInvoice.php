<?php

namespace App\Core\Rules\Action\Infakt;

use App\Core\Entity\Order;
use App\Core\Rules\RuleTrigger;
use Doctrine\ORM\EntityManagerInterface;
use SCA\InFakt\Model\CustomerModel;

final class CreateZeroVatInvoice extends InfaktAbstractAction {

    public function __construct(private readonly EntityManagerInterface $entityManager, $ruleRrigger) {
        parent::__construct($ruleRrigger, $this->entityManager);
    }

    public function getName(): string {
        return 'infakt.create.proforma.invoice';
    }

    public function execute(array $context): void {

        /** @var Order $orderEntity */
        $orderEntity = $context['order'];
        $integration = $context['integration'];
        $this->createApiClient($integration);
        try {
            $invoiceData = $this->createInvoice('vat', $orderEntity);
            $this->getApiClient()->vatInvoiceModule->create($invoiceData);
            $this->ruleRrigger->triggerEvent('order.fs.infakt.create.success', ['order' => $orderEntity]);
        } catch (\Exception $exception) {
            $this->ruleRrigger->triggerEvent('order.fs.infakt.create.failure', ['order' => $orderEntity]);
        }
    }

    public function getLabel(): string
    {
        return 'Stwórz fakturę OSS Infakt';
    }

    public function getAllowedValues(): array
    {
        return [
            'fields' => []
        ];
    }

}