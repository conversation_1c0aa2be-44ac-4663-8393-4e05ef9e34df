<?php

namespace App\Core\Rules\Action\Infakt;

use App\Core\Entity\Order;
use App\Core\Rules\RuleTrigger;
use Doctrine\ORM\EntityManagerInterface;

final class CreateVatInvoice extends InfaktAbstractAction {

    public function __construct(RuleTrigger $ruleTrigger, private readonly EntityManagerInterface $entityManager) {
        parent::__construct($ruleTrigger, $entityManager);
    }

    public function getName(): string {
        return 'infakt.create.vat.invoice';
    }

    public function execute(array $context): void {
        /** @var Order $orderEntity */
        $orderEntity = $context['order'];
        $integration = $context['integration'];
        $this->createApiClient($integration);

        try {
            $invoiceData = $this->createInvoice('vat', $orderEntity, $integration);

            $data = $this->getApiClient()->vatInvoiceModule->create($invoiceData);
            if (!isset($data['invoice_task_reference_number'])) {
                throw new \Exception('Failed to create invoice: ' . json_encode($data));
            }
            $status = $this->getApiClient()->vatInvoiceModule->checkStatus($data['invoice_task_reference_number']);
            if (!isset($status['invoice_uuid'])) {
                $error = [
                    'reference_number' => $status['invoice_task_reference_number'],
                    'errors' => json_encode($status['invoice_errors'])
                ];
                throw new \Exception('Failed to create invoice: ' . json_encode($error));
            }
            $invoiceRead = $this->getApiClient()->vatInvoiceModule->read($status['invoice_uuid']);
            if (!isset($invoiceRead['number'])) {
                throw new \Exception('Failed to create invoice: ' . json_encode($status));
            }
            $orderEntity->getOrderInvoice()->setInvoiceNumber($status['invoice_uuid']);
            $this->entityManager->persist($orderEntity);
            $this->entityManager->flush();
            $this->ruleRrigger->triggerEvent('order.fs.infakt.create.success', ['order' => $orderEntity]);
        } catch (\Exception $exception) {
            $this->ruleRrigger->triggerEvent('order.fs.infakt.create.failure', ['order' => $orderEntity]);
        }
    }

    public function getLabel(): string {
        return 'Stwórz fakturę VAT Infakt';
    }

    public function getAllowedValues(): array {
        return [
            'fields' => []
        ];
    }

}