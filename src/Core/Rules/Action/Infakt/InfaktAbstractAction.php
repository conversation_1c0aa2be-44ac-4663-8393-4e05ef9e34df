<?php

namespace App\Core\Rules\Action\Infakt;

use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Rules\RuleTrigger;
use App\Core\Utility\Money;
use Doctrine\ORM\EntityManagerInterface;
use SCA\InFakt\Client\ApiClient;
use SCA\InFakt\Client\Authenticator;
use SCA\InFakt\Infakt;
use SCA\InFakt\Model\VatInvoiceModel;
use SCA\InFakt\Model\VatInvoiceProductModel;

class InfaktAbstractAction {

    private ApiClient $infaktApiClient;

    private Infakt $infakt;

    public function __construct(
        protected RuleTrigger $ruleRrigger,
        private readonly EntityManagerInterface $entityManager
    ) {}

    protected function getApiClient(): ApiClient {
        return $this->infaktApiClient;
    }

    protected function createApiClient(Integration $integration): void {
        $authenticator = new Authenticator($integration->getSettingsByKey('API_TOKEN'));
        $this->infaktApiClient = new ApiClient($authenticator, true);
        $this->infakt = new Infakt($this->infaktApiClient);
    }

    protected function createInvoice(string $type, Order $order, $integration): array {
        $invoice = new VatInvoiceModel();
        $name = explode(' ', $order->getOrderInvoice()->getInvoiceFullname());
        $invoice->recipientSignature = $order->getOrderInvoice()->getInvoiceFullname();
        $invoice->sellerSignature = $integration->getSettingsByKey('SELLER_SIGNATURE');

        $invoice->clientCompanyName = $integration->getSettingsByKey('COMPANY');
        $invoice->clientFirstName = $name[0];
        $invoice->clientLastName = $name[1] ?? $name[0];
        $invoice->clientBusinessActivityKind = 'private_person';
        $invoice->clientStreet = $order->getOrderInvoice()->getInvoiceAddress();
        $invoice->clientStreetNumber = $order->getOrderInvoice()->getInvoiceAddress();
        $invoice->clientFlatNumber = $order->getOrderInvoice()->getInvoiceAddress();
        $invoice->clientCity = $order->getOrderInvoice()->getInvoiceCity();
        $invoice->clientPostCode = $order->getOrderInvoice()->getInvoicePostcode();
        $invoice->clientCountry = $order->getOrderInvoice()->getInvoiceCountry();

        $invoice->saleType = 'merchandise';
        $invoice->taxPrice = (new Money($order->getFullPriceWithDelivery()))->subtract(new Money($order->getFullPriceWithDiscount()))->toFloat();
        $invoice->currency = $order->getCurrency();
        $invoice->kind = $type; // final, proforma, vat
        $invoice->invoiceDate = date('Y-m-d');
        $invoice->paidDate =  date('Y-m-d');
        foreach ($order->getProducts() as $product) {
            $invoiceProduct = new VatInvoiceProductModel();
            $invoiceProduct->name = $product->getName();
            $invoiceProduct->grossPrice = $product->getPriceBrutto();
            $invoiceProduct->quantity = $product->getQuantity();
            $invoiceProduct->taxSymbol = $product->getTaxRate();
            $invoice->services[] = $invoiceProduct->getAll($invoiceProduct);
        }

        return $invoice->getAll($invoice);
    }

}