<?php

namespace App\Core\Rules\Action;

use App\Core\Entity\OrderStatus;
use SCA\Rules\Domain\Action\AbstractAction;

class SetStatusAction extends AbstractAction {

    public function getName(): string {
        return 'set.status.action';
    }

    public function execute(array $context): void {
        $order = $context['order'];
        $newStatus = $this->entityManager->getRepository(OrderStatus::class)->find($context['actionValue']['fields']['status']);
        $currentStatus = $order->getInternalStatusId();
        if ($newStatus->getId()->toRfc4122() !== $currentStatus->getId()->toRfc4122()) {
            $order->setInternalStatusId($newStatus);
        }
        $this->entityManager->persist($context['order']);
        $this->entityManager->flush();
    }

    public function getLabel(): string
    {
        return 'Ustaw status';
    }

    public function getAllowedValues(): array
    {
        $values = [];
        foreach ($this->entityManager->getRepository(OrderStatus::class)->findAll() as $status) {
            $tab = $status->getTab() ? $status->getTab()->getName() : 'Domyślne';
            $values[] =
                [
                    'label' => $status->getName(),
                    'value' => $status->getId()->toRfc4122(),
                    'group' => $tab,
                ];
        }

        return [
            'fields' => [
                [
                    'name' => 'status',
                    'type' => 'select',
                    'label' => 'Status',
                    'options' => $values,
                ]
            ]
        ];
    }
}