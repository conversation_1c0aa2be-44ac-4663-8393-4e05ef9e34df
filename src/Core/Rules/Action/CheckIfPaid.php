<?php

namespace App\Core\Rules\Action;

use App\Core\Entity\Integration;
use App\Core\Scheduler\Message\SingleOrderCheckStatusInPrestaShopMessage;
use Doctrine\ORM\EntityManagerInterface;
use SCA\Rules\Domain\Action\AbstractAction;
use Symfony\Component\Messenger\MessageBusInterface;

final class CheckIfPaid extends AbstractAction  {

    public function __construct(
        private MessageBusInterface $messageBus,
        protected readonly EntityManagerInterface $entityManager,
    ) {}

    public function getName(): string
    {
        return 'order.check.if.paid';
    }

    public function execute(array $context): void {
        $order = $context['order'];
        $integration = $this->entityManager->getRepository(Integration::class)->find($order->getOrderSourceId());
        $message = new SingleOrderCheckStatusInPrestaShopMessage($integration, $order);
        $this->messageBus->dispatch($message);
    }

    public function getLabel(): string
    {
        return 'Sprawdź czy zamówienie jest opłacone';
    }

    public function getAllowedValues(): array
    {
        return [
            'fields' => []
        ];
    }

}