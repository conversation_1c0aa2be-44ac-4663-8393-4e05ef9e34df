<?php

namespace App\Core\Rules\Action;

use App\Core\Rules\RuleTrigger;
use App\Core\Service\Subiekt\Orders\SubiektOrdersService;
use Doctrine\ORM\EntityManagerInterface;
use SCA\Rules\Domain\Action\AbstractAction;

final class CreateZkDocument extends AbstractAction {

    public function __construct(
        private SubiektOrdersService $subiektOrdersService,
        private RuleTrigger $ruleTrigger,
        protected readonly EntityManagerInterface $entityManager,
    ) {}

    public function getName(): string {
        return 'create.zk';
    }

    public function execute(array $context): void {
        $orderEntity = $context['order'];
        try {
            if ($invoiceNo = $this->subiektOrdersService->addOrder($orderEntity)) {
                $orderEntity->getOrderInvoice()->setInvoiceNumber($invoiceNo);
                $this->entityManager->persist($orderEntity);
                $this->entityManager->flush();
                $this->ruleTrigger->triggerEvent('order.zk.create.success', $context);
            } else {
                $this->ruleTrigger->triggerEvent('order.zk.create.error', $context);
            }
        } catch (\Exception $exception) {
            $this->ruleTrigger->triggerEvent('order.zk.create.error', $context);
        }
    }

    public function getLabel(): string
    {
        return 'Stwórz dokument ZK';
    }

    public function getAllowedValues(): array
    {
        return [
            'fields' => []
        ];
    }

}