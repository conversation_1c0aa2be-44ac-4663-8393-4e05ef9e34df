<?php

namespace App\Core\Rules\Condition;

use App\Core\Entity\OrderStatus;

class StatusCondition extends AbstractConditionClass
{
    public function getName(): string
    {
        return 'order.status.condition';
    }

    public function evaluate(array $context): bool {
        $operator = $this->getConditionFieldOperator('status', $context['conditionValue']['fields']);
        $value = $this->getConditionFieldValue('status', $context['conditionValue']['fields']);
        $currentStatus = $context['order']->getInternalStatusId()->getId()->toRfc4122();

        return parent::compare($currentStatus, $value, $operator);
    }

    public function getAllowedOperators(): array
    {
        return [
            'name' => 'operator',
            'type' => 'select',
            'label' => 'Warunek',
            'options' => [
                [
                    'value' => '===',
                    'label' => 'równe',
                ],
                [
                    'value' => '!==',
                    'label' => 'inne',
                ],
            ]
        ];
    }

    public function getSettings(): array {
        return [
            'fields' => $this->getAllowedValues(),
        ];
    }

    public function getAllowedValues(): array {
        $values = [];
        foreach ($this->entityManager->getRepository(OrderStatus::class)->findAll() as $status) {
            $tab = $status->getTab() ? $status->getTab()->getName() : 'Domyślne';
            $values[] =
                [
                    'label' => $status->getName(),
                    'value' => $status->getId()->toRfc4122(),
                    'group' => $tab,
                ];
        }
        return [
            [
                'name' => 'status',
                'label' => 'Status',
                'type' => 'select',
                'options' => $values,
                'allowedOperators' => $this->getAllowedOperators(),
            ]
        ];
    }

    public function getLabel(): string
    {
        return 'Status';
    }
}