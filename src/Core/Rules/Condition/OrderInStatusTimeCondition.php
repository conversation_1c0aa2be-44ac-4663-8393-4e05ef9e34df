<?php

namespace App\Core\Rules\Condition;

use App\Core\Entity\OrderStatus;
use App\Core\Taxonomy\DateTaxonomy;

class OrderInStatusTimeCondition extends AbstractConditionClass
{
    public function getName(): string
    {
        return 'order.status.time.condition';
    }

    public function evaluate(array $context): bool {
        $order = $context['order'];
        $status = $this->getConditionFieldValue('status', $context['conditionValue']['fields']);
        $days = $this->getConditionFieldValue('days', $context['conditionValue']['fields']) ?? 0;
        $minutes = $this->getConditionFieldValue('minutes', $context['conditionValue']['fields']) ?? 0;
        $hours = $this->getConditionFieldValue('hours', $context['conditionValue']['fields']) ?? 0;
        $daysOperator = $this->getConditionFieldOperator('days', $context['conditionValue']['fields']) ?? '<';
        $excludeWeekends = $this->getConditionFieldValue('excludeWeekends', $context['conditionValue']['fields']) ?? false;

        if ($excludeWeekends === 'false') {
            $dateFrom = DateTaxonomy::substractTime(new \DateTime('now'), $days, $hours, $minutes)->format(DateTaxonomy::DATE_FORMAT);
        } else {
            $dateFrom = DateTaxonomy::subtractBusinessTime(new \DateTime('now'), $days, $hours, $minutes)->format(DateTaxonomy::DATE_FORMAT);
        }

        $currentOrderStatusDate = $order->getStatusDate()->format(DateTaxonomy::DATE_FORMAT);
        $dateCondition = parent::compare($currentOrderStatusDate, $dateFrom, '<');
        $statusCondition = parent::compare($order->getInternalStatusId()->getId()->toRfc4122(), $status, '===');

        return $dateCondition && $statusCondition;
    }



    public function getAllowedOperators(): array
    {
        return [
            'name' => 'operator',
            'type' => 'select',
            'label' => 'Warunek',
            'options' => [
                [
                    'value' => '<',
                    'label' => 'więcej niż',
                ],
            ]
        ];
    }

    public function getSettings(): array {
        return [
            'fields' => $this->getAllowedValues(),
        ];
    }

    public function getAllowedValues(): array {
        $values = [];
        foreach ($this->entityManager->getRepository(OrderStatus::class)->findAll() as $status) {
            $tab = $status->getTab() ? $status->getTab()->getName() : 'Domyślne';
            $values[] =
                [
                    'label' => $status->getName(),
                    'value' => $status->getId()->toRfc4122(),
                    'group' => $tab,
                ];
        }
        return [
            [
                'name' => 'days',
                'label' => 'Dni w statusie',
                'type' => 'number',
                'min' => 0,
                'max' => 31,
                'optional' => true,
                'allowedOperators' => $this->getAllowedOperators(),
            ],
            [
                'name' => 'minutes',
                'label' => 'Minuty w statusie',
                'min' => 0,
                'max' => 59,
                'type' => 'number',
                'optional' => true,
                'allowedOperators' => $this->getAllowedOperators(),
            ],
            [
                'name' => 'hours',
                'label' => 'Godziny w statusie',
                'min' => 0,
                'max' => 23,
                'type' => 'number',
                'optional' => true,
                'allowedOperators' => $this->getAllowedOperators(),
            ],
            [
                'name' => 'excludeWeekends',
                'label' => 'Wyklucz soboty i niedziele',
                'type' => 'select',
                'options' => [
                    [
                        'value' => "true",
                        'label' => 'Tak',
                    ],
                    [
                        'value' => "false",
                        'label' => 'Nie',
                    ]
                ]
            ],
            [
                'name' => 'status',
                'label' => 'Status',
                'type' => 'select',
                'options' => $values,
            ]
        ];
    }

    public function getLabel(): string
    {
        return 'Zamówienie dni w statusie';
    }
}