<?php

namespace App\Core\Rules\Condition;

class OrderIsPaid extends AbstractConditionClass
{
    public function getName(): string
    {
        return 'order.is.paid';
    }

    public function evaluate(array $context): bool {
        $operator = $this->getConditionFieldOperator('isPaid', $context['conditionValue']['fields']);
        $value = (bool) $this->getConditionFieldValue('isPaid', $context['conditionValue']['fields']);
        $isPaid = $context['order']->isPaid();

        return parent::compare($isPaid, $value, $operator);
    }

    public function getAllowedOperators(): array
    {
        return [
            'name' => 'operator',
            'type' => 'select',
            'label' => 'Warunek',
            'options' => [
                [
                    'value' => '===',
                    'label' => 'równe',
                ],
                [
                    'value' => '!==',
                    'label' => 'inne',
                ],
            ]
        ];
    }

    public function getSettings(): array {
        return [
            'fields' => $this->getAllowedValues(),
        ];
    }

    public function getAllowedValues(): array {
        return [
            [
                'name' => 'isPaid',
                'label' => 'Zamówienie opłacono',
                'type' => 'select',
                'options' => [
                    [
                        'value' => 'true',
                        'label' => 'Tak',
                    ],
                    [
                        'value' => 'false',
                        'label' => 'Nie'
                    ]
                ],
                'allowedOperators' => $this->getAllowedOperators(),
            ]
        ];
    }

    public function getLabel(): string
    {
        return 'Zamówienie jest opłacone';
    }
}