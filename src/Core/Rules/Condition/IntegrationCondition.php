<?php

namespace App\Core\Rules\Condition;

use App\Core\Entity\Integration;

class IntegrationCondition extends AbstractConditionClass {

    public function getName(): string
    {
        return 'order.integration.condition';
    }

    public function getLabel(): string {
        return 'Warunek integracji - integracja to:';
    }

    public function evaluate(array $context): bool {
        $operator = $this->getConditionFieldOperator('integration', $context['conditionValue']['fields']);
        $requiredValue = $this->getConditionFieldValue('integration', $context['conditionValue']['fields']);
        $value = $context['order']->getOrderSourceId();
        switch ($operator) {
            case '===': {
                return in_array($value, $requiredValue);
            }
            case '!==': {
                return !in_array($value, $requiredValue);
            }
        }
    }

    public function getSettings(): array {
        return [
            'fields' => $this->getAllowedValues(),
        ];
    }
    public function getAllowedOperators(): array
    {
        return [
            'name' => 'operator',
            'type' => 'select',
            'label' => 'Warunek',
            'options' => [
                [
                    'value' => '===',
                    'label' => 'równe',
                ],
                [
                    'value' => '!==',
                    'label' => 'inne',
                ],
            ]
        ];
    }
    public function getAllowedValues(): array {

        $values = [];
        foreach ($this->entityManager->getRepository(Integration::class)->findAll() as $integration) {
            $values[] =
                [
                    'label' => $integration->getName(),
                    'value' => $integration->getId(),
                ];
        }

        return [
            [
                'name' => 'integration',
                'type' => 'selectMultiple',
                'label' => 'Integracja',
                'options' => $values,
                'allowedOperators' => $this->getAllowedOperators(),
            ]
        ];
    }
}