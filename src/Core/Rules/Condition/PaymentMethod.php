<?php

namespace App\Core\Rules\Condition;

class PaymentMethod extends AbstractConditionClass
{
    public function getName(): string
    {
        return 'order.payment.method';
    }

    public function evaluate(array $context): bool {
        $value = $this->getConditionFieldValue('paymentMethod', $context['conditionValue']['fields']);
        $currentPaymentMethod = $context['order']->getPaymentMethod();

        return in_array($currentPaymentMethod, $value);
    }

    public function getAllowedOperators(): array
    {
        return [
            'name' => 'operator',
            'type' => 'select',
            'label' => 'Warunek',
            'options' => [
                [
                    'value' => '===',
                    'label' => 'równe',
                ],
                [
                    'value' => '!==',
                    'label' => 'inne',
                ],
            ]
        ];
    }

    public function getSettings(): array {
        return [
            'fields' => $this->getAllowedValues(),
        ];
    }

    public function getAllowedValues(): array {
        return [
            [
                'name' => 'paymentMethod',
                'label' => 'Sposób płatności',
                'type' => 'selectMultipleTags',
                'options' => []
            ]
        ];
    }

    public function getLabel(): string
    {
        return 'Sposób płatności';
    }
}