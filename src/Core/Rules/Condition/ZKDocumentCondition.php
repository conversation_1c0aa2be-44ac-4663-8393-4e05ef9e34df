<?php

namespace App\Core\Rules\Condition;

use App\Core\Entity\OrderStatus;

class ZKDocumentCondition extends AbstractConditionClass
{
    public function getName(): string
    {
        return 'zk.document.exists';
    }

    public function evaluate(array $context): bool {
        $operator = $this->getConditionFieldOperator('exists', $context['conditionValue']['fields']);
        $value = (bool) $this->getConditionFieldValue('exists', $context['conditionValue']['fields']);
        $order = $context['order'];

        return parent::compare($order->checkIfZKExists(), $value, $operator);
    }

    public function getAllowedOperators(): array
    {
        return [
            'name' => 'operator',
            'type' => 'select',
            'label' => 'Warunek',
            'options' => [
                [
                    'value' => '===',
                    'label' => 'równe',
                ],
                [
                    'value' => '!==',
                    'label' => 'inne',
                ],
            ]
        ];
    }

    public function getSettings(): array {
        return [
            'fields' => $this->getAllowedValues(),
        ];
    }

    public function getAllowedValues(): array {
        return [
            [
                'name' => 'exists',
                'label' => 'Status posiadaniach dokumentu ZK:',
                'type' => 'select',
                'options' => [
                    [
                        'value' => 'true',
                        'label' => 'Tak',
                    ],
                    [
                        'value' => 'false',
                        'label' => 'Nie',
                    ]
                ],
                'allowedOperators' => $this->getAllowedOperators(),
            ]
        ];
    }

    public function getLabel(): string
    {
        return 'Czy posiada dokument ZK';
    }
}