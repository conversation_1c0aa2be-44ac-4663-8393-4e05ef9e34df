<?php

namespace App\Core\Rules\Condition;

use SCA\Rules\Domain\Condition\AbstractCondition;

abstract class AbstractConditionClass extends AbstractCondition {

    public function getConditionFieldValue(string $fieldName, array $ruleConditionData): mixed {
        foreach ($ruleConditionData as $fieldData) {
            if ($fieldData['name'] === $fieldName) {
                return $fieldData['value'];
            }
        }

        return null;
    }

    public function getConditionFieldOperator(string $fieldName, array $ruleConditionData): mixed {
        foreach ($ruleConditionData as $fieldData) {
            if ($fieldData['name'] === $fieldName) {
                return $fieldData['operator'];
            }
        }

        return null;
    }
}