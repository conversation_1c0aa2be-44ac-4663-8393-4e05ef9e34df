<?php

namespace App\Core\Rules\Condition;

use App\Core\Entity\OrderStatus;

class FSDocumentCondition extends AbstractConditionClass
{
    public function getName(): string
    {
        return 'fs.document.exists';
    }

    public function evaluate(array $context): bool {
        $operator = $this->getConditionFieldOperator('exists', $context['conditionValue']['fields']);
        $value = (bool) $this->getConditionFieldValue('exists', $context['conditionValue']['fields']);
        $order = $context['order'];

        $fs = $order->checkIfFSExists();
        return parent::compare($fs, $value, $operator);
    }

    public function getAllowedOperators(): array
    {
        return [
            'name' => 'operator',
            'type' => 'select',
            'label' => 'Warunek',
            'options' => [
                [
                    'value' => '===',
                    'label' => 'równe',
                ],
                [
                    'value' => '!==',
                    'label' => 'inne',
                ],
            ]
        ];
    }

    public function getSettings(): array {
        return [
            'fields' => $this->getAllowedValues(),
        ];
    }

    public function getAllowedValues(): array {
        return [
            [
                'name' => 'exists',
                'label' => 'Czy posiada fakturę:',
                'type' => 'select',
                'options' => [
                    [
                        'value' => 'true',
                        'label' => 'Tak',
                    ],
                    [
                        'value' => 'false',
                        'label' => 'Nie',
                    ]
                ],
                'allowedOperators' => $this->getAllowedOperators(),
            ]
        ];
    }

    public function getLabel(): string
    {
        return 'Czy posiada fakturę';
    }
}