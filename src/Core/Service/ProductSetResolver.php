<?php

namespace App\Core\Service;

use App\Core\Entity\Order;
use App\Core\Entity\OrderProduct;
use App\Core\Service\OrderStatusService;
use App\Core\Service\View\Order\OrderProductImageService;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;

class ProductSetResolver {

    private array $productsChanged = [];
    private array $newProducts = [];

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly SubiektApi $subiektApi,
        private readonly OrderStatusService $orderStatusService,
        private readonly OrderProductImageService $orderProductImageService) {}

    public function schedulerCheckProductsSet(): array {
        $orders = $this->entityManager->getRepository(Order::class)->findBy(['internal_status_id' => $this->orderStatusService->getStatus(OrderTaxonomy::STATUS_NEW)]);
        foreach ($orders as $order) {
            $products = $order->getProducts();
            foreach ($products as $product) {
                $this->checkIfAProductIsASet($product);
            }
            $order->setInternalStatusId($this->orderStatusService->getStatus(OrderTaxonomy::STATUS_AFTER_SET_CHECK));
            $this->entityManager->persist($order);
        }
        $this->entityManager->flush();

        return ['Products Set Resolver' => 'Products Set Resolver Message:', 'productsChanged' => 'productsChanged ' . count($this->productsChanged), 'newProducts ' => 'newProducts ' . count($this->newProducts)];
    }

    public function checkProductSetForOrder(Order $order): void {
        $products = $order->getProducts();
        foreach ($products as $product) {
            $this->checkIfAProductIsAset($product);
        }
        $order->setInternalStatusId($this->orderStatusService->getStatus(OrderTaxonomy::STATUS_VERIFIED));
        $this->entityManager->persist($order);
        $this->entityManager->flush();
    }

    public function checkIfAProductIsASet(OrderProduct $orderProduct) {
        $result = $this->subiektApi->makeRequest($orderProduct->getSku());
        if ('fail' === $result['state']) {
            return false;
        }

        if (empty($result['data'][0])) {
            return false;
        }
        if (null === reset($result['data'][0])['sku']) {
            return false;
        }

        //TODO Dostosować do wzorca money!!!
        $this->productsChanged[] = $orderProduct->getId();
        $productQuantityCount = $orderProduct->getQuantity();
        $productPrice = $orderProduct->getPriceBrutto();
        $apiProducts = $result['data'][0];
        $productCount = 0;
        $order = $orderProduct->getOrderId();
        array_map(function($product) use (&$productCount){
            $productCount += (int) $product['quantity'];
        } , $apiProducts);
        $priceForMainProduct = ($productPrice - ($productCount - 1) * 0.01);
        $newProductsArray = [];

        $minVatProduct = $this->findProductByMinValueByKey($apiProducts, 'vat');

        foreach ($apiProducts as $key => $apiProduct) {
            if ($apiProduct['ean'] === $minVatProduct['ean']) {
                unset($apiProducts[$key]);

                break;
            }
        }
        array_unshift($apiProducts, $minVatProduct);

        foreach ($apiProducts as $key => $product) {
            $newApiProduct = clone $orderProduct;
            if (0 === $key) {
                $this->createMainProduct($newApiProduct, $newProductsArray, $product, $priceForMainProduct, $productQuantityCount);
            } else {
                $this->createProduct($newApiProduct, $newProductsArray, $product, $productQuantityCount);
            }
        }
        $order->removeProduct($orderProduct);
        foreach ($newProductsArray as $newProduct) {
            $order->addProduct($newProduct);
            $this->newProducts[] = $newProduct->getId();
        }
        $this->entityManager->persist($order);
        $this->entityManager->flush();

        return true;
    }

    private function createMainProduct(OrderProduct $orderProduct, array &$newProductsArray, array $apiProduct, $priceForMainProduct, $productQuantityCount): void {
        if (1 < $apiProduct['quantity']) {
            $mainProduct = clone $orderProduct;
            $secondMainProduct = clone $orderProduct;

            $mainProduct->setQuantity($productQuantityCount * 1);
            $mainProduct->setPriceBrutto($priceForMainProduct);
            $mainProduct->setSku($apiProduct['sku']);
            $mainProduct->setEan($apiProduct['ean']);
            $mainProduct->setName($apiProduct['name']);
            $mainProduct->setCoverImageUrl($this->orderProductImageService->getCoverImage($mainProduct));

            $secondMainProduct->setQuantity($productQuantityCount * ($apiProduct['quantity'] - 1));
            $secondMainProduct->setPriceBrutto(1);
            $secondMainProduct->setSku($apiProduct['sku']);
            $secondMainProduct->setEan($apiProduct['ean']);
            $secondMainProduct->setName($apiProduct['name']);
            $secondMainProduct->setCoverImageUrl($mainProduct->getCoverImageUrl());

            $newProductsArray[] = $mainProduct;
            $newProductsArray[] = $secondMainProduct;

        } else {
            $orderProduct->setQuantity($productQuantityCount * $apiProduct['quantity']);
            $orderProduct->setSku($apiProduct['sku']);
            $orderProduct->setEan($apiProduct['ean']);
            $orderProduct->setName($apiProduct['name']);
            $orderProduct->setPriceBrutto($priceForMainProduct);
            $orderProduct->setCoverImageUrl($this->orderProductImageService->getCoverImage($orderProduct));
            $newProductsArray[] = $orderProduct;
        }
    }

    private function createProduct(OrderProduct $orderProduct, array &$newProductsArray, array $apiProduct, $productQuantityCount): void {
        $orderProduct->setPriceBrutto(1);
        $orderProduct->setQuantity($productQuantityCount * $apiProduct['quantity']);
        $orderProduct->setSku($apiProduct['sku']);
        $orderProduct->setEan($apiProduct['ean']);
        $orderProduct->setName($apiProduct['name']);
        $orderProduct->setCoverImageUrl($this->orderProductImageService->getCoverImage($orderProduct));
        $newProductsArray[] = $orderProduct;
    }

    function findProductByMinValueByKey(array $array, $key): array
    {
        $product = [];
        $minValue = null;
        foreach ($array as $subArray) {
            if (isset($subArray[$key])) {
                if ($minValue === null || $subArray[$key] < (float) $minValue) {
                    $minValue = (float) $subArray[$key];
                    $product = $subArray;
                }
            }
        }

        return $product;
    }
}