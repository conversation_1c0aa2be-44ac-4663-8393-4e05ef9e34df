<?php

namespace App\Core\Service;

use App\Core\Entity\BasketOrder;
use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Entity\Product;
use App\Core\Entity\ProductReservation;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;

readonly class ReservationService {

    public function __construct(
        private EntityManagerInterface $entityManager,
        private Security $security
    ) {}

    public function completeReservationByBasketOrder(BasketOrder $basketOrder): bool {
        $order = $basketOrder->getOrder();
        foreach ($basketOrder->getBasketOrderProducts() as $basketOrderProduct) {
            $orderProduct = $basketOrderProduct->getOrderProductId();
            $product = $this->entityManager->getRepository(Product::class)->findOneBy(['sku' => $orderProduct->getSku()]);
            $reservations = $this->entityManager->getRepository(ProductReservation::class)->findBy(['id_order' => $order, 'product' => $product, 'status' => 'reserved']);
            foreach ($reservations as $reservation) {
                $reservation->setStatus('completed');
                $reservation->setUserCompleted($this->security->getUser());
                $reservation->setDateCompleted(new \DateTime());
                $esq = $reservation->getEanShelfQuantity();
                $esq->setQuantity($esq->getQuantity() - $reservation->getQuantity());
                $this->entityManager->persist($esq);
                $this->entityManager->persist($reservation);
            }
        }
        $this->entityManager->flush();

        return TRUE;
    }

    public function unReserveOrder (Order $order, $status = false): bool {
        $reservations = $this->entityManager->getRepository(ProductReservation::class)->findBy(['id_order' => $order]);
        foreach ($reservations as $reservation) {
            $esq = $reservation->getEanShelfQuantity();
            $esq->setVirtualQuantity($esq->getVirtualQuantity() + $reservation->getQuantity());

            $this->entityManager->persist($esq);
            $this->entityManager->remove($reservation);
        }
        if($status){
            $status = $this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_VERIFIED]);
            $order->setInternalStatusId($status);
            $this->entityManager->persist($order);
        }
        $this->entityManager->flush();

        return TRUE;
    }
}