<?php

namespace App\Core\Service;

use App\Core\Entity\Log;
use DateTimeZone;
use Doctrine\ORM\EntityManagerInterface;
use Monolog\Handler\AbstractProcessingHandler;
use Monolog\Level;
use Monolog\LogRecord;
use Symfony\Component\DependencyInjection\Attribute\AsAlias;

#[AsAlias(id: 'core.database_logger', public: true)]
class DatabaseLogHandler extends AbstractProcessingHandler {

    public function __construct(private readonly EntityManagerInterface $entityManager, int|string|Level $level = Level::Debug, bool $bubble = true) {
        parent::__construct($level, $bubble);
    }

    protected function write(LogRecord $record): void
    {
        $log = new Log();
        $log->setMessage($record['message'] ?? null);
        $log->setLevel($record['level'] ?? null);
        $log->setLevelName($record['level_name'] ?? null);
        $log->setExtra($record['extra'] ?? null);
        $log->setUser($record['context']['user'] ?? null);
        $log->setEntityType($record['context']['entity_type'] ?? null);
        $log->setEntityId($record['context']['entity_id'] ?? null);
        $log->setAction($record['context']['action'] ?? null);
        $log->setContext($record['context'] ?? null);
        $log->setCreatedAt(new \DateTime('now', new DateTimeZone('Europe/Warsaw')));
        $this->entityManager->persist($log);
        $this->entityManager->flush();
    }
}