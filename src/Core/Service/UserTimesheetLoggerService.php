<?php

namespace App\Core\Service;

use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class UserTimesheetLoggerService {

    private const URL_ACTION_COMPLETED = 'https://praca.swiatsupli.pl/webwallpaper/completed?idBaselinker=%s&userBaselinker=%s';
    private const URL_ACTION_PACKED = 'https://praca.swiatsupli.pl/webwallpaper/packed?idBaselinker=%s&userBaselinker=%s';
    private const URL_ACTION_ADD = 'https://praca.swiatsupli.pl/webwallpaper/add?idBaselinker=%s&userBaselinker=%s';
    public function __construct(private readonly HttpClientInterface $httpClient) { }
    public function actionCompleted(UserInterface $user, string $orderId): void {
        $this->httpClient->request('GET', $this->createUrl($user, $orderId, self::URL_ACTION_COMPLETED));
    }

    public function actionPacked(UserInterface $user, string $orderId): void {
        $this->httpClient->request('GET', $this->createUrl($user, $orderId, self::URL_ACTION_PACKED));
    }

    public function actionAdd(UserInterface $user, string $orderId): void {
        $this->httpClient->request('GET', $this->createUrl($user, $orderId, self::URL_ACTION_ADD));
    }

    private function createUrl(UserInterface $user, string $orderId, string $url): string {
        return sprintf($url, $orderId, $user->getFullname());
    }
}