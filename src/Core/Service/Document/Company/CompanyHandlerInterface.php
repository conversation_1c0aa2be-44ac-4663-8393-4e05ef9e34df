<?php

namespace App\Core\Service\Document\Company;

use App\Core\Entity\DocumentSellerCompany;

interface CompanyHandlerInterface
{
    public function matchesCompany(array $data): bool;

    public function getCompanyName(): string;

    public function getCompanyData(array $xml): array;

    public function handleFile(array $fileData): array;

    public function getCompanyObject(): ?DocumentSellerCompany;
}