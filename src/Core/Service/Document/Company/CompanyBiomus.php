<?php

namespace App\Core\Service\Document\Company;

use App\Core\Service\Document\Company\AbstractCompanyHandler;

class CompanyBiomus extends AbstractCompanyHandler {
    protected string $companyName = 'Biomus sp. z o.o.';

    public function matchesCompany(array $data): bool
    {
        if (isset($data['invoice']['seller_information']['fv_seller'])) {
            return str_starts_with($data['invoice']['seller_information']['fv_seller'], $this->companyName);
        }

        return false;
    }

    public function getCompanyData(array $fileData): array
    {
        return $this->companyDataB($fileData);
    }
}