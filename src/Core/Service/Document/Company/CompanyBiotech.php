<?php

namespace App\Core\Service\Document\Company;

use App\Core\Service\Document\Company\AbstractCompanyHandler;

class CompanyBiotech extends  AbstractCompanyHandler {
    protected string $companyName = 'BioTech USA Polska sp. z.o.o.';

    public function matchesCompany(array $data): bool
    {
        return isset($data['C_CUSTOMER_ORDER_IVC_HU_REP']['COMPANY_NAME'])
            && (string) $data['C_CUSTOMER_ORDER_IVC_HU_REP']['COMPANY_NAME'] === $this->companyName;
    }

    public function getCompanyData(array $fileData): array
    {
        return $this->companyDataD($fileData);
    }
}