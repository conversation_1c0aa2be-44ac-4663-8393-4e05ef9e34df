<?php

namespace App\Core\Service\Document\Company;

use App\Core\Service\Document\Company\AbstractCompanyHandler;

class CompanyHealthLabs extends AbstractCompanyHandler {

    protected string $companyName = 'HEALTH LABS CARE SPÓŁKA AKCYJNA (dawniej Health Labs Care Sp. z o.o. Sp. k.)';

    public function matchesCompany(array $data): bool
    {
        return isset($data['Invoice-Parties']['Seller']['Name'])
            && (string) $data['Invoice-Parties']['Seller']['Name'] === $this->companyName;
    }

    public function getCompanyData(array $fileData): array
    {
        return $this->companyDataA($fileData);
    }
}