<?php

namespace App\Core\Service\Document\Company;

use App\Core\Service\Document\Company\AbstractCompanyHandler;

class CompanyPharmovit extends AbstractCompanyHandler {

    protected string $companyName = 'PHARMOVIT DYSTRYBUCJA SP. Z O.O.';

    public function matchesCompany(array $data): bool
    {
        return isset($data['DOKUMENT']['NAGLOWEK']['SPRZEDAWCA']['NAZWA'])
            && (string) $data['DOKUMENT']['NAGLOWEK']['SPRZEDAWCA']['NAZWA'] === $this->companyName;
    }

    public function getCompanyData(array $fileData): array
    {
        return $this->companyDataE($fileData);
    }
}