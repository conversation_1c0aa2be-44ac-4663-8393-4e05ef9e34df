<?php

namespace App\Core\Service\Document\Company;

use App\Core\Entity\DocumentSellerCompany;
use App\Core\Entity\DocumentType;
use App\Core\Service\Document\Company\CompanyHandlerInterface;
use App\Core\Service\Document\Company\DataExtractorTrait;
use App\Core\Service\Document\DocumentService;
use App\Core\Service\Document\DocumentTypeService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag('document.company.detector')]
abstract class AbstractCompanyHandler implements CompanyHandlerInterface {

    use DataExtractorTrait;
    protected string $companyName;

    public function __construct(protected readonly EntityManagerInterface $entityManager, protected readonly DocumentService $documentService) {}

    public function handleFile(string|array $filePath): array
    {
        $data = $this->getCompanyData($filePath);
        $document = $this->documentService->add($data, $this->getCompanyObject());

        return [
            'processed' => true,
            'details' => [
                'document' => $document,
                'company_name' => $this->companyName,
                'other_data' => 'Specific processing details for Company A',
            ],
        ];
    }

    protected function getDocumentType(): string {
        return $this->entityManager->getRepository(DocumentType::class)->findOneBy(['name' => 'Faktura Zakupowa'])->getId()->toRfc4122();
    }

    protected function companyDataA(array $fileData): array {
        $data = [
            'document_number_external' => $fileData['Invoice-Header']['InvoiceNumber'] ?? '',
            'type' => $this->getDocumentType(),
            'status' => 'Stworzony',
        ];
        $documentItems = [];

        foreach ($fileData['Invoice-Lines']['Line'] as $documentLine) {
            $line = $documentLine['Line-Item'];
            $documentItem = [
                'quantity_required' => (int) $line['InvoiceQuantity'],
                'ean13' => $line['EAN'],
                'reference' => '',
                'unit_price_netto' => (float) $line['InvoiceUnitNetPrice'],
                'unit_price_brutto' => round((float) $line['InvoiceUnitNetPrice'] + ((float) $line['InvoiceUnitNetPrice'] / 100 * (float) $line['TaxRate']) , 2),
                'total_price_netto' => (float) $line['NetAmount'],
                'total_price_brutto' => (float) $line['NetAmount'] + (float) $line['TaxAmount'],
                'tax' => (float) $line['TaxRate'],
                'expiration_date' => null,
                'quantity_completed' => 0,
                'is_in_subiekt' => false,
                'sample' => false,
                'name' => $line['ItemDescription']
            ];

            $documentItems[] = $documentItem;
        }
        $data['documentItems'] = $documentItems;

        return $data;
    }

    protected function companyDataB(array $fileData): array {
        $data = [
            'document_number_external' => $fileData['invoice']['invoice_number'] ?? '',
            'type' => $this->getDocumentType(),
            'status' => 'Stworzony',
        ];
        $documentItems = [];

        foreach ($fileData['invoice']['order_items']['order_item'] as $line) {
            $documentItem = [
                'quantity_required' => (int) $line['order_item_quantity'],
                'ean13' => $line['ean'] ?? '',
                'reference' => '',
                'unit_price_netto' => $this->calculatePriceNetto((float) $line['order_item_price'], (float) $line['order_item_vat']),
                'unit_price_brutto' => (float) $line['order_item_price'],
                'total_price_netto' => (float) $fileData['invoice']['total_price_netto'],
                'total_price_brutto' => (float) $fileData['invoice']['total_price_brutto'],
                'tax' => (float) $fileData['invoice']['tax'],
                'expiration_date' => null,
                'quantity_completed' => 0,
                'is_in_subiekt' => false,
                'name' => $line['order_item_auction_name']
            ];

            $documentItems[] = $documentItem;
        }
        $data['documentItems'] = $documentItems;

        return $data;
    }

    public function companyDataC(array $fileData): array {
        $data = [
            'document_number_external' => $this->findFieldValue('Tytul', $fileData) ?? '',
            'type' => $this->getDocumentType(),
            'status' => 'Stworzony',
        ];
        $documentItems = [];

        foreach ($fileData['FormattedAreaPair']['FormattedAreaPair']['FormattedAreaPair'] as $documentLine) {
            $documentItem = [
                'quantity_required' => (int) $this->findFieldValue('z1Iloscf1', $documentLine),
                'ean13' => $this->findFieldValue('EAN', $documentLine) ?? '',
                'reference' => '',
                'unit_price_netto' => (float) $this->findFieldValue('z1WartNettoZRabf1', $documentLine),
                'unit_price_brutto' => (float) $this->findFieldValue('z1WartBruttoZRabf1', $documentLine),
                'total_price_netto' => round(((float) $this->findFieldValue('z1WartNettoZRabf1', $documentLine) * (int) $this->findFieldValue('z1Iloscf1', $documentLine)), 2),
                'total_price_brutto' => round(((float) $this->findFieldValue('z1WartBruttoZRabf1', $documentLine) * (int) $this->findFieldValue('z1Iloscf1', $documentLine)), 2),
                'tax' => (float) $this->findFieldValue('z1StawkaVATf1', $documentLine),
                'expiration_date' => null,
                'quantity_completed' => 0,
                'is_in_subiekt' => false,
                'name' => $this->findFieldValue('z1NazwaLubOpisf1', $documentLine)
            ];

            $documentItems[] = $documentItem;
        }
        $data['documentItems'] = $documentItems;

        return $data;
    }

    public function companyDataD(array $fileData): array {
        $data = [
            'document_number_external' => $fileData['C_CUSTOMER_ORDER_IVC_HU_REP']['INVOICE_REFERENCE'] ?? '',
            'type' => $this->getDocumentType(),
            'status' => 'Stworzony',
        ];

        $documentItems = [];

        foreach ($fileData['C_CUSTOMER_ORDER_IVC_HU_REP']['ORDER_ITEMS']['ORDER_ITEM']['INVOICE_LINES']['INVOICE_LINE'] as $line) {
            if (is_array($line['C_EAN'])) {
                continue;
            }
            $documentItem = [
                'quantity_required' => (int) $line['PRICE_QTY'],
                'ean13' => $line['C_EAN'],
                'reference' => '',
                'unit_price_netto' => (float) $line['SALE_UNIT_PRICE'],
                'unit_price_brutto' => (float) $line['PRICE_INCL_TAX'],
                'total_price_netto' => (float) $line['NET_CURR_AMOUNT'],
                'total_price_brutto' => round((float) $line['PRICE_INCL_TAX_TOTAL'], 2),
                'tax' => (float) $line['LINE_VAT_PERCENT'],
                'expiration_date' => null,
                'quantity_completed' => 0,
                'is_in_subiekt' => false,
                'name' => $line['CATALOG_DESC']
            ];

            $documentItems[] = $documentItem;
        }
        $data['documentItems'] = $documentItems;

        return $data;
    }

    protected function companyDataComarch(array $fileData): array {
        $data = [
            'document_number_external' => $fileData['DOKUMENT']['NAGLOWEK']['NUMER_PELNY'] ?? '',
            'type' => $this->getDocumentType(),
            'status' => 'Stworzony',
        ];

        $documentItems = [];

        foreach ($fileData['DOKUMENT']['POZYCJE']['POZYCJA'] as $line) {
            $documentItem = [
                'quantity_required' => (int) $line['ILOSC'],
                'ean13' => $line['TOWAR']['EAN'],
                'reference' => '',
                'unit_price_netto' => (float) $line['WARTOSC_NETTO'] / (int) $line['ILOSC'],
                'unit_price_brutto' => (float) $line['CENY']['PO_RABACIE_WAL_CENNIKA'],
                'total_price_netto' => (float) $line['WARTOSC_NETTO'],
                'total_price_brutto' => (float) $line['WARTOSC_BRUTTO'],
                'tax' => (float) $line['STAWKA_VAT']['STAWKA'],
                'expiration_date' => null,
                'quantity_completed' => 0,
                'is_in_subiekt' => false,
                'name' => $line['TOWAR']['NAZWA']
            ];

            $documentItems[] = $documentItem;
        }
        $data['documentItems'] = $documentItems;

        return $data;
    }

    //This is Comarch Optima too, but different version
    protected function companyDataE(array $fileData): array {
        $data = [
            'document_number_external' => $fileData['DOKUMENT']['NAGLOWEK']['NUMER_PELNY'] ?? '',
            'type' => $this->getDocumentType(),
            'status' => 'Stworzony',
        ];

        $documentItems = [];

        foreach ($fileData['DOKUMENT']['POZYCJE']as $line) {
            $documentItem = [
                'quantity_required' => (int) $line['TOWAR']['STAWKA_VAT']['ILOSC'],
                'ean13' => $line['TOWAR']['EAN'],
                'reference' => '',
                'unit_price_netto' => (float) $line['TOWAR']['STAWKA_VAT']['CENY']['PO_RABACIE_WAL_CENNIKA'],
                'unit_price_brutto' => (float) $line['TOWAR']['STAWKA_VAT']['CENY']['PO_RABACIE_WAL_CENNIKA'] + ($line['TOWAR']['STAWKA_VAT']['CENY']['PO_RABACIE_WAL_CENNIKA'] / 100 * $line['TOWAR']['STAWKA_VAT']['STAWKA']),
                'total_price_netto' => (float) $line['TOWAR']['STAWKA_VAT']['WARTOSC_NETTO'],
                'total_price_brutto' => (float) $line['TOWAR']['STAWKA_VAT']['WARTOSC_BRUTTO'],
                'tax' => (float) $line['TOWAR']['STAWKA_VAT']['STAWKA'],
                'expiration_date' => null,
                'quantity_completed' => 0,
                'is_in_subiekt' => false,
                'name' => $line['TOWAR']['NAZWA']
            ];

            $documentItems[] = $documentItem;
        }
        $data['documentItems'] = $documentItems;

        return $data;
    }

    public function getCompanyName(): string
    {
        return $this->companyName;
    }

    public function getCompanyObject(): ?DocumentSellerCompany
    {
        return $this->entityManager->getRepository(DocumentSellerCompany::class)->findOneBy(['companyName' => $this->companyName]);
    }
}