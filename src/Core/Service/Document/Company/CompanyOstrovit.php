<?php

namespace App\Core\Service\Document\Company;

use App\Core\Service\Document\Company\AbstractCompanyHandler;

class CompanyOstrovit extends AbstractCompanyHandler {

    protected string $companyName = 'OSTROVIT SPÓŁKA Z OGRANICZONĄ  ODPOWIEDZIALNOŚCIĄ';

    public function matchesCompany(array $data): bool
    {
        return isset($data['DOKUMENT']['NAGLOWEK']['SPRZEDAWCA']['NAZWA'])
            && (string) $data['DOKUMENT']['NAGLOWEK']['SPRZEDAWCA']['NAZWA'] === $this->companyName;
    }

    public function getCompanyData(array $fileData): array
    {
        return $this->companyDataComarch($fileData);
    }
}