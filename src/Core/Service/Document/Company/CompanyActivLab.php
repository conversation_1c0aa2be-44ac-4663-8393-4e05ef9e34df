<?php

namespace App\Core\Service\Document\Company;

use App\Core\Service\Document\Company\AbstractCompanyHandler;

class CompanyActivLab extends AbstractCompanyHandler {

    protected string $companyName = 'REGIS SP Z OO';

    public function matchesCompany(array $data): bool
    {
        return isset($data['Invoice-Parties']['Seler']['Name'])
            && (string) $data['Invoice-Parties']['Seler']['Name'] === $this->companyName;
    }

    public function getCompanyData(array $fileData): array
    {
        return $this->companyDataA($fileData);
    }
}