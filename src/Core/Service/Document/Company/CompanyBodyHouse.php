<?php

namespace App\Core\Service\Document\Company;

use App\Core\Service\Document\Company\AbstractCompanyHandler;

class CompanyBodyHouse extends AbstractCompanyHandler {

    protected string $companyName = 'Body House Sp. z o.o.';

    public function matchesCompany(array $data): bool
    {
        return $this->getCompanyName() === $this->findFieldValue('FldNazwaPelnaPodmiotu', $data);
    }

    public function getCompanyData(array $fileData): array
    {
        return $this->companyDataC($fileData);
    }
}