<?php

namespace App\Core\Service\Document\Company;

use App\Core\Service\Document\Company\AbstractCompanyHandler;

class CompanyLabOne extends AbstractCompanyHandler {

    protected string $companyName = 'Lab One Sp. z o.o.';

    public function matchesCompany(array $data): bool
    {
        return isset($data['Invoice-Parties']['Seller']['Name'])
            && (string) $data['Invoice-Parties']['Seller']['Name'] === $this->companyName;
    }

    public function getCompanyData(array $fileData): array
    {
        return $this->companyDataA($fileData);
    }
}