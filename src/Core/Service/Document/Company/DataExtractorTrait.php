<?php

namespace App\Core\Service\Document\Company;

trait DataExtractorTrait {
    public function findFieldValue($fieldName, $data) {
        foreach ($data as $key => $element) {
            if ('ObjectName' === $key && is_string($element) && $fieldName === $element) {
                if (isset($data['Value'])) {
                    return $data['Value'];
                }
            }
            if (is_array($element)) {
                if (($result = $this->findFieldValue($fieldName, $element)) !== NULL) {
                    return $result;
                }
            }
        }

        return NULL;
    }

    public function calculatePriceNetto(float $bruttoPrice, float $tax): float {
        return round(($bruttoPrice * 100) / (100 + $tax), 2);
    }
}