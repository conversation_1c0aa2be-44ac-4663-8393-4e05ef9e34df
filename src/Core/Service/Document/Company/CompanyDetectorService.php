<?php

namespace App\Core\Service\Document\Company;

use App\Core\Entity\DocumentSellerCompany;
use App\Core\Service\Document\Company\CompanyHandlerInterface;
use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;

class CompanyDetectorService
{
    private iterable $handlers;

    public function __construct(#[AutowireIterator('document.company.detector')] iterable $handlers)
    {
        $this->handlers = $handlers;
    }

    public function detectCompany(array $companyData): ?DocumentSellerCompany
    {
        foreach ($this->handlers as $handler) {
            if ($handler instanceof CompanyHandlerInterface) {
                if ($handler->matchesCompany($companyData)) {
                    return $handler->getCompanyObject();
                }
            }
        }

        return NULL;
    }

    public function handleFile(array $dataArray): array
    {
        foreach ($this->handlers as $handler) {
            if ($handler instanceof CompanyHandlerInterface) {
                if($handler->matchesCompany($dataArray)) {
                    return $handler->handleFile($dataArray);
                }
            }
        }

        throw new \RuntimeException('Company not identified or no handler available for this XML');
    }
}