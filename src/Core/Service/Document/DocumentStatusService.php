<?php

namespace App\Core\Service\Document;

use App\Core\Entity\DocumentStatus;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Serializer\SerializerInterface;

class DocumentStatusService
{
    public function __construct(
        private EntityManagerInterface $entityManager, private readonly SerializerInterface $serializer
    ) {
    }

    public function add(string $name): array {
        $checkStatus = $this->entityManager->getRepository(DocumentStatus::class)->findOneBy(['name' => $name]);
        if($checkStatus){
            return [
                'code' => 400,
                'message' => 'Status already exists'
            ];
        }
        $status = new DocumentStatus();
        $status->setName($name);
        $this->entityManager->persist($status);
        $this->entityManager->flush();
        return [
            'code' => 201,
            'message' => 'Status created'
        ];
    }

    public function list(): array {
        return $this->entityManager->getRepository(DocumentStatus::class)->findAll();
    }

    public function get($id) {
        return $this->entityManager->getRepository(DocumentStatus::class)->find($id);
    }

    public function edit($status, $data): array {
        try {
            $this->serializer->deserialize(
                json_encode($data),
                DocumentStatus::class,
                'json',
                ['object_to_populate' => $status]
            );
            $this->entityManager->persist($status);
            $this->entityManager->flush();
            return ['message' => 'Status updated', 'code' => 200];
        } catch (Exception $e) {
            return ['message' => 'Error updating status: ' . $e->getMessage(), 'code' => 500];
        }
    }

    public function delete($status): array{
        try {
            $this->entityManager->remove($status);
            $this->entityManager->flush();
            return ['message' => 'Status deleted', 'code' => 200];
        } catch (Exception $e) {
            return ['message' => 'Error deleting status', 'code' => 500];
        }
    }

}