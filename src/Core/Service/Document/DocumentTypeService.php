<?php

namespace App\Core\Service\Document;

use App\Core\Entity\DocumentStatus;
use App\Core\Entity\DocumentType;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Serializer\SerializerInterface;

class DocumentTypeService {

    public function __construct(
        private EntityManagerInterface $entityManager,
        private readonly SerializerInterface $serializer
    ){}

    public function list() {
        return $this->entityManager->getRepository(DocumentType::class)->findAll();
    }

    public function add($data): array {
        try {
            $checkStatus = $this->entityManager->getRepository(DocumentType::class)->findOneBy(['name' => $data['name']]);
            if($checkStatus){
                return ['message' => 'Document type already exists', 'code' => 400];
            }
            $documentType = new DocumentType();
            $documentType->setName($data['name']);
            $documentType->setPrefix($data['prefix']);
            $this->entityManager->persist($documentType);
            $this->entityManager->flush();
            return ['message' => 'Document type created', 'code' => 201];
        } catch (Exception $e) {
            return ['message' => 'Error creating document type', 'code' => 500];
        }
    }

    public function get($id) {
        return $this->entityManager->getRepository(DocumentType::class)->find($id);
    }

    public function edit($status, $data): array {
        try {
            $this->serializer->deserialize(
                json_encode($data),
                DocumentType::class,
                'json',
                ['object_to_populate' => $status]
            );
            $this->entityManager->persist($status);
            $this->entityManager->flush();
            return ['message' => 'Status updated', 'code' => 200];
        } catch (Exception $e) {
            return ['message' => 'Error updating status: ' . $e->getMessage(), 'code' => 500];
        }
    }

    public function delete($status): array{
        try {
            $this->entityManager->remove($status);
            $this->entityManager->flush();
            return ['message' => 'Status deleted', 'code' => 200];
        } catch (Exception $e) {
            return ['message' => 'Error deleting status', 'code' => 500];
        }
    }
}