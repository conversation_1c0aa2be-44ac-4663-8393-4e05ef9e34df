<?php

namespace App\Core\Service\Document;

use App\Core\Entity\DocumentSellerCompany;
use Doctrine\ORM\EntityManagerInterface;

class DocumentSellerCompanyService {
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
    ) {}

    public function add($data){

        try {
            $documentSellerCompany = new DocumentSellerCompany();
            $documentSellerCompany->setCompanyName($data['companyName']);
            $documentSellerCompany->setInternalName($data['internalName']);
            $documentSellerCompany->setActive($data['active'] ?? false);
            $documentSellerCompany->setFormat($data['format']);

            $this->entityManager->persist($documentSellerCompany);
            $this->entityManager->flush();

            return [
                'success' => true,
                'code' => 201,
                'message' => 'Document seller company created successfully',
            ] ;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'code' => 500,
                'message' => 'Error creating document seller company',
            ];
        }

    }

    public function edit($id, $data){
        try {
            $documentSellerCompany = $this->entityManager->getRepository(DocumentSellerCompany::class)->find($id);
            $documentSellerCompany->setCompanyName($data['companyName']);
            $documentSellerCompany->setInternalName($data['internalName']);
            $documentSellerCompany->setActive($data['active']);
            $documentSellerCompany->setFormat($data['format']);

            $this->entityManager->flush();

            return [
                'success' => true,
                'code' => 200,
                'message' => 'Document seller company updated successfully',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'code' => 500,
                'message' => 'Error updating document seller company',
            ];
        }


    }

    public function delete($id){
        try {
            $documentSellerCompany = $this->entityManager->getRepository(DocumentSellerCompany::class)->find($id);

            $this->entityManager->remove($documentSellerCompany);
            $this->entityManager->flush();

            return [
                'success' => true,
                'code' => 200,
                'message' => 'Document seller company deleted successfully',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'code' => 500,
                'message' => 'Error deleting document seller company',
            ];
        }
    }

    public function switchOnOff($id){
        $documentSellerCompany = $this->entityManager->getRepository(DocumentSellerCompany::class)->find($id);
        $documentSellerCompany->setActive(!$documentSellerCompany->isActive());

        $this->entityManager->flush();

        return $documentSellerCompany;
    }

    public function get($id){
        $object = $this->entityManager->getRepository(DocumentSellerCompany::class)->find($id);
        if (!$object) {
            return [
                'success' => false,
                'code' => 404,
                'message' => 'Document seller company not found',
            ];
        }
        return[
            'success' => true,
            'code' => 200,
            'message' => 'Document seller company found',
            'data' => $object,
        ];
    }

    public function list() {
        return[
            'success' => true,
            'code' => 200,
            'message' => 'Document seller company list',
            'data' => $this->entityManager->getRepository(DocumentSellerCompany::class)->findAll(),
        ];
    }
}