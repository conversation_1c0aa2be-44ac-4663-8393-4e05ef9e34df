<?php

namespace App\Core\Service\Document;

use App\Core\Entity\Document;
use App\Core\Entity\DocumentItem;
use App\Core\Entity\DocumentSellerCompany;
use App\Core\Entity\DocumentStatus;
use App\Core\Entity\DocumentType;
use App\Core\Service\Subiekt\SubiektApiService;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;

readonly class DocumentService {

    public function __construct(
        private DocumentItemService $documentItemService,
        private EntityManagerInterface $entityManager,
        private SerializerInterface $serializer,
        private DenormalizerInterface $denormalizer,
        private SubiektApiService $subiektApiService,
    ) {
    }

    public function generateNumber(DocumentType $documentType): string {
        $currentYear = (int) (new DateTime())->format('Y');
        $currentMonth = (int) (new DateTime())->format('m');
        $documentCount = $this->entityManager->getRepository(Document::class)->countDocumentsByTypeAndMonth($documentType, $currentYear, $currentMonth);
        $incrementalNumber = $documentCount + 1;

        return sprintf('%s/%d/%s/%s', $documentType->getPrefix(), $incrementalNumber, $currentMonth, $currentYear);
    }

    public function add(array $data, DocumentSellerCompany $sellerCompany): Document|bool {
        try {
            $type = $this->entityManager->getRepository(DocumentType::class)->find($data
            ['type']);
            $status = $this->entityManager->getRepository(DocumentStatus::class)->findOneBy(['name' => 'Stworzony']);
            $data['document_number_internal'] = $this->generateNumber($type);
            $document = $this->denormalizer->denormalize($data, Document::class);
            $document->setType($type);
            $document->setStatus($status);
            $document->setSellerCompany($sellerCompany);
            foreach ($document->getDocumentItems() as &$documentItem) {
                $this->documentItemService->refresh($documentItem);
            }

            $this->entityManager->persist($document);
            $this->entityManager->flush();

            return $document;

        } catch (Exception $e) {
            return false;
        }
    }

    public function list(): array {
        return $this->entityManager->getRepository(Document::class)->findAll();
    }

    public function get(string $id): Document|array
    {
        $document = $this->entityManager->getRepository(Document::class)->find($id);
        if (!$document) {
            return ['message' => 'Document not found', 'code' => 404];
        }

        $this->updateDocumentItemsWithEans($document);

        return $document;
    }

    private function updateDocumentItemsWithEans(Document $document): void
    {
        $products = $document->getDocumentItems();

        foreach ($products as $product) {
            if ($product->getEans() || !$product->getReference()) {
                continue;
            }

            try {
                $apiResponse = $this->getEans($product->getReference());


                if ($apiResponse) {
                    $this->updateProductEans($product, $apiResponse);
                }
            } catch (\Exception $exception) {
            }
        }

        $this->entityManager->flush();
    }

    private function getEans(string $productCode): array
    {
        $response = $this->subiektApiService->getEans($productCode);

        if ($this->isApiResponseSuccessful($response)) {
            return $response['data']['eans'] ?? [];
        }

        return [];
    }

    private function isApiResponseSuccessful(array $apiResponse): bool
    {
        return 'success' === $apiResponse['status'];
    }

    private function updateProductEans(DocumentItem $product, array $eans): void
    {
        $productEan13 = $product->getEan13();

        if (!in_array($productEan13, $eans, true)) {
            $eans[] = $productEan13;
        }

        $product->setEans($eans);
        $this->entityManager->persist($product);
    }

    public function edit($status, $data): array {
        try {
            $this->serializer->deserialize(
                json_encode($data),
                Document::class,
                'json',
                ['object_to_populate' => $status]
            );
            $this->entityManager->persist($status);
            $this->entityManager->flush();
            return ['message' => 'Status updated', 'code' => 200];
        } catch (Exception $e) {
            return ['message' => 'Error updating status: ' . $e->getMessage(), 'code' => 500];
        }
    }

    public function delete($status): array{
        try {
            $this->entityManager->remove($status);
            $this->entityManager->flush();
            return ['message' => 'Status deleted', 'code' => 200];
        } catch (Exception $e) {
            return ['message' => 'Error deleting status', 'code' => 500];
        }
    }
}