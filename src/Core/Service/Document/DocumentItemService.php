<?php

namespace App\Core\Service\Document;

use App\Core\Entity\Document;
use App\Core\Entity\DocumentItem;
use App\Core\Service\Subiekt\Products\SubiektProductsService;
use App\Core\Service\Subiekt\SubiektApiService;
use Doctrine\ORM\EntityManagerInterface;
use <PERSON>ymfony\Component\Serializer\SerializerInterface;

class DocumentItemService {
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly SerializerInterface $serializer,
        private readonly SubiektProductsService $subiektProductService,
        private readonly SubiektApiService $subiektApiService,
    ) {
    }

    public function refresh(DocumentItem &$documentItem) {
        $subiekt = $this->subiektProductService->findProductByEan($documentItem->getEan13());
        if('success' === $subiekt['status'] && isset($subiekt['data']) ){
            $documentItem->setIsInSubiekt(true);
            $documentItem->setReference($subiekt['data']['code']);
        } else {
            $documentItem->setIsInSubiekt(false);
        }
    }
    public function add(Document $documentId, $data) {
        $documentItem = new DocumentItem();
        $this->serializer->deserialize(json_encode($data), DocumentItem::class, 'json', [
            'object_to_populate' => $documentItem
        ]);
        if(!isset($data['quantity'])){
            $documentItem->setQuantityRequired(0);
        }
        $subiekt = $this->subiektProductService->findProductByEan($documentItem->getEan13());
        if('success' === $subiekt['status'] && isset($subiekt['data']['code']) ){
            $documentItem->setIsInSubiekt(true);
            $documentItem->setReference($subiekt['data']['code']);
            $documentItem->setName($subiekt['data']['name']);
        } else {
            $documentItem->setIsInSubiekt(false);
        }
        if ($documentId->addDocumentItem($documentItem)){
            $this->entityManager->persist($documentItem);
            $this->entityManager->flush();
            return ['status' => 'success', 'message' => 'Document item added'];
        }

        return ['status' => 'error', 'message' => 'Document item not added'];

    }

    public function list(Document $document) {
        return $document->getDocumentItems();

    }

    public function edit(DocumentItem $documentItemId, $data) {
        $this->serializer->deserialize(json_encode($data), DocumentItem::class, 'json', [
            'object_to_populate' => $documentItemId
        ]);
        if(isset($data['isInSubiekt']) && $data['isInSubiekt']){
            $this->assignEans($documentItemId, [$documentItemId->getEan13()]);
        }
        $this->entityManager->persist($documentItemId);
        $this->entityManager->flush();
        return ['status' => 'success', 'message' => 'Document item edited'];
    }

    public function delete(Document $documentId, DocumentItem $documentItemId) {
        if($documentId->removeDocumentItem($documentItemId)){
            $this->entityManager->remove($documentItemId);
            $this->entityManager->flush();
            return ['status' => 'success', 'message' => 'Document item deleted'];
        }
        return ['status' => 'error', 'message' => 'Document item not deleted'];
    }

    public function getProducts($chars) {
        return $this->subiektProductService->listByChars($chars);
    }

    public function get($documentItemId) {
        $item = $this->entityManager->getRepository(DocumentItem::class)->find($documentItemId);
        if($result = $this->subiektProductService->get($item->getReference())){
            return ['status' => 'success', 'message' => 'Product found', 'data' => $result];
        }
        return ['status' => 'error', 'message' => 'Product not found'];
    }

    public function assignEans(DocumentItem $documentItemId, array $eans): array {
        $result = $this->subiektProductService->assignEans($documentItemId, $eans);
        if ('success' === $result['status'] && 'success' === $result['data']['status']) {
            $eans = $this->getEans($documentItemId->getReference());
            if(!empty($eans)){
                $documentItemId->setEans($eans);
            }
            $this->entityManager->persist($documentItemId);
            $this->entityManager->flush();
        }
        return $this->subiektProductService->assignEans($documentItemId, $eans);
    }

    private function getEans(string $productCode): array
    {
        $response = $this->subiektApiService->getEans($productCode);

        if ($this->isApiResponseSuccessful($response)) {
            return $response['data']['eans'] ?? [];
        }

        return [];
    }

    private function isApiResponseSuccessful(array $apiResponse): bool
    {
        return 'success' === $apiResponse['status'];
    }
}