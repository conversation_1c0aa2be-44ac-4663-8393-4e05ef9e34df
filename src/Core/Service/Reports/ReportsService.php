<?php

namespace App\Core\Service\Reports;

use App\Core\Entity\Order;
use Doctrine\ORM\EntityManagerInterface;

class ReportsService {

    public function __construct(
        private readonly EntityManagerInterface $entityManager
    ) {}

    public function getPackedIn24($dateFrom, $dateTo): array{
        if (!$this->isValidDate($dateFrom) || !$this->isValidDate($dateTo)) {
            return ['error' => 'Invalid Date Format: dateFrom and dateTo should be in YYYY-MM-DD format'];
        }
        $orders = $this->entityManager->getRepository(Order::class)->countOrdersByShipmentTime( $dateFrom,  $dateTo);

        $within24h = 0;
        $after24h = 0;
        $after48h = 0;

        foreach ($orders as $order) {
            $dateAdd = (new \DateTime())->setTimestamp($order['date_add']);
            $shipmentDate = (new \DateTime())->setTimestamp(strtotime($order['shipment_date']));

            $effectiveHours = $this->calculateEffectiveHours($dateAdd, $shipmentDate);
            if ($effectiveHours <= 24) {
                $within24h++;
            } elseif ($effectiveHours <= 48) {
                $after24h++;
            } else {
                $after48h++;
            }

        }

        return [
            'status' => 'success',
            'data' => [
                'within24h' => $within24h,
                'after24h' => $after24h,
                'after48h' => $after48h
            ]
        ];
    }

    private function isValidDate(string $date): bool
    {
        $d = \DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    private function calculateEffectiveHours(\DateTime $start, \DateTime $end): int {
        $totalHours = 0;

        while ($start < $end) {
            if ($start->format('N') >= 6) {
                $start->modify('next Monday midnight');
                continue;
            }

            $totalHours++;
            $start->modify('+1 hour');
        }

        return $totalHours;
    }

}