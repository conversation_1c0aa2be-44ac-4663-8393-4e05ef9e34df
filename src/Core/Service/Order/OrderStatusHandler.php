<?php

namespace App\Core\Service\Order;

use App\Core\Entity\Integration;
use App\Core\Entity\Log;
use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Message\ChangePrestashopStatusMessenger;
use App\Core\Service\LoggerService;
use App\Core\Service\OrderStatusService;
use App\Core\Service\Subiekt\Documents\SubiektDocumentsService;
use App\Core\Service\Subiekt\Orders\SubiektOrdersService;
use App\Core\Taxonomy\CommunicatorTaxonomy;
use App\Core\Taxonomy\DatabaseLoggerActionTaxonomy;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class OrderStatusHandler {
    public const PRESTASHOP_SENT_STATUS_ID = 4;
    public const PRESTASHOP_CANCELLED_STATUS_ID = 6;
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly OrderStatusService     $orderStatusService,
        private readonly LoggerService          $loggerService,
        private readonly SubiektDocumentsService $subiektDocumentsService,
        private readonly MessageBusInterface $messageBus,
    ) {}

    public function handleOrderStatus($action): void {
        $this->{$action}();
    }

    public function readyToSendToSent(): void {
        $statusToSend = $this->orderStatusService->getStatus(OrderTaxonomy::STATUS_TO_SEND);
        $orders = $this->entityManager->getRepository(Order::class)->findOrdersWithInvoicesAndDeliveryData($statusToSend,'FS %');
        /*** @var Order $order */
        $ordersIds = [];
        foreach ($orders as $order) {
            $ordersIds[] = $order->getOrderId();

            $shipment = false;
            $orderShipemnt = $order->getOrderDelivery()->getOrderDeliveryShipmentData();
            foreach ($orderShipemnt as $shipment) {
                if($shipment->isLabel()){
                    $shipment = true;
                    break;
                }
            }
            if($shipment) {
                $ordersIds[] = $order->getId();
                $order->setInternalStatusId($this->orderStatusService->getStatus(OrderTaxonomy::STATUS_SENT));
                $this->entityManager->persist($order);
            }

        }
        $this->entityManager->flush();
    }

    public function readyToSendToNoInvoice(): void {
        $statusToSend = $this->orderStatusService->getStatus(OrderTaxonomy::STATUS_TO_SEND);
        $orders = $this->entityManager->getRepository(Order::class)->findOrdersWithInvoicesAndDeliveryData($statusToSend,'ZK %',200);
        $ordersIds = [];
        foreach ($orders as $order) {
            $ordersIds[] = $order->getOrderId();

            $shipment = false;
            $orderShipemnt = $order->getOrderDelivery()->getOrderDeliveryShipmentData();
            foreach ($orderShipemnt as $shipment) {
                if($shipment->isLabel()){
                    $shipment = true;
                    break;
                }
            }
            if($shipment) {
                $ordersIds[] = $order->getId();
                $order->setInternalStatusId($this->orderStatusService->getStatus(OrderTaxonomy::STATUS_INVOICE_ERROR));
                $this->entityManager->persist($order);
            }

        }
        $this->entityManager->flush();
    }

    public function handleStatusNew(Order $order, UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }
    public function defaultHandleStatus(Order $order, UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }
    public function handleUnpaidStatus(Order $order, UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }
    public function handleCollectionReceivedStatus(Order $order, UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        if('prestashop' === $integration->getType()) {
            $this->handlePrestastopOrderStatusChange($order, $integration, 4);
        }
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }
    public function handleUnpaid3DaysStatus(Order $order, UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }
    public function handleRemovedFromBasketStatus(Order $order, UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleAfterCreateZKStatus(Order $order, UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleAfterSetCheckStatus(Order $order, UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleZKErrorStatus(Order $order, UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleAfterContactWithClientStatus(Order $order, UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleNewStatus(Order $order, UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus): void {
        $this->loggerService->orderChangeStatus($order,$user, $oldStatus, $newStatus);
    }

    public function handleVerifiedStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleMissingToCompleteStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleCompletingStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleInBasketStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handlePackingStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleToSendStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleSentStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        if('prestashop' === $integration->getType()) {
            $this->handlePrestastopOrderStatusChange($order, $integration, self::PRESTASHOP_SENT_STATUS_ID);
        }

        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleLabelErrorStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleInvoiceErrorStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleEmailErrorStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleClientContactStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handleIncompleteToCheckStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handlePersonalPickupStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function handlePersonalCancelledStatus(Order $order,UserInterface $user, OrderStatus $oldStatus, OrderStatus $newStatus, Integration $integration): Log {

        if('prestashop' === $integration->getType()) {
            $this->handlePrestastopOrderStatusChange($order, $integration, self::PRESTASHOP_CANCELLED_STATUS_ID);
        }

        try {
            $this->subiektDocumentsService->updateZkReservation($order, false);
        } catch (\Exception $e) {

        }
        return $this->logStatusChange($order, $user, $oldStatus, $newStatus);
    }

    public function logStatusChange($order, $user, $oldStatus, $newStatus): Log {
        $log = new Log();
        $log->setMessage('Zamówienie zmieniło status');
        $log->setLevel(200);
        $log->setLevelName('INFO');
        $log->setExtra(null);
        $log->setUser($user);
        $log->setEntityType(get_class($order));
        $log->setEntityId($order->getId());
        $log->setAction(DatabaseLoggerActionTaxonomy::ORDER_CHANGE_STATUS);
        $log->setCreatedAt(new \DateTime('now', new \DateTimeZone('Europe/Warsaw')));
        $context = [
            'entity_id' => $order->getId(),
            'entity_type' => get_class($order),
            'user' => $user,
            'action' => DatabaseLoggerActionTaxonomy::ORDER_CHANGE_STATUS,
            'order_status_prev_id' => $oldStatus->getId(),
            'order_status_new_id' => $newStatus->getId(),
            'order_status_prev_name' => $oldStatus->getName(),
            'order_status_new_name' => $newStatus->getName(),
        ];
        $log->setContext($context);

        return $log;
    }

    public function handlePrestastopOrderStatusChange(Order $order, Integration $integration, $newStatusId) {
        $message = new ChangePrestashopStatusMessenger($order->getId(), $integration->getId(), $newStatusId);
        $this->messageBus->dispatch($message);
    }

}