<?php

namespace App\Core\Service\Order;

use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Repository\OrderRepository;
use App\Core\Service\Carrier\CarrierService;
use App\Core\Taxonomy\OrderFilterTaxonomy;
use Doctrine\ORM\EntityManagerInterface;


class OrderFilterService {

    public function __construct(
        private readonly OrderRepository        $orderRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly CarrierService $carrierService,
    ){}

    public function filterOrders(array $filters, int $offset, int $limit, ?string $orderBy, ?string $orderByOrder, ?bool $orWhere): array
    {
        $ordersQueryBuilder = $this->orderRepository->createFilteredQueryBuilder($filters,$orWhere);

        $countQueryBuilder = clone $ordersQueryBuilder;
        $totalOrders = $this->orderRepository->getTotalOrdersCount($countQueryBuilder);

        if ($orderBy) {
            $ordersQueryBuilder->orderBy("o.$orderBy", $orderByOrder);
        }
        $ordersQueryBuilder->setFirstResult($offset)
            ->setMaxResults($limit);

        $orders = $ordersQueryBuilder->getQuery()->getResult();

        return [
            'totalOrders' => $totalOrders,
            'orders' => $orders
        ];
    }


    public function getAllFilters(): array {
        $filterArray = OrderFilterTaxonomy::FILTER_ARRAY;

        foreach ($filterArray as &$filter) {
            if ($filter['field'] === 'internal_status_id' && $filter['type'] === 'select') {
                $options = $this->entityManager->getRepository(OrderStatus::class)->findAll();
                $filter['options'] = array_map(function($f) {
                    return [
                        'value' => $f->getId(),
                        'label' => $f->getName()
                    ];
                }, $options);
            } elseif ($filter['field'] === 'orderDelivery_delivery_method' && $filter['type'] === 'select') {
                $filters = $this->carrierService->getCarrierTypes();
                $filter['options'] = array_map(function($f) {
                    return [
                        'value' => $f,
                        'label' => $f
                    ];
                }, $filters);
            } elseif ($filter['field'] === 'payment_method' && $filter['type'] === 'select') {
                $paymentMethods = $this->entityManager->getRepository(Order::class)->getDistinctOneByField('payment_method');
                $filter['options'] = array_map(function($f) {
                    return [
                        'value' => $f['payment_method'],
                        'label' => $f['payment_method']
                    ];
                }, $paymentMethods);
            } elseif ($filter['field'] === 'order_source_id' && $filter['type'] === 'select') {
                $orderSourceId = $this->entityManager->getRepository(Integration::class)->findAll();
                $filter['options'] = array_map(function($f) {
                    return [
                        'value' => $f->getId(),
                        'label' => $f->getName()
                    ];
                }, $orderSourceId);
            } else if($filter['field'] === 'payment_method_cod' && $filter['type'] === 'select') {
                $filter['options'] = [
                    ['value' => false, 'label' => 'Nie'],
                    ['value' => true, 'label' => 'Tak']
                ];
            }
        }

        return $filterArray;
    }

    public function getAllAvailableFieldsFilterNames(): array {
        return array_map(function($filter) {
           return $filter['field'];
        }, OrderFilterTaxonomy::FILTER_ARRAY);
    }

    public function getAcceptedFilters($requestedFilters): array {
        $availableFilters = $this->getAllAvailableFieldsFilterNames();

        return array_filter($requestedFilters, function($filterKey) use ($availableFilters) {
            return in_array($filterKey, $availableFilters);
        }, ARRAY_FILTER_USE_KEY);
    }
}
