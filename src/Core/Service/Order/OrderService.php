<?php

namespace App\Core\Service\Order;

use App\Core\Entity\Order;
use App\Core\Entity\ProductReservation;
use App\Core\Entity\Storehouse;
use App\Core\Service\OrderStatusService;
use App\Core\Service\Subiekt\Products\SubiektProductsService;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;

class OrderService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly SubiektProductsService
        $subiektProductService,
    ) {}

    public function removeOrder(Order $order):bool|array {
        try {
                $reservations = $this->entityManager->getRepository(ProductReservation::class)->findBy(['id_order' => $order->getId()]);
                if ($reservations) {
                    foreach ($reservations as $reservation) {
                        $eanShelf = $reservation->getEanShelfQuantity();
                        $eanShelf->setVirtualQuantity($eanShelf->getVirtualQuantity() + $reservation->getQuantity());
                        $this->entityManager->persist($eanShelf);
                        $this->entityManager->remove($reservation);
                    }
                }

            $this->entityManager->remove($order);
            $this->entityManager->flush();

            return true;
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function checkIfAdditionalDocument(Order $order): array {
        $warehousePacking = $this->entityManager->getRepository(Storehouse::class)->findOneBy(['internal_name' => 'Pakownia']);
        $warehouseShop = $this->entityManager->getRepository(Storehouse::class)->findOneBy(['internal_name' => 'Sklep']);


        if(!$warehousePacking || !$warehouseShop) {
            return [
                'status' => 'error',
                'message' => 'Warehouse not found'
            ];
        }

        if(!$warehousePacking->getIdExternal() && !$warehouseShop->getIdExternal()) {
            return [
                'status' => 'error',
                'message' => 'Warehouse dont have external ID'
            ];
        }

        $orderClone = clone $order;
        $products = [];
        $requiredQuantities = [];

        foreach ($order->getProducts() as $product) {

            $requiredQuantities[$product->getSku()] = $product->getQuantity();
            $products[] = $product->getSku();
        }

        $stocks = $this->subiektProductService->getQuantityInWarehouse($products, [$warehousePacking->getIdExternal(), $warehouseShop->getIdExternal()]);


        $stocks = $stocks['content'];

        $resultFromMag8 = [];

        foreach ($requiredQuantities as $sku => $neededQuantity) {
            $availableIn4 = 0;
            $availableIn8 = 0;

            if (isset($stocks[$sku])) {
                foreach ($stocks[$sku]['warehouse'] as $warehouse) {
                    if ($warehouse['id'] == $warehousePacking->getIdExternal()) {
                        $availableIn4 = $warehouse['Dostepne'];
                    }
                    if ($warehouse['id'] == $warehouseShop->getIdExternal()) {
                        $availableIn8 = $warehouse['Dostepne'];
                    }
                }
            }

            if ($neededQuantity <= $availableIn4) {
                continue;
            } else {
                $remainingQuantity = $neededQuantity - $availableIn4;

                if ($remainingQuantity > 0) {
                    if ($remainingQuantity <= $availableIn8) {
                        $resultFromMag8[$sku] = $remainingQuantity;
                    } else {
                        return [
                            'status' => 'error',
                            'message' => 'Not enough products in warehouse',
                            'needAdditionalDocument' => false,
                            'data' => [
                                'sku' => $sku,
                                'needed' => $neededQuantity,
                                'availableIn4' => $availableIn4,
                                'availableIn8' => $availableIn8
                            ]
                        ];
                    }
                }
            }
        }

        foreach ($orderClone->getProducts() as $product) {
            $sku = $product->getSku();
            if (isset($resultFromMag8[$sku])) {
                $product->setQuantity($resultFromMag8[$sku]);
            } else {
                $orderClone->removeProduct($product);
            }
        }

        if ($resultFromMag8) {
            return [
                'status' => 'success',
                'needAdditionalDocument' => true,
                'message' => 'Additional document needed',
                'order' => $orderClone
            ];
        } else {
            return [
                'status' => 'success',
                'needAdditionalDocument' => false,
                'message' => 'No additional document needed'
            ];
        }
    }

}