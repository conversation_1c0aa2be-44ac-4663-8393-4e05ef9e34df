<?php

namespace App\Core\Service\Basket;

use App\Core\Entity\Basket;
use App\Core\Entity\BasketOrder;
use App\Core\Entity\BasketOrderProduct;
use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Entity\User;
use App\Core\Service\Communicator\CommunicatorService;
use App\Core\Service\LoggerService;
use App\Core\Service\OrderStatusService;
use App\Core\Service\ReservationService;
use App\Core\Service\Subiekt\Documents\SubiektDocumentsService;
use App\Core\Taxonomy\BasketOrderProductTaxonomy;
use App\Core\Taxonomy\BasketOrderTaxonomy;
use App\Core\Taxonomy\BasketTaxonomy;
use App\Core\Taxonomy\CommunicatorTaxonomy;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\User\UserInterface;

readonly class BasketOrderService {

    public function __construct(
        private EntityManagerInterface $entityManager,
        private OrderStatusService     $orderStatusService,
        private LoggerService          $loggerService,
        private ReservationService     $reservationService, private SubiektDocumentsService $subiektDocumentsService,
        private CommunicatorService $communicatorService
    ) {}

    public function changeBasketOrderStatus(BasketOrder $basketOrder, int $status): void {
        $basketOrder->setStatusId($status);
        $this->entityManager->persist($basketOrder);
        $this->entityManager->flush();
    }

    public function getBasketOrderProductStatuses(BasketOrder $basketOrder): array {
        $statuses = [];
        foreach ($basketOrder->getBasketOrderProducts() as $basketOrderProduct) {
            $statuses[] = $basketOrderProduct->getStatusId();
        }

        return $statuses;
    }

    public function allowChangeStatus(BasketOrder $basketOrder, int $status): bool {
        $basketOrderProductStatuses = $this->getBasketOrderProductStatuses($basketOrder);
        $currentBasketOrderStatus = $basketOrder->getStatusId();
        $decision = false;

        switch ($status) {
            case BasketOrderTaxonomy::STATUS_COMPLETE_COLLECTION:
                if ($this->areAllStatuses($basketOrderProductStatuses, BasketOrderTaxonomy::STATUS_COMPLETE_COLLECTION)) {
                    $this->changeBasketOrderStatus($basketOrder, BasketOrderTaxonomy::STATUS_COMPLETE_COLLECTION);
                    $decision = true;
                    $this->reservationService->completeReservationByBasketOrder($basketOrder);
                } elseif ($currentBasketOrderStatus !== BasketOrderTaxonomy::STATUS_INCOMPLETE_COLLECTION) {
                    $this->changeBasketOrderStatus($basketOrder, BasketOrderTaxonomy::STATUS_INCOMPLETE_COLLECTION);
                }
                break;

            case BasketOrderTaxonomy::STATUS_COMPLETE_PACKING:
                if ($this->areAllStatuses($basketOrderProductStatuses, BasketOrderTaxonomy::STATUS_COMPLETE_PACKING)) {
                    $this->changeBasketOrderStatus($basketOrder, BasketOrderTaxonomy::STATUS_COMPLETE_PACKING);
                    $decision = true;
                } elseif ($currentBasketOrderStatus !== BasketOrderTaxonomy::STATUS_INCOMPLETE_PACKING) {
                    $this->changeBasketOrderStatus($basketOrder, BasketOrderTaxonomy::STATUS_INCOMPLETE_PACKING);
                }
                break;
        }

        return $decision;
    }

    private function areAllStatuses(array $statuses, int $targetStatus): bool {
        foreach ($statuses as $status) {
            if ($status !== $targetStatus) {
                return false;
            }
        }
        return true;
    }

    public function assignOrdersToBasket(Basket $basket, $basketOrderAssignCountLimit, UserInterface $user): void {
        $orders = $this->entityManager->getRepository(Order::class)->findBy(
            [
                'internal_status_id' => $this->orderStatusService->getStatus(OrderTaxonomy::STATUS_COMPLETING)
            ],
            [
                'date_confirmed' => 'ASC'
            ],
            $basketOrderAssignCountLimit);
        if (!empty($orders)) {
            $basket->setUser($user);
            $basket->setStatusId(BasketTaxonomy::STATUS_COMPLETING);
            foreach ($orders as $order) {
                $this->createBasketOrder($order, $basket, $user);
                if($order->getOrderInvoice()->getAdditionalNumber() && $order->getOrderInvoice()->getMmNumber() != ''){
                    $this->communicatorService->sendTelegramMessage(CommunicatorTaxonomy::TELEGRAM_INFO_CHAT,'Zamówienie '.$order->getOrderInvoice()->getAdditionalNumber().' zostało dodane do koszyka');
                    $this->subiektDocumentsService->createMMByZk($order);
                }

            }
        } else {
            if (!$this->entityManager->getRepository(BasketOrder::class)->count(['basket_id' => $basket->getId()]) > 0) {
                $basket->setUser(null);
                $basket->setStatusId(BasketTaxonomy::STATUS_EMPTY);
            }
        }

        $this->entityManager->persist($basket);
        $this->entityManager->flush();
        $this->logAction($orders, $user, $basket);
    }

    private function createBasketOrder(Order $order, Basket $basket, UserInterface $user): void {
        $basketOrder = new BasketOrder();
        $basketOrder->setOrder($order);
        $basketOrder->setBasketId($basket);
        $basketOrder->setUser($user);
        $basketOrder->setDateCreated(new \DateTime('now'));
        $basketOrder->setDateUpdated(new \DateTime('now'));
        $basketOrder->setStatusId(BasketOrderProductTaxonomy::STATUS_INCOMPLETE_COLLECTION);
        $this->entityManager->persist($basketOrder);
        foreach ($order->getProducts() as $orderProduct) {
            $basketOrderProduct = new BasketOrderProduct();
            $basketOrderProduct->setBasketOrder($basketOrder);
            $basketOrderProduct->setOrderProductId($orderProduct);
            $basketOrderProduct->setStatusId(BasketOrderProductTaxonomy::STATUS_INCOMPLETE_COLLECTION);
            $basketOrderProduct->setQuantityRequired($orderProduct->getQuantity());
            $basketOrderProduct->setQuantityCompleted(0);
            $basketOrderProduct->setQuantityPacked(0);
            $this->entityManager->persist($basketOrderProduct);
        }
        $order->setInternalStatusId($this->orderStatusService->getStatus(OrderTaxonomy::STATUS_IN_BASKET));
        $this->entityManager->persist($order);
    }

    private function logAction(array $orders, UserInterface $user, Basket $basket): void {
        foreach ($orders as $order) {
            $this->loggerService->logOrderAddedToBasket($order, $user, $basket);
        }

        $this->loggerService->logBasketGotOrders($basket, $user);
    }

    public function assignOrderToBasket(Order $order, Basket $basket, UserInterface $user): void {
        if ($basket->isEmpty()) {
            $basket->setUser($user);
            $basket->setStatusId(BasketTaxonomy::STATUS_COMPLETING);
            $this->createBasketOrder($order, $basket, $user);
        } else if ($basket->isCompleting()) {
            $this->createBasketOrder($order, $basket, $user);
        }
        $this->entityManager->persist($basket);
        $this->entityManager->flush();
        $this->logAction([$order], $user, $basket);
    }

    public function reassignOrderToBasket(Order $order, Basket $basket, UserInterface $user): void {
        if ($basket->isEmpty()) {
            $basket->setUser($user);
            $basket->setStatusId(BasketTaxonomy::STATUS_COMPLETING);
            $this->reassignBasketOrder($order, $basket, $user);
        } else if ($basket->isCompleting()) {
            $this->reassignBasketOrder($order, $basket, $user);
        }
        $this->entityManager->persist($basket);
        $this->entityManager->flush();
        $this->logAction([$order], $user, $basket);
    }

    public function reassignBasketOrder(Order $order, Basket $basket, UserInterface $user): void {
        $basketOrders = $this->entityManager->getRepository(BasketOrder::class)->findBy(['order' => $order]);
        foreach ($basketOrders as $basketOrder) {
            $basketOrder->setBasketId($basket);
            $basketOrder->setUser($user);
            $basketOrder->setDateUpdated(new \DateTime('now'));
            $this->entityManager->persist($basketOrder);
        }
        $order->setInternalStatusId($this->orderStatusService->getStatus(OrderTaxonomy::STATUS_IN_BASKET));
        $this->entityManager->persist($order);
    }
}