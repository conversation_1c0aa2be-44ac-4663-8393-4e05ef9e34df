<?php

namespace App\Core\Service\Basket;

use App\Core\Entity\Basket;
use App\Core\Entity\BasketOrder;
use App\Core\Service\LoggerService;
use App\Core\Taxonomy\BasketOrderProductTaxonomy;
use App\Core\Taxonomy\BasketOrderTaxonomy;
use App\Core\Taxonomy\BasketTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class BasketService {

    public function __construct(private readonly EntityManagerInterface $entityManager, private readonly LoggerService $loggerService) {}

    public function finishCollecting(Basket $basket, UserInterface $user): void {

        $basketOrders = $this->entityManager->getRepository(BasketOrder::class)->findBy(['basket_id' => $basket->getId()]);
        if (empty($basketOrders)) {
            $basket->setStatusId(BasketTaxonomy::STATUS_EMPTY);
            $basket->setUser(null);
            $this->loggerService->basketChangeStatus($basket, $user, BasketTaxonomy::STATUS_EMPTY);
        }

        $orderStatuses = [];
        foreach ($basketOrders as $basketOrder) {
            /** @var $basketOrder BasketOrder */
            $orderStatuses[] = $basketOrder->getStatusId();
            if ($basketOrder->getStatusId() === BasketOrderTaxonomy::STATUS_COMPLETE_COLLECTION) {
                $basketOrder->setStatusId(BasketOrderTaxonomy::STATUS_INCOMPLETE_PACKING);
                foreach ($basketOrder->getBasketOrderProducts() as $basketOrderProduct) {
                    $basketOrderProduct->setStatusId(BasketOrderProductTaxonomy::STATUS_INCOMPLETE_PACKING);
                    $this->entityManager->persist($basketOrderProduct);
                }
                $this->entityManager->persist($basketOrder);
            }
        }
        if (in_array(BasketOrderTaxonomy::STATUS_COMPLETE_COLLECTION, $orderStatuses)) {
            $basket->setStatusId(BasketTaxonomy::STATUS_PACKING);
            $this->loggerService->basketChangeStatus($basket, $user, BasketTaxonomy::STATUS_PACKING);
        }

        $this->entityManager->persist($basket);
        $this->entityManager->flush();
    }

    public function finishPacking(Basket $basket, UserInterface $user): void {
        $basketOrders = $this->entityManager->getRepository(BasketOrder::class)->findBy(['basket_id' => $basket->getId()]);
        if (empty($basketOrders)) {
            $this->nullAndEmptyBasket($basket, $user);

            return;
        }
        $basketOrdersStatuses = $this->getBasketOrderStatuses($basketOrders);
        if ($this->areAllStatuses($basketOrdersStatuses, BasketOrderTaxonomy::STATUS_INCOMPLETE_COLLECTION)) {
            $basket->setStatusId(BasketTaxonomy::STATUS_COMPLETING);
            $basket->setUser(null);
            $this->loggerService->basketChangeStatus($basket, $user, BasketTaxonomy::STATUS_COMPLETING);
        }

        $this->entityManager->persist($basket);
        $this->entityManager->flush();
    }

    private function nullAndEmptyBasket(Basket $basket, UserInterface $user): void {
        $basket->setStatusId(BasketTaxonomy::STATUS_EMPTY);
        $basket->setUser(null);
        $this->entityManager->persist($basket);
        $this->entityManager->flush();
        $this->loggerService->basketChangeStatus($basket, $user, BasketTaxonomy::STATUS_EMPTY);
    }

    public function getBasketOrderStatuses(array $basketOrders): array {
        $statuses = [];
        foreach ($basketOrders as $basketOrder) {
            $statuses[] = $basketOrder->getStatusId();
        }

        return $statuses;
    }

    private function areAllStatuses(array $statuses, int $targetStatus): bool {
        foreach ($statuses as $status) {
            if ($status !== $targetStatus) {
                return false;
            }
        }
        return true;
    }
}