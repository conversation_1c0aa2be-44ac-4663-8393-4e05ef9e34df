<?php

namespace App\Core\Service\PrintNode;

use App\Core\Service\Settings;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class PrintNodeService {
    private string $baseUrl;

    public function __construct(
        private readonly HttpClientInterface  $client,
        private readonly PrintNodeAuthService $auth,
        private readonly Settings             $settings,
    ) {
        $this->baseUrl = $this->settings->get('PRINTNODE_BASE_URL') ?? '';
    }

    public function request(string $method, string $endpoint, array $options = []) {
        try {
            $defaultHeaders = $this->auth->getAuthHeaders();

            if (array_key_exists('headers', $options)) {
                $options['headers'] = array_merge($defaultHeaders, $options['headers']);
            } else {
                $options['headers'] = $defaultHeaders;
            }

            return $this->client->request($method, $this->baseUrl . $endpoint, array_merge([
                'headers' => $this->auth->getAuthHeaders()
            ], $options));
        } catch (\Exception $e) {
            throw new \Exception('Error while requesting PrintNode API - ' . $e->getMessage());
        }

    }
}
