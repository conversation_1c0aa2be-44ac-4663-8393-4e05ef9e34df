<?php

namespace App\Core\Service\PrintNode;

use App\Core\Service\Settings;

class PrintNodeAuthService {
    private string $apiKey;

    public function __construct(
        private readonly Settings $settings,
    ) {
        $this->apiKey = $this->settings->get('PRINTNODE_API_KEY') ?? '';
    }

    public function getAuthHeaders(): array {
        return [
            'Authorization' => 'Basic ' . base64_encode($this->apiKey . ':'),
            'Content-Type' => 'application/json'
        ];
    }
}