<?php

namespace App\Core\Service\PrintNode;

use App\Core\Service\PrintNode\Enum\PrintNodeContentTypeEnum;
use Symfony\Component\HttpFoundation\Response;;

class PrintNodePostService {
    public function __construct(
        private readonly PrintNodeService    $printNodeService,
        private readonly PrintNodeGetService $printNodeGetService
    ) {
    }

    public function print(int $printerId, string $contentType, string $content, string $title = ''): mixed {
        return $this->sendPrintJob($printerId, $contentType, $content, $title);
    }

    public function printByUserPrinter(string $contentType, string $content, string $title = ''): mixed {
        $userPrinter = $this->printNodeGetService->getPrinterByUser();
        if ($userPrinter === null) {
            return new Response('User printer not found', Response::HTTP_NOT_FOUND);
        }

        return $this->sendPrintJob($userPrinter, $contentType, $content, $title);
    }

    private function sendPrintJob(int $printerId, string $contentType, string $content, string $title): mixed {
        if (!PrintNodeContentTypeEnum::tryFrom($contentType)) {
            return 'Invalid content type';
        }
        $data = [
            'printerId' => $printerId,
            'contentType' => $contentType,
            'content' => $content,
            'title' => $title,
        ];

        return $this->printNodeService->request('POST', 'printjobs', [
            'json' => $data,
            'headers' => [
                'Content-Type' => 'application/json',
            ],
        ]);
    }
}
