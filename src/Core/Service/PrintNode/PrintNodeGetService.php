<?php

namespace App\Core\Service\PrintNode;

use App\Core\Service\Settings;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Contracts\HttpClient\ResponseInterface;

class PrintNodeGetService {

    public function __construct(
        private readonly PrintNodeService $printNodeService,
        private readonly Settings         $settings,
        private readonly Security         $security
    ) {
    }

    public function getPrintJobs(): ResponseInterface {
        return $this->printNodeService->request('GET', 'printjobs');
    }
    public function getWhoAmI(): ResponseInterface {
        return $this->printNodeService->request('GET', 'whoami');
    }

    public function getPrinters(): ResponseInterface {
        return $this->printNodeService->request('GET', 'printers');
    }

    public function getPrinter(int $id): ResponseInterface {
        return $this->printNodeService->request('GET', 'printers/' . $id);
    }

    public function getPrinterState(int $id): bool {
        try {
            $response = $this->printNodeService->request('GET', 'printers/' . $id);

            if (null !== $response && $response->getContent() !== '') {
                $printer = json_decode($response->getContent(), true);

                return isset($printer[0]) && $printer[0]['state'] === 'online';
            }
        } catch (\Exception $e) {
        }

        return false;
    }

    public function getPrinterByUser() {
        $stations = $this->settings->get('PRINTNODE_STATIONS');
        $user = $this->security->getUser();
        return $this->findKeyByValue(json_decode($stations, true), $user->getEmail());
    }

    private function findKeyByValue($data, $value) {
        foreach ($data as $key => $values) {
            if (in_array($value, $values)) {
                return $key;
            }
        }

        return null;
    }
}