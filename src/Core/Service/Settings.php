<?php

namespace App\Core\Service;

use Doctrine\ORM\EntityManagerInterface;
use App\Core\Entity\Settings as SettingsEntity;
class Settings {

    public function __construct(private readonly EntityManagerInterface $entityManager) {}

    public function has($key): bool {
        return !empty($this->entityManager->getRepository(SettingsEntity::class)->findOneBy(['name' => $key]));
    }

    public function get($key): ?string {
        if ($this->has($key)) {
            return $this->entityManager->getRepository(SettingsEntity::class)->findOneBy(['name' => $key])->getValue();
        }

        return NULL;
    }

    private function getSettingsEntity($key): ?SettingsEntity {
        if ($this->has($key)) {
            return $this->entityManager->getRepository(SettingsEntity::class)->findOneBy(['name' => $key]);
        }

        return NULL;
    }

    public function set($key, $value): void {
        if (NULL === ($settingsEntity = $this->getSettingsEntity($key))) {
            $settingsEntity = new SettingsEntity();
            $settingsEntity->setName($key);
        }
        $settingsEntity->setValue($value);

        $this->entityManager->persist($settingsEntity);
        $this->entityManager->flush();
    }
}