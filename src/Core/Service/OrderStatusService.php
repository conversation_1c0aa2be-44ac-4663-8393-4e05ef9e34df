<?php

namespace App\Core\Service;

use App\Core\Entity\OrderStatus;
use Doctrine\ORM\EntityManagerInterface;

class OrderStatusService {

    public function __construct(private readonly EntityManagerInterface $entityManager){}

    public function getStatus($status) {
        return $this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => $status]);
    }

    public function getAll() {
        return $this->entityManager->getRepository(OrderStatus::class)->findAll();
    }
}