<?php

namespace App\Core\Service;

use Symfony\Contracts\HttpClient\HttpClientInterface;

class SubiektApi {

    private string $subiektUrl = '';
    private string $subiektApiKey = '';
    public function __construct(
        private readonly HttpClientInterface $httpClient,
        private readonly Settings $settings,
    ) {
        $this->subiektUrl = $this->settings->get('SUBIEKT_URL') ?? '';
        $this->subiektApiKey = $this->settings->get('SUBIEKT_API_KEY') ?? '';
    }

    public function makeRequest(string $sku) {
        $requestBody = [
            'body' =>  $this->prepareBody($sku)
        ];
        $response = $this->httpClient->request('GET', $this->subiektUrl, $requestBody);

        return json_decode($response->getContent(), TRUE);
    }

    private function prepareBody(string $sku) {
        return json_encode([
            'api_key' => $this->subiektApiKey,
            'data' => [
                'sku' => $sku
            ]
        ]);
    }
}