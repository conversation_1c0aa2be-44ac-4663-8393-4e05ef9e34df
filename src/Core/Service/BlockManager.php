<?php

namespace App\Core\Service;

use App\Core\Entity\BlockPlacement;
use App\Engine\Block\BlockInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;

class BlockManager
{
    private array $availableBlocks = [];

    public function __construct(
        private EntityManagerInterface $entityManager,
        #[AutowireIterator(BlockInterface::class)] $blocks
    ) {
        $this->registerBlocks($blocks);
    }

    public function getAvailableBlocks(): array
    {
        return $this->availableBlocks;
    }

    public function getBlocksForRegion(string $region): array
    {
        $placements = $this->entityManager
            ->getRepository(BlockPlacement::class)
            ->findBy(['region' => $region, 'enabled' => true], ['weight' => 'ASC']);

        $blocks = [];
        foreach ($placements as $placement) {
            $block = $this->getBlock($placement->getBlockId());
            if ($block && $block->isEnabled()) {

                $block->setConfiguration($placement->getConfiguration());
                $blocks[] = $block;
            }
        }

        return $blocks;
    }

    public function getBlock(string $blockId): ?BlockInterface
    {
        return $this->availableBlocks[$blockId] ?? null;
    }

    public function placeBlock(string $blockId, string $region, int $weight = 0, array $config = []): BlockPlacement
    {
        $placement = new BlockPlacement();
        $placement->setBlockId($blockId);
        $placement->setRegion($region);
        $placement->setWeight($weight);
        $placement->setConfiguration($config);
        $placement->setEnabled(true);

        $this->entityManager->persist($placement);
        $this->entityManager->flush();

        return $placement;
    }

    public function removeBlock(int $placementId): void
    {
        $placement = $this->entityManager
            ->getRepository(BlockPlacement::class)
            ->find($placementId);

        if ($placement) {
            $this->entityManager->remove($placement);
            $this->entityManager->flush();
        }
    }

    public function updateBlockPlacement(int $placementId, array $data): void
    {
        $placement = $this->entityManager
            ->getRepository(BlockPlacement::class)
            ->find($placementId);

        if ($placement) {
            if (isset($data['region'])) {
                $placement->setRegion($data['region']);
            }
            if (isset($data['weight'])) {
                $placement->setWeight($data['weight']);
            }
            if (isset($data['enabled'])) {
                $placement->setEnabled($data['enabled']);
            }
            if (isset($data['configuration'])) {
                $placement->setConfiguration($data['configuration']);
            }

            $this->entityManager->flush();
        }
    }

    public function getAllPlacements(): array
    {
        return $this->entityManager
            ->getRepository(BlockPlacement::class)
            ->findBy([], ['region' => 'ASC', 'weight' => 'ASC']);
    }

    public function getBlocksByRegion(): array
    {
        $placements = $this->getAllPlacements();
        $grouped = [];

        foreach ($placements as $placement) {
            $region = $placement->getRegion();
            if (!isset($grouped[$region])) {
                $grouped[$region] = [];
            }
            $grouped[$region][] = $placement;
        }

        return $grouped;
    }

    private function registerBlocks(iterable $blocks): void
    {
        foreach ($blocks as $block) {
            if ($block instanceof BlockInterface) {
                $this->availableBlocks[$block->getId()] = $block;
            }
        }
    }

    public function renderRegionBlocks(string $region, array $context = []): string
    {
        $blocks = $this->getBlocksForRegion($region);
        $output = '';

        foreach ($blocks as $block) {
            if ($block->isVisible($context)) {
                $output .= $block->render($context);
            }
        }

        return $output;
    }
}
