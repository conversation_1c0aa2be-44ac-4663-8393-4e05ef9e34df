<?php

namespace App\Core\Service;

use Symfony\Component\Lock\LockFactory;
use Symfony\Component\Lock\Store\FlockStore;

class LockService
{
    private LockFactory $lockFactory;

    public function __construct()
    {
        $store = new FlockStore();
        $this->lockFactory = new LockFactory($store);
    }

    public function acquireLock(string $resource, callable $callback): bool {
        $lock = $this->lockFactory->createLock($resource);

        if (!$lock->acquire()) {
            return false;
        }

        try {
            $callback();
        } finally {
            $lock->release();
        }

        return true;
    }
}