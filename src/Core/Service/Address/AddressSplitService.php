<?php

namespace App\Core\Service\Address;

class AddressSplitService {

    /**
     * Removes diacritical marks from text and converts to Latin characters
     */
    public static function removeDiacritics(string $text): string {
        $diacriticsMap = [
            // Polish characters
            'ą' => 'a', 'Ą' => 'A',
            'ć' => 'c', 'Ć' => 'C',
            'ę' => 'e', 'Ę' => 'E',
            'ł' => 'l', 'Ł' => 'L',
            'ń' => 'n', 'Ń' => 'N',
            'ó' => 'o', 'Ó' => 'O',
            'ś' => 's', 'Ś' => 'S',
            'ź' => 'z', 'Ź' => 'Z',
            'ż' => 'z', 'Ż' => 'Z',

            // German characters
            'ä' => 'a', 'Ä' => 'A',
            'ö' => 'o', 'Ö' => 'O',
            'ü' => 'u', 'Ü' => 'U',
            'ß' => 'ss',

            // French characters
            'à' => 'a', 'À' => 'A',
            'á' => 'a', 'Á' => 'A',
            'â' => 'a', 'Â' => 'A',
            'è' => 'e', 'È' => 'E',
            'é' => 'e', 'É' => 'E',
            'ê' => 'e', 'Ê' => 'E',
            'ë' => 'e', 'Ë' => 'E',
            'î' => 'i', 'Î' => 'I',
            'ï' => 'i', 'Ï' => 'I',
            'ì' => 'i', 'Ì' => 'I',
            'í' => 'i', 'Í' => 'I',
            'ô' => 'o', 'Ô' => 'O',
            'ò' => 'o', 'Ò' => 'O',
            'ù' => 'u', 'Ù' => 'U',
            'ú' => 'u', 'Ú' => 'U',
            'û' => 'u', 'Û' => 'U',
            'ý' => 'y', 'Ý' => 'Y',
            'ÿ' => 'y',

            // Spanish characters
            'ñ' => 'n', 'Ñ' => 'N',

            // Turkish characters
            'ç' => 'c', 'Ç' => 'C',
            'ğ' => 'g', 'Ğ' => 'G',
            'ş' => 's', 'Ş' => 'S',

            // Nordic characters
            'å' => 'a', 'Å' => 'A',
            'æ' => 'ae', 'Æ' => 'AE',

            // Other common characters
            'š' => 's',
        ];

        return strtr($text, $diacriticsMap);
    }
    public static function split(
        string $inputAddress,
        bool   $numberComesFirst = false,
        bool   $exceptions = false): array {
        $c = 'äáàâåöóòôüúùûëéèêïíìîýÿÄÁÀÂÖÓÒÔÜÚÙÛËÉÈÊÏÍÌÎÝßñÑŞÇçğšæÆłŁąĄęĘóÓśŚźŹżŻćĆńŃ';

        $address = new AddressService($inputAddress);
        if ($numberComesFirst) {
            $hasMatch = preg_match(
                "/^(\d+)([\w\/\‘\'\-\.]*)[,\s]+(\d*[\w{$c}\d \/\‘\'\-\.]+)$/",
                $address->getStrippedInput(),
                $match
            );
        } else {
            $hasMatch = preg_match(
                "/^(\d*[\w{$c}\d \/\‘\'\-\.]+)[,\s]+(\d+)[\s]*([\w{$c}\d\-\/]*)$/",
                $address->getStrippedInput(),
                $match
            );
        }

        if ($hasMatch) {
            array_shift($match);

            if ($numberComesFirst) {
                $address->setStreet(self::removeDiacritics($match[2]));
                $address->setHousenumber(self::removeDiacritics($match[0]));
                $address->setAddition(self::removeDiacritics($match[1]));
            } else {
                $address->setStreet(self::removeDiacritics($match[0]));
                $address->setHousenumber(self::removeDiacritics($match[1]));
                $address->setAddition(self::removeDiacritics($match[2]));
            }

            return $address->toArray();

        } elseif ($exceptions) {

            return ["Parsed address could not be split", 0, $address];

        } else {

            return [$address->getOrginalInput(), null, null];

        }
    }
}