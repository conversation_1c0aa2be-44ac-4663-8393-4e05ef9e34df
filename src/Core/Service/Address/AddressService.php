<?php

namespace App\Core\Service\Address;

use JsonSerializable;

class AddressService implements JsonSerializable {
    protected $orginalInput;
    protected $street;
    protected $housenumber;
    protected $addition;

    public function __construct(string $inputAddress) {
        $this->orginalInput = $inputAddress;
    }

    public function getOrginalInput(): string {
        return $this->orginalInput;
    }

    public function getStrippedInput(): string {
        $input = $this->getOrginalInput();

        $input = trim(preg_replace('/\s+/S', " ", $input));

        $input = str_replace([
            'Mieszkanie ',
            'Apartament ',
            'mieszkanie ',
            'apartament ',
            'm. ',
        ], '', $input);

        return $input;
    }

    public function getStreet(): string {
        return $this->street ?? "";
    }

    public function setStreet(string $street): void {
        $this->street = $street;
    }

    public function getHousenumber(): string {
        return $this->housenumber ?? "";
    }

    public function setHousenumber(string $housenumber): void {
        $this->housenumber = $housenumber;
    }

    public function getAddition(): string {
        return $this->addition ?? "";
    }

    public function setAddition(string $addition): void {
        $this->addition = $addition;
    }

    public function toArray(): array {
        return [
            'street' => $this->getStreet(),
            'houseNumber' => $this->getHousenumber(),
            'apartmentNumber' => $this->getAddition(),
        ];
    }

    public function __toString() {
        return implode(' ', $this->toArray());
    }

    public function jsonSerialize() {
        $this->toArray();
    }
}