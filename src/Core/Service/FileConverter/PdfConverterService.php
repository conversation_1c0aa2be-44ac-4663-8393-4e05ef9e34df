<?php

namespace App\Core\Service\FileConverter;

use Symfony\Component\HttpFoundation\Response;

class PdfConverterService
{
    public static function convertFromBase64(string $base64): string|false {
        return base64_decode($base64);
    }

    public static function downloadPdfFromBase64(string $base64, string $fileName): Response {
        $pdf = self::convertFromBase64($base64);
        if ($pdf === false) {
            return new Response('Error while converting pdf from base64');
        }
        $response = new Response($pdf);
        $response->headers->set('Content-Type', 'application/pdf');
        $response->headers->set('Content-Disposition', 'attachment; filename="'.$fileName.'"');
        return $response;
    }
}