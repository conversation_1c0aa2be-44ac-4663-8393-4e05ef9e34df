<?php

namespace App\Core\Service\Communicator;

use App\Core\Service\Communicator\Slack\CommunicatorSlackService;
use App\Core\Service\Communicator\Telegram\CommunicatorTelegramService;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

readonly class CommunicatorService
{
    public function __construct(
        private CommunicatorSlackService    $slackService,
        private CommunicatorTelegramService $telegramService,
        #[Autowire(param: 'telegram.debug')]  private bool $debug,
    ){}

    public function sendSlackMessage(string $channel, string $text, array $blocks = []): array
    {
        return $this->slackService->sendMessage($channel, $text, $blocks);
    }
    public function sendTelegramMessage(string $chatId,string $message): array
    {
        if ($this->debug) {
            return $this->telegramService->sendMessage($chatId, $message);
        }

        return [];
    }
}