<?php

Namespace App\Core\Service\Communicator\Slack;

use App\Core\Service\Settings;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class CommunicatorSlackService
{

    public const CHANNEL = '#wms-orders';
    private ?string $token;

    public function __construct(
        private readonly HttpClientInterface $httpClient,
        private readonly Settings $settings,
    )
    {
        $this->token = $this->settings->get('SLACK_BOT_TOKEN');
    }

    public function sendMessage(string $channel, string $text, array $blocks = []): array
    {
        return ['error' => 'This method is unavailable for now'];
        $url = 'https://slack.com/api/chat.postMessage';

        $response = $this->httpClient->request('POST', $url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'channel' => $channel,
                'text' => $text,
                'blocks' => $blocks,
            ],
        ]);

        if ($response->getStatusCode() !== 200) {
            throw new \Exception('Failed to send message: ' . $response->getContent(false));
        } else {
            $content = json_decode($response->getContent(),true);
            if(!$content['ok']){
                return [
                    'status' => 'error',
                    'message' => $content['error'],
                    'code' => $response->getStatusCode(),
                ];
            } else {
                return [
                    'status' => 'success',
                    'message' => 'Message "' . $content['message']['text'] .'" sent successfully',
                    'code' => $response->getStatusCode(),
                ];
            }
        }
    }

}