<?php

namespace App\Core\Service\Communicator\Telegram;

use App\Core\Service\Settings;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class CommunicatorTelegramService
{

    public const CHANNEL = '-931188189';
    private ?string $botToken;

    public function __construct(
        private readonly HttpClientInterface $httpClient,
        private readonly Settings            $settings,
    ) {
        $this->botToken = $this->settings->get('TELEGRAM_BOT_TOKEN');
    }

    public function sendMessage(string $chatId, string $message): array {
        return ['error' => 'This method is unavailable for now'];
        $response = $this->httpClient->request('GET', sprintf('https://api.telegram.org/bot%s/sendMessage', $this->botToken), [
            'query' => [
                'chat_id' => $chatId,
                'text' => $message
            ]
        ]);

        $content = json_decode($response->getContent(false), true);
        if (!$content['ok']) {
            return [
                'status' => 'error',
                'message' => $content['description'],
                'code' => $response->getStatusCode(),
            ];
        } else {
            return [
                'status' => 'success',
                'message' => 'Message "' . $content['result']['text'] . '" sent successfully',
                'code' => $response->getStatusCode(),
            ];
        }
    }
}