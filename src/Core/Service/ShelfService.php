<?php

namespace App\Core\Service;

use App\Core\Entity\Ean;
use App\Core\Entity\EanShelfQuantity;
use App\Core\Entity\Order;
use App\Core\Entity\Product;
use App\Core\Entity\ProductReservation;
use App\Core\Entity\QuantityLog;
use App\Core\Entity\Shelf;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Uid\Uuid;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class ShelfService {

    private string $shelfUrl;

    public function __construct(
        private readonly HttpClientInterface $httpClient,
        private readonly Settings $settings,
        private readonly EntityManagerInterface $entityManager
    ) {
        $this->shelfUrl = $this->settings->get('SHELF_API_URL') ?? '';
    }

    public function getShelfData(Order $order): array {
        $returnArray = [];
        foreach ($order->getProducts() as $product) {
            $productId = $product->getId()->toRfc4122();
            $returnArray[$productId]['collected_all'] = FALSE;
            $returnArray[$productId]['location'] = $product->getLocation() ?? '';
            $returnArray[$productId]['ean'] = '';
            $quantity = $product->getQuantity();

            $productObjects = $this->entityManager->getRepository(Product::class)->findBy(['sku' => $product->getSku()]);
            if (empty($productObjects)) {
                $returnArray[$productId]['status'] = OrderTaxonomy::NEED_INVESTIGATION;
                continue;
            }

            $currentlyReservedObjects = [];
            foreach ($productObjects as $productObject) {
                $currentlyReservedObjects = array_merge($currentlyReservedObjects, $this->entityManager->getRepository(ProductReservation::class)->findBy(['id_order' => $order, 'product' => $productObject, 'status' => 'reserved']));
            }

            $eans = [];
            foreach ($productObjects as $productObject) {
                $eans = array_merge($eans, $this->entityManager->getRepository(Ean::class)->findBy(['product' => $productObject]));
            }

            if (empty($eans)) {
                $returnArray[$productId]['status'] = OrderTaxonomy::NEED_INVESTIGATION;
                continue;
            }
            $eanCodes = [];
            foreach ($eans as $eanElement) {
                $eanCodes[] = $eanElement->getEan();
            }
            $eanCodes = array_unique($eanCodes);

            if (empty($eanCodes)) {
                $returnArray[$productId]['status'] = OrderTaxonomy::NEED_INVESTIGATION;
                continue;
            }
            $eanList = implode(',', $eanCodes);
            foreach ([StorehouseTaxonomy::MAIN_SUBIEKT_STOREHOUSE_NAME, StorehouseTaxonomy::SHOP_SUBIEKT_STOREHOUSE_NAME] as $storehouse) {
                $conn = $this->entityManager->getConnection();
                $sql = 'SELECT e.id, strhs.internal_name from ean e left join ean_shelf_quantity esq on e.id = esq.ean_id left join shelf s on s.id = esq.shelf_id left join rack r on r.id = s.rack_id left join storehouse strhs on strhs.id = r.storehouse_id where strhs.internal_name = :storehousename and e.ean in (' . $eanList. ')';
                $stmt = $conn->prepare($sql);

                $stmt->bindValue('storehousename', $storehouse);

                $res = $stmt->executeQuery();
                $results = $res->fetchAllAssociative();
                $eanObjects = [];
                foreach ($results as $result) {
                    $eanObjects = array_merge($eanObjects, $this->entityManager->getRepository(Ean::class)->findBy(['id' => Uuid::fromBinary($result['id'])->toRfc4122()]));
                }
                $esqs = [];
                if ($storehouse === StorehouseTaxonomy::MAIN_SUBIEKT_STOREHOUSE_NAME) {
                    $shelves = $this->entityManager->getRepository(Shelf::class)->findBy(['shelf_no' => ['R1', 'R2', 'R3', 'R4', 'R5', 'R6']]);
                    $highPriority = $this->entityManager->getRepository(EanShelfQuantity::class)->filterByEansAndShelves($eanObjects, $shelves);
                    $shelves = array_merge($shelves, $this->entityManager->getRepository(Shelf::class)->findBy(['shelf_no' => ['Lada']]));
                    $lowPriority = $this->entityManager->getRepository(EanShelfQuantity::class)->filterByEansAndNotShelves($eanObjects, $shelves);

                    $esqs = array_merge($highPriority, $lowPriority);

                } else {
                    foreach ($eanObjects as $eanObject) {
                        $esqs = array_merge($esqs, $this->entityManager->getRepository(EanShelfQuantity::class)->findBy(['ean' => $eanObject]));
                    }
                }

                foreach ($currentlyReservedObjects as $reservedObject) {
                    $quantity -= $reservedObject->getQuantity();
                }

                if ($quantity <= 0) {
                    $returnArray[$productId]['collected_all'] = TRUE;
                    $returnArray[$productId]['location'] = $product->getLocation();
                    $returnArray[$productId]['ean'] = $product->getEan();
                    break;
                }
                foreach ($esqs as $esq) {
                    if ($storehouse !== $esq->getShelf()->getRack()->getStorehouse()->getInternalName()) {
                        continue;
                    }
                    $reservation = NULL;
                    foreach ($currentlyReservedObjects as $reservedKey => $reservedObject) {
                        if ($esq === $reservedObject->getEanShelfQuantity()) {
                            $reservation = $reservedObject;
                            unset($currentlyReservedObjects[$reservedKey]);
                        }
                    }

                    if (NULL === $reservation){
                        $reservation = new ProductReservation();
                    }

                    $doReservation = FALSE;
                    $quantityToEsq = $esq->getVirtualQuantity();
                    $quantityToReserve = $reservation->getQuantity();

                    if ($esq->getVirtualQuantity() >= $quantity) {
                        $returnArray[$productId]['location'] .=  ' ' . $esq->getShelf()->getShelfNo() . '('. $quantity . ')';
                        $returnArray[$productId]['ean'] = $esq->getEan()->getEan();

                        $quantityToReserve = $reservation->getQuantity() + $quantity;
                        $quantityToEsq = $esq->getVirtualQuantity() - $quantity;
                        $quantity = 0;
                        $doReservation = TRUE;
                    } elseif ($esq->getVirtualQuantity() > 0) {
                        $tookQty = $esq->getVirtualQuantity();
                        $quantity -= $tookQty;
                        $returnArray[$productId]['location'] .= ' ' . $esq->getShelf()->getShelfNo() . '('. $tookQty . ')';
                        $returnArray[$productId]['ean'] = $esq->getEan()->getEan();
                        $quantityToReserve = $reservation->getQuantity() + $tookQty;
                        $quantityToEsq = $esq->getVirtualQuantity() - $tookQty;

                        $doReservation = TRUE;
                    }
                    if ($doReservation) {
                        if ($quantityToEsq < 0) {
                            $quantityLog = new QuantityLog();
                            $quantityLog->setQuantityAvailable($esq->getVirtualQuantity());
                            $quantityLog->setQuantityRequired($esq->getQuantity());
                            $quantityLog->setIdOrder($order);
                            $quantityLog->setIdEsq($esq);
                            $quantityLog->setIdProduct($product);
                            $quantityLog->setIdStorehouse($esq->getShelf()->getRack()->getStorehouse());
                            $quantityLog->setDateChecked(new \DateTime());
                            $this->entityManager->persist($quantityLog);
                        }
                        $esq->setVirtualQuantity($quantityToEsq);
                        $reservation->setShelf($esq->getShelf());
                        $reservation->setQuantity($quantityToReserve);
                        $reservation->setProduct($esq->getEan()->getProduct());
                        $reservation->setStatus('reserved');
                        $reservation->setDateCreated(new \DateTime());
                        $reservation->setEanShelfQuantity($esq);
                        $reservation->setIdOrder($order);
                        $this->entityManager->persist($esq);
                        $this->entityManager->persist($reservation);
                    }

                    if (0 === $quantity) {
                        $returnArray[$productId]['collected_all'] = TRUE;
                        break;
                    }
                }
                if (0 === $quantity) {
                    $returnArray[$productId]['collected_all'] = TRUE;
                    break;
                }
            }

        }
        $this->entityManager->flush();

        return $returnArray;
    }

    public function getSkuByEan(string $ean): ?string {
        $data = ['searchTerm' => $ean];
        $response = $this->httpClient->request('POST', $this->shelfUrl . '/scanner/getCode', [
            'headers' => [
                'Content-Type' => 'multipart/form-data',
            ],
            'body' => $data
        ]);
        $contentData = json_decode($response->getContent(false), true);
        if (is_array($contentData)) {
            $dataTable = reset($contentData);
            if (isset($dataTable['id'])) {
                return $this->getCodes($dataTable['id']);
            }
        }

        return null;
    }

    private function getCodes($codeId): ?string {
        $data = ['searchTerm' => $codeId];
        $response = $this->httpClient->request('POST', $this->shelfUrl . '/scanner/getCodes', [
            'headers' => [
                'Content-Type' => 'multipart/form-data',
            ],
            'body' => $data,
        ]);
        $contentData = json_decode($response->getContent(false), true);
        if (is_array($contentData)) {
            foreach ($contentData as $productData) {
                if (!empty($productData['shelf_mappings'])) {
                    foreach ($productData['shelf_mappings'] as $mapping) {
                        if ($mapping['quantity_reserved'] > 0) {
                            return $productData['sku'];
                        }
                    }
                }
            }
        }

        return null;
    }
}