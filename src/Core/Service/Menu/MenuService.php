<?php

namespace App\Core\Service\Menu;

use App\Core\Attribute\AsMenuLink;
use App\Core\Rules\Action\SendMailAction;
use App\Core\Rules\RuleTrigger;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;
use Symfony\Component\DependencyInjection\Attribute\TaggedIterator;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Traversable;
use Twig\Environment;

class MenuService {

    private array $links = [];

    public function __construct(
        #[AutowireIterator('menu.menu_link')] private readonly Traversable $menuLinks,
        private readonly UrlGeneratorInterface $urlGenerator,
        private Environment $twig,
        private Security $security,
    ) {}

    public function getMenuLinks(): array
    {

        if (!empty($this->links)) {
            return $this->links;
        }

        $flatLinks = [];

        foreach ($this->menuLinks as $service) {
            $classReflection = new \ReflectionClass($service);
            foreach ($classReflection->getMethods() as $method) {
                $menuAttributes = $method->getAttributes(AsMenuLink::class);
                $routeAttributes = $method->getAttributes(Route::class);

                if (empty($menuAttributes) || empty($routeAttributes)) {
                    continue;
                }

                $menuAttr = $menuAttributes[0]->newInstance();
                $routeArgs = $routeAttributes[0]->getArguments();
                $routeName = $routeArgs['name'] ?? null;

                if (!$routeName) {
                    continue;
                }

                if ($menuAttr->getPermission() && !$this->security->isGranted(
                        $menuAttr->getPermission(),
                        $this->security->getUser()
                    )) {
                    continue;
                }

                $flatLinks[] = [
                    'name' => $menuAttr->getLinkName(),
                    'url' => $this->urlGenerator->generate($routeName),
                    'parent' => $menuAttr->getParent(),
                    'icon' => $menuAttr->getIcon(),
                    'route' => $routeName,
                    'weight' => $menuAttr->getWeight(),
                ];
            }
        }
        $this->links = $this->buildTree($flatLinks);
        return $this->links;
    }
    public function renderMenu(): string {
        return $this->twig->render('menu/menu.html.twig', ['links' => $this->getMenuLinks()]);
    }

    private function buildTree(array $elements): array {
        $indexed = [];
        $tree = [];

        foreach ($elements as &$element) {
            $element['children'] = [];
            if (!isset($element['weight'])) {
                $element['weight'] = 0;
            }
            $indexed[$element['route']] = &$element;
        }

        foreach ($indexed as &$element) {
            if ($element['parent'] !== null) {
                $parentName = $element['parent'];
                if (isset($indexed[$parentName])) {
                    $indexed[$parentName]['children'][] = &$element;
                }
            } else {
                $tree[] = &$element;
            }
        }

        $sortChildren = function (&$items) use (&$sortChildren) {
            usort($items, function ($a, $b) {
                return $a['weight'] < $b['weight'];
            });
            foreach ($items as &$item) {
                if (!empty($item['children'])) {
                    $sortChildren($item['children']);
                }
            }
        };

        $sortChildren($tree);

        return $tree;
    }
}