<?php

namespace App\Core\Service\View\Order;

use App\Core\Service\View\Order\Types\ViewTypeInterface;

final class OrderViewFactory {

    public function __construct (private iterable $viewProvider) {}

    public function getViewProvider($type): ViewTypeInterface {
        foreach ($this->viewProvider as $provider) {
            if ($provider->support($type)) {
                return $provider;
            }
        }

        throw new \Exception('No provider found');
    }
}