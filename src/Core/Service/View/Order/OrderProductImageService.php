<?php

namespace App\Core\Service\View\Order;

use App\Core\Entity\Integration;
use App\Core\Entity\OrderProduct;
use App\Core\Service\Fetcher\Api\PrestashopApiService;
use Doctrine\ORM\EntityManagerInterface;

class OrderProductImageService
{

    private Integration $integration;

    public function __construct(private readonly PrestashopApiService $prestashopApiService, private readonly EntityManagerInterface $entityManager) {
    }

    public function getImageUrl(int $productId, int $combinationId): array {
        return $this->prestashopApiService->getProductImages($productId,$combinationId);
    }

    public function getImageUrlBySku($sku): string {
        return $this->prestashopApiService->getProductImagesBySku($sku);
    }

    private function constructImageUrl(int $imageId, string $type = 'home_default'): string {
        return $this->integration->getIntegrationData()->getApiUrl() . '/img/p/' . implode('/', str_split((string) $imageId, 1)) . '/' . $imageId . '-' . $type . '.jpg';
    }

    public function constructImageUrlByImageIdAndIntegration(int $imageId, Integration $integration,  $type = 'home_default'): string {
        $this->integration = $integration;
        $this->prestashopApiService->setConnectionData($integration->getIntegrationData()->getApiUrl(), $integration->getIntegrationData()->getApiToken());

        return $this->integration->getIntegrationData()->getApiUrl() . '/img/p/' . implode('/', str_split((string) $imageId, 1)) . '/' . $imageId . '-' . $type . '.jpg';
    }

    public function getCoverImage(OrderProduct $orderProduct): string {
        $this->prepareApi($orderProduct);
        $imageId =  $this->getImageUrlBySku($orderProduct->getSku());

        return $this->constructImageUrl((int)$imageId);
    }

    private function getIntegrationForProduct(OrderProduct $orderProduct): void {
        $integration = $this->entityManager->getRepository(Integration::class)->findOneBy(['id' => $orderProduct->getStorageId()]);
        if ($integration instanceof Integration) {
            $this->integration = $integration;
        }
    }

    private function prepareApi(OrderProduct $orderProduct): void {
        $this->getIntegrationForProduct($orderProduct);
        $this->prestashopApiService->setConnectionData($this->integration->getIntegrationData()->getApiUrl(), $this->integration->getIntegrationData()->getApiToken());
    }
}