<?php

namespace App\Core\Service\View\Order\Types;

use App\Core\Entity\Order;
use App\Core\Service\View\Order\Types\ViewTypeInterface;
use Doctrine\ORM\EntityManagerInterface;

class PrestashopViewType implements ViewTypeInterface {

    const VIEW_TYPE = 'prestashop';

    public function __construct(private readonly EntityManagerInterface $entityManager) {}
    public function support(string $type): bool {
        return $type === self::VIEW_TYPE;
    }

    public function getView() {
        return $this->entityManager->getRepository(Order::class)->findAll();
    }

}