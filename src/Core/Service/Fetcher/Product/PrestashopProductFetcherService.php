<?php

namespace App\Core\Service\Fetcher\Product;

use App\Core\Service\Fetcher\Api\PrestashopApiService;

class PrestashopProductFetcherService {
    private const LIMIT = NULL;

    public function __construct(
        private readonly PrestashopApiService $prestashopApiService
    ) {
    }

    public function getProductById($productId, $baseUrl, $apiKey): array {
        $this->prestashopApiService->setConnectionData($baseUrl, $apiKey);
        return $this->prestashopApiService->getResource('products', '[id, weight]', 'JSON', self::LIMIT,  ['id' => $productId], [], true);
    }
    private function getProductByName($filter): array {
        return $this->prestashopApiService->getResource('products', '[id,reference,name,stock_availables[id,id_product_attribute]]', 'JSON', self::LIMIT, $filter);
    }

    private function getProductsWithCombination($filter): array {
        $products = $this->prestashopApiService->getResource('products', '[id,reference,name,stock_availables[id,id_product_attribute]]', 'JSON', self::LIMIT, $filter);
        $combinations = $this->prestashopApiService->getResource('combinations', 'full', 'JSON', self::LIMIT, $filter);

        $finalProducts = [];
        foreach ($products['data'] as $product) {
            $finalProducts[$product['id']] = $product;
        }

        foreach ($combinations['data'] as $combination) {
            if (!isset($finalProducts[$combination['id_product']])) {
                $p = $this->prestashopApiService->getResource('products', '[id,reference,name,stock_availables[id,id_product_attribute]]', 'JSON', self::LIMIT, ['id' => $combination['id_product']], [], true);
                if (!empty($p['data'])) {
                    $finalProducts[$combination['id_product']] = $p['data'];
                }
            }
        }
        foreach ($finalProducts AS &$product){
            if(count($product['associations']['stock_availables']) > 1){
                $product['combination'] = true;
            } else {
                $product['combination'] = false;
            }
        }

        return array_values($finalProducts);
    }

    public function getProducts($field, $value): array {
        $filter = [$field => "%[$value]%"];
        return match ($field) {
            'name' => $this->getProductByName($filter),
            default => $this->getProductsWithCombination($filter)
        };
    }


}