<?php

namespace App\Core\Service\Fetcher\Order;

use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Entity\OrderDelivery;
use App\Core\Entity\OrderInvoice;
use App\Core\Entity\OrderStatus;
use App\Core\Service\Communicator\CommunicatorService;
use App\Core\Service\Fetcher\Api\BaselinkerApiService;
use App\Core\Service\OrderStatusService;
use App\Core\Service\View\Order\OrderProductImageService;
use App\Core\Taxonomy\CommunicatorTaxonomy;
use App\Core\Taxonomy\DateTaxonomy;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\SerializerInterface;

class BaselinkerOrderFetcherService {

    const TO_VERIFY = '127548';
    const ADDITIONAL_FETCH_INTERVAL_TIME = 2;
    const ORDERS_LIMIT = 100;
    const INT_TO_STRING_FIELDS = ['date_confirmed', 'date_add', 'date_in_status'];
    private int $orderCounter = 0;
    private array $errors = [];
    private array $ordersFetched = [];
    private string $maxExecutionTime;
    private float $safetyMargin;
    private float $startTime;

    public function __construct(
        private readonly BaselinkerApiService     $baselinkerApiService,
        private readonly EntityManagerInterface   $entityManager,
        private readonly SerializerInterface      $serializer,
        private readonly OrderProductImageService $orderProductImageService,
        private readonly OrderStatusService       $orderStatusService, private readonly CommunicatorService $communicatorService
    ) {
        $this->initializeExecutionTime();
        $this->safetyMargin = 0.5; // 50% of max execution time
    }

    private function initializeExecutionTime(): void {
        $executionTime = ini_get('max_execution_time');
        $this->maxExecutionTime = ($executionTime) ? $executionTime : PHP_INT_MAX;
    }

    public function fetchLastOrders(Integration $integration): array {
        $this->startTime = microtime(true);
            $this->runOrdersFetchLoop($integration);

        return [
            'orderCounter' => $this->orderCounter,
            'ordersFetched' => $this->ordersFetched,
            'errors' => $this->errors,
        ];
    }

    private function runOrdersFetchLoop(Integration $integration): void {
        $last = $this->getLastConfirmedDateForIntegration([$integration->getId()]);
        $dateConfirmedFrom = 1719871200;

        if (NULL !== reset($last)) {
            $dateConfirmedFrom = reset($last) + self::ADDITIONAL_FETCH_INTERVAL_TIME;
        }

        $ordersData = $this->baselinkerApiService->getOrders(
            [
                'filter_order_source' => $integration->getSettingsByKey('TYPE'),
                'filter_order_source_id' => (int)$integration->getName(),
                'date_confirmed_from' => $dateConfirmedFrom,
            ]
        );

        if (!empty($ordersData['data']['orders'])) {
            $ordersToSave = array_filter($ordersData['data']['orders'], function ($order) use ($integration) {
                return !$this->entityManager->getRepository(Order::class)->findOneBy(['order_source_id' => $integration->getId(), 'shop_order_id' => (string)$order['order_id']]);
            });
            $this->createOrders(array_reverse($ordersToSave), $integration);
            $this->ordersFetched = array_merge($this->ordersFetched, $ordersToSave);
        }

        $elapsedTime = microtime(true) - $this->startTime;
        if ($elapsedTime >= $this->maxExecutionTime * $this->safetyMargin) {
            return;
        }

        if (isset($ordersData['data']['orders']) && self::ORDERS_LIMIT === count($ordersData['data']['orders'])) {
            $this->runOrdersFetchLoop($integration);
        }
    }

    private function getLastConfirmedDateForIntegration(array $integrationIds) {
        return $this->entityManager->getRepository(Order::class)->getLastConfirmedForIntegration($integrationIds);
    }

    private function checkIfOrderExists($orderId): bool {
        return $this->entityManager->getRepository(Order::class)->checkIfOrderExists($orderId) instanceof Order;
    }

    private function createOrders($orders, Integration $integration): void {
        foreach ($orders as $order) {
//            $elapsedTime = microtime(true) - $this->startTime;
//            if ($elapsedTime >= $this->maxExecutionTime * $this->safetyMargin) {
//                return;
//            }

            $this->createOrder($order, $integration);
        }
    }

    private function createOrder($order, Integration $integration): void {
        try {

            if (!$this->entityManager->contains($integration)) {
                $integration = $this->entityManager->find(Integration::class, $integration->getId());
                if ($integration === null) {
                    throw new \Exception('Integration entity not found in the database');
                }
            }
            $this->entityManager->refresh($integration);
            $orderInvoiceFieldNames = $this->entityManager->getClassMetadata(OrderInvoice::class)->getFieldNames();
            $order['orderInvoice'] = array_intersect_key($order, array_flip($orderInvoiceFieldNames));
            $orderDeliveryFieldNames = $this->entityManager->getClassMetadata(OrderDelivery::class)->getFieldNames();
            $order['orderDelivery'] = array_intersect_key($order, array_flip($orderDeliveryFieldNames));
            $order = array_filter($order, function($k) use ($orderInvoiceFieldNames) { return !in_array($k, $orderInvoiceFieldNames); }, ARRAY_FILTER_USE_KEY);
            $order = array_filter($order, function($k) use ($orderDeliveryFieldNames) { return !in_array($k, $orderDeliveryFieldNames); }, ARRAY_FILTER_USE_KEY);
            $this->convertIntToStringFields($order, self::INT_TO_STRING_FIELDS);
            $order['order_source'] = $integration->getSettingsByKey('INTERNAL_NAME');
            $orderId = $this->generateOrderId($order, $integration);
            $order['shop_order_id'] = $order['order_id'];
            $order['order_id'] = $orderId;
            $order['order_source_id'] = $integration->getId();
            $orderEntity = $this->serializer->deserialize(json_encode($order), Order::class, 'json');
            foreach ($orderEntity->getProducts() as $product) {
                $product->setStorageId($integration->getId());
                $product->setLocation('');
                $product->setFullPrice($product->getPriceBrutto());
                try {
                    $product->setCoverImageUrl($this->orderProductImageService->getCoverImage($product));

                } catch (\Exception $e) {
                    $product->setCoverImageUrl(null);
                }
                $orderEntity->addProduct($product);
            }
            $statusEntity = $this->getStatus();
            $orderEntity->setInternalStatusId($statusEntity);
            $orderEntity->setFullPrice($orderEntity->getFullPriceCountByProducts());
            $this->entityManager->persist($orderEntity);
            $this->entityManager->flush();
            $this->orderCounter++;
        } catch (\Exception $e) {
            $this->errors[] = $e->getMessage();
        }
    }

    private function getStatus() {
        return $this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_NEW]);
    }

    public function getOrderSources(): array {
        return $this->baselinkerApiService->getSources();
    }

    public function getOrderById(int $idOrder) {
        return $this->baselinkerApiService->getOrder($idOrder);
    }

    public function getOrders() {
        return $this->baselinkerApiService->getOrders();
    }

    public function getStatusesList() {
        return $this->baselinkerApiService->getStatusesList();
    }

    private function convertIntToStringFields(array &$order, array $fields) {
        foreach ($fields as $field) {
            if (isset($order[$field]) && is_int($order[$field])) {
                $order[$field] = (string) $order[$field];
            }
        }
    }

    private function replaceOrderSourceById(array &$order, array $sources) {
        if (isset($order['order_source']) && isset($order['order_source_id'])) {
            $source = $order['order_source'];
            $sourceId = $order['order_source_id'];

            if (isset($sources[$source][$sourceId])) {
                $order['order_source'] = $sources[$source][$sourceId];
            }
        }
    }

    private function generateOrderId(array $order, Integration $integration): string {
        $count = $this->entityManager->getRepository(Order::class)->count([]);
        return (string) ($integration->getId() . '-' . $order['order_id'] . '-' . ($count + 1));
    }

    public function checkOrdersState(Integration $integration, array $orders = []): bool {
        $checkOrders = 0;
        foreach ($orders as $order) {
            $apiResponse = $this->baselinkerApiService->getOrder($order->getShopOrderId());
            if ($apiResponse['data']['status'] !== 'SUCCESS' && count($apiResponse['data']['orders']) !== 1) {
                continue;
            }
            if ($this->checkIfProductIsPaid($apiResponse['data']['orders'][0]['products'], $apiResponse['data']['orders'][0])) {
                $status = OrderTaxonomy::STATUS_VERIFIED;
            } else {
                $status = OrderTaxonomy::STATUS_UNPAID;
            }
            if ($order->getInternalStatusId()->getName() !== $status) {
                $order->setInternalStatusId($this->orderStatusService->getStatus($status));
                $this->entityManager->persist($order);
                $this->entityManager->flush();
            }
            $checkOrders++;
        }
        return $checkOrders;
    }

    public function checkUnpaidOrdersState(Integration $integration, array $orders = []): bool {
        $checkOrders = 0;
        foreach ($orders as $order) {
            $apiResponse = $this->baselinkerApiService->getOrder($order->getShopOrderId());
            if ($apiResponse['data']['status'] !== 'SUCCESS' && count($apiResponse['data']['orders']) !== 1) {
                continue;
            }
            if ($this->checkIfProductIsPaid($apiResponse['data']['orders'][0]['products'], $apiResponse['data']['orders'][0])) {
                $status = OrderTaxonomy::STATUS_VERIFIED;
            } else {
                $status = OrderTaxonomy::STATUS_UNPAID_3_DAYS;
            }
            if ($order->getInternalStatusId()->getName() !== $status) {
                $order->setInternalStatusId($this->orderStatusService->getStatus($status));
                $this->entityManager->persist($order);
                $this->entityManager->flush();
            }
            $checkOrders++;
        }
        return $checkOrders;
    }

    public function checkUnpaidMoreThan3daysOrdersState(Integration $integration, array $orders = []): bool {
        $checkOrders = 0;
        foreach ($orders as $order) {
            $apiResponse = $this->baselinkerApiService->getOrder($order->getShopOrderId());
            if ($apiResponse['data']['status'] !== 'SUCCESS' && count($apiResponse['data']['orders']) !== 1) {
                continue;
            }
            if ($this->checkIfProductIsPaid($apiResponse['data']['orders'][0]['products'], $apiResponse['data']['orders'][0])) {
                $status = OrderTaxonomy::STATUS_VERIFIED;
            } else {
                $status = OrderTaxonomy::STATUS_UNPAID;
            }
            if ($order->getInternalStatusId()->getName() !== $status) {
                $order->setInternalStatusId($this->orderStatusService->getStatus($status));
                $this->entityManager->persist($order);
                $this->entityManager->flush();
            }
            $checkOrders++;
            $this->communicatorService->sendTelegramMessage(CommunicatorTaxonomy::TELEGRAM_INFO_CHAT, 'Baselinker: Order ' . $order->getShopOrderId() . ' status changed to ' . $status . ' because of unpaid more than 3 days.');
        }
        return $checkOrders;
    }

    private function checkIfProductIsPaid($products, $order): bool{
        if($order['payment_method_cod'] === '1'){
            return true;
        }

        $amount = 0;
        $amount = $order['delivery_price'];
        foreach ($products as $product){
            $amount += $product['price_brutto'] * $product['quantity'];
        }
        if (abs((float)$amount - (float)$order['payment_done']) < 0.00001) {
            return true;
        }
        return false;
    }

    public function fetchOrder($orderId, Integration $integration) {
        $ordersData = $this->baselinkerApiService->getOrders(
            [
                'order_id' => (int)$orderId
            ]
        );

        if (!empty($ordersData['data']['orders'])) {
            $ordersToSave = array_filter($ordersData['data']['orders'], function ($order) use ($integration) {
                return !$this->entityManager->getRepository(Order::class)->findOneBy(['order_source_id' => $integration->getId(), 'shop_order_id' => (string)$order['order_id']]);
            });
            $this->createOrders(array_reverse($ordersToSave), $integration);
        }
    }
}
