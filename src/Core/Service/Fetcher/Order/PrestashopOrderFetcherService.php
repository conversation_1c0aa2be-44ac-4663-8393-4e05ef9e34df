<?php

namespace App\Core\Service\Fetcher\Order;

use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Entity\OrderDelivery;
use App\Core\Entity\OrderInvoice;
use App\Core\Entity\OrderProduct;
use App\Core\Exception\OrderExistsException;
use App\Core\Exception\OrderFetchException;
use App\Core\Message\FetchSingleOrderMessage;
use App\Core\Rules\RuleTrigger;
use App\Core\Service\Fetcher\Api\PrestashopApiService;
use App\Core\Service\View\Order\OrderProductImageService;
use App\Core\Utility\Money;
use DateTime;
use DateTimeZone;
use Doctrine\DBAL\LockMode;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Messenger\MessageBusInterface;

readonly class PrestashopOrderFetcherService {
    
    private const ORDERS_COUNT_STOP_LIMIT = 50;

    private const TIME_PERIOD = '-2days';
    
    private const ORDER_FETCH_LIMIT = self::ORDERS_COUNT_STOP_LIMIT;
    
    public function __construct(
        private PrestashopApiService $prestashopApiService,
        private EntityManagerInterface $entityManager,
        private OrderProductImageService $orderProductImageService,
        private MessageBusInterface $messageBus,
        private RuleTrigger $ruleTrigger,
    ) {}
    
    public function fetchOrder($orderId, Integration $integration): true {
        try {
            xdebug_break();
            $this->prestashopApiService->setConnectionData($integration->getIntegrationData()->getApiUrl(), $integration->getIntegrationData()->getApiToken());
            $filters = ['id' => $orderId];
            $apiOrder = $this->prestashopApiService->getResource('sca_order', 'full', 'JSON', 1, $filters, [], true);
            if (null !== $apiOrder['error']) {
                throw new Exception('Can not fetch order id ' . $orderId);
            }
            $this->entityManager->beginTransaction();
            $orderExistsQuery = $this->entityManager->createQuery('SELECT o.id FROM App\Core\Entity\Order o where o.order_source_id = :integrationId and o.shop_order_id = :shopOrderId');
            $orderExistsQuery->setParameter('integrationId', $integration->getId());
            $orderExistsQuery->setParameter('shopOrderId', $apiOrder['data']['id']);
            $results = $orderExistsQuery->setLockMode(LockMode::PESSIMISTIC_WRITE)->getOneOrNullResult();

            if (is_null($results)) {
                if (!isset($apiOrder['data']) || !isset($apiOrder['data']['associations']) || !isset($apiOrder['data']['associations']['order_rows'])) {
                    throw new Exception('Error while fetching order with id: ' . $orderId . ' - Missing data');
                }
                $apiOrderProductsCount = count($apiOrder['data']['associations']['order_rows']);
                $order = $this->createOrder($apiOrder['data'], $integration);
                if (!is_null($order)) {
                    if ($apiOrderProductsCount !== $order->getProducts()->count()) {
                        $this->entityManager->remove($order);
                        $this->entityManager->flush();
                        throw new OrderFetchException($apiOrder['data']['id']);
                    }
                }
                $this->entityManager->commit();
                $this->ruleTrigger->triggerEvent('order.fetched.after.save', ['order' => $order]);

                if ($order->isPaid()) {
                    $this->ruleTrigger->triggerEvent('order.is.paid', ['order' => $order]);
                }
                return true;
            } else {
                throw new OrderExistsException($apiOrder['data']['id']);
            }
        } catch (OrderExistsException $exception) {

            $this->entityManager->rollback();
            $this->entityManager->clear();

            return TRUE;

        } catch (OrderFetchException $exception) {

            $this->entityManager->rollback();
            $this->entityManager->clear();

            throw new Exception($exception->getMessage() . ' - ShopOrderId: ' . $orderId);

        } catch (Exception|\TypeError $exception) {

            $this->entityManager->rollback();
            $this->entityManager->clear();

            throw new Exception($exception->getMessage() . ' - ShopOrderId: ' . $orderId);

        }
    }
    
    private function createOrder(array $orderApi, Integration $integration): ?Order {
        $error = [];
        foreach (['customer', 'currency','address_delivery', 'address_invoice', 'carrier'] as $fieldName) {
            if (!isset($orderApi['associations'][$fieldName])) {
                $error[] = 'Field '  . $fieldName . ' is missing';
            }
        }
        if (!empty($error)) {
            throw new Exception('Error while fetching order: ' . implode(', ', $error));
        }

        $customerMessage = $orderApi['message'] ?? null;
        $currency = $orderApi['associations']['currency'][0] ?? null;
        $customer = $orderApi['associations']['customer'][0] ?? null;
        $addressDelivery = $orderApi['associations']['address_delivery'][0] ?? null;
        $addressInvoice = $orderApi['associations']['address_invoice'][0] ?? null;
        $carrier = $orderApi['associations']['carrier'][0] ?? null;
        $orderEntity = new Order();
        $isPaid = $this->isPaid($integration, $orderApi);
        $orderEntity->setPaid($isPaid);
        $orderEntity->setCancelled(false);
        $orderEntity->setInternalStatusId($integration->getIntegrationData()->getFetchStatus());
        $orderEntity->setShopOrderId($orderApi['id']);
        $orderEntity->setExternalOrderId($orderApi['id']);
        $orderEntity->setOrderSource($integration->getName());
        $orderEntity->setOrderSourceInfo('');
        $orderEntity->setOrderSourceId($integration->getId());
        $orderEntity->setOrderStatusId(127548);
        $orderEntity->setDateAdd(new \DateTimeImmutable($orderApi['date_add'], new DateTimeZone('Europe/Warsaw')));
        $orderEntity->setDateConfirmed(new \DateTimeImmutable('1970-01-01 00:00:00', new DateTimeZone('Europe/Warsaw')));
        $orderEntity->setDateInStatus(new \DateTimeImmutable($orderApi['date_upd'], new DateTimeZone('Europe/Warsaw')));
        $orderEntity->setConfirmed(true);
        $orderEntity->setUserLogin($customer['email']);
        $orderEntity->setCurrency($currency['iso_code']);
        $orderEntity->setPaymentMethod($orderApi['payment']);
        $orderEntity->setPaymentMethodCod($orderApi['module'] === 'ps_cashondelivery');
        $orderEntity->setUserComments($customerMessage ?? '');
        $orderEntity->setAdminComments('');
        $orderEntity->setEmail($customer['email']);
        $orderEntity->setPhone($this->filterPhoneNumber([
            $addressDelivery["phone"],
            $addressDelivery["phone_mobile"],
            $addressInvoice["phone"],
            $addressInvoice["phone_mobile"]
        ]));
        $orderEntity->setExtraField1('');
        $orderEntity->setExtraField2('');
        $orderEntity->setCustomExtraFields(null);
        $orderEntity->setOrderPage('');
        $orderEntity->setPickState(0);
        $orderEntity->setPackState(0);
        [
            $discountPercentage,
            $isDiscount
        ] = $this->checkDiscount($orderApi);
        if ($isDiscount) {
            $orderEntity->setDiscountValue(Money::fromFloat(floatval($orderApi['total_discounts']))->getAmount());
            if (isset($orderApi['sca_orderoverride'])) {
                if (json_validate($orderApi['sca_orderoverride'])) {
                    $decoded = json_decode($orderApi['sca_orderoverride'], true);
                    if (isset($decoded['cart_rules'][0]['name'])) {
                        $orderEntity->setDiscountName($decoded['cart_rules'][0]['name']);
                    }
                }
            }
        }
        if ($orderEntity->isPaid()) {
            $orderEntity->setPaymentDone(Money::fromFloat(floatval($orderApi['total_paid']))->getAmount());
        } else {
            $orderEntity->setPaymentDone(Money::fromFloat(floatval($orderApi['total_paid_real']))->getAmount());
        }


        if ($orderApi['module'] === 'ps_cashondelivery') {
            $orderEntity->setPaymentDone(0);
        }
        $orderEntity->setFullPrice(Money::fromFloat(floatval($orderApi['total_products_wt']))->getAmount());
        foreach ($orderApi['associations']['order_rows'] as $key => $apiProduct) {
            $orderProductEntity = new OrderProduct();
            $orderProductEntity->setStorage('shop');
            $orderProductEntity->setStorageId($integration->getId());
            $orderProductEntity->setOrderProductId($apiProduct['product_id']);
            $orderProductEntity->setProductId($apiProduct['product_id']);
            $orderProductEntity->setVariantId(0);
            $orderProductEntity->setName($apiProduct['product_name']);
            $orderProductEntity->setSku($apiProduct['product_reference']);
            $orderProductEntity->setEan($apiProduct['product_ean13']);
            $orderProductEntity->setLocation('');
            $orderProductEntity->setWarehouseId(0);
            $orderProductEntity->setAuctionId(0);
            $orderProductEntity->setAttributes('');
            $orderProductEntity->setWidth($apiProduct['width']);
            $orderProductEntity->setHeight($apiProduct['height']);
            $orderProductEntity->setDepth($apiProduct['depth']);
            $weight = (int) (round(floatval($apiProduct['weight'] * 100)));
            $orderProductEntity->setWeight($weight);
            if ($isDiscount) {
                $discountedPrice = Money::fromFloat(floatval($apiProduct['unit_price_tax_incl']))->getAmount() - (Money::fromFloat($apiProduct['unit_price_tax_incl'])->getAmount() / 100 * $discountPercentage);
                $orderProductEntity->setPriceBrutto($discountedPrice);
                $orderProductEntity->setFullPrice(Money::fromFloat($apiProduct['unit_price_tax_incl'])->getAmount());
            }  else {
                $orderProductEntity->setPriceBrutto(Money::fromFloat(floatval($apiProduct['unit_price_tax_incl']))->getAmount());
                $orderProductEntity->setFullPrice(Money::fromFloat(floatval($apiProduct['unit_price_tax_incl']))->getAmount());
            }
            //@TODO Where it comes from?
            // Resource na product -> tax rate rules
            $orderProductEntity->setTaxRate(800);
            $orderProductEntity->setQuantity($apiProduct['product_quantity']);
            $orderProductEntity->setBundleId(0);
            $orderProductEntity->setOrderId($orderEntity);
            $orderProductEntity->setCoverImageUrl($this->orderProductImageService->constructImageUrlByImageIdAndIntegration((int)$apiProduct['images'], $integration));
            $orderEntity->addProduct($orderProductEntity);
        }

        $orderDeliveryEntity = new OrderDelivery();
        $orderDeliveryEntity->setOrder($orderEntity);
        $orderDeliveryEntity->setDeliveryAddress($addressDelivery['address1'] . ' ' . $addressDelivery['address2']);
        $orderDeliveryEntity->setDeliveryMethod($carrier['name']);
        $orderDeliveryEntity->setDeliveryPrice(Money::fromFloat(floatval($orderApi['total_shipping_tax_incl']))->getAmount());
        $orderDeliveryEntity->setDeliveryPackageModule('');
        $orderDeliveryEntity->setDeliveryPackageNr('');
        $orderDeliveryEntity->setDeliveryFullname($addressDelivery['firstname'] . ' ' . $addressDelivery['lastname']);
        $orderDeliveryEntity->setDeliveryCompany($addressDelivery['company'] ?? '');
        $orderDeliveryEntity->setDeliveryPostcode($addressDelivery['postcode']);
        $orderDeliveryEntity->setDeliveryCity($addressDelivery['city']);
        $orderDeliveryEntity->setDeliveryState('');
        $orderDeliveryEntity->setDeliveryCountry($addressDelivery['country_name']);
        $orderDeliveryEntity->setDeliveryCountryCode($addressDelivery['country_iso_code']);
        if (null !== $orderApi['sca_orderdelivery_point_name']) {
            $orderDeliveryEntity->setDeliveryPointName($orderApi['sca_orderdelivery_point_name']);
        }
        else {
            $orderDeliveryEntity->setDeliveryPointName('');
        }
        $orderDeliveryEntity->setDeliveryPointId('');
        $orderDeliveryEntity->setDeliveryPointAddress('');
        $orderDeliveryEntity->setDeliveryPointPostcode('');
        $orderDeliveryEntity->setDeliveryPointCity('');
        $orderEntity->setOrderDelivery($orderDeliveryEntity);

        $orderInvoiceEntity = new OrderInvoice();
        $orderInvoiceEntity->setOrder($orderEntity);
        $orderInvoiceEntity->setInvoiceFullname($addressInvoice['firstname'] . ' ' . $addressInvoice['lastname']);
        $orderInvoiceEntity->setInvoiceCompany($addressInvoice['company'] ?? '');
        $orderInvoiceEntity->setInvoiceNip($addressInvoice['vat_number']);
        $orderInvoiceEntity->setInvoiceAddress($addressInvoice['address1'] . ' ' . $addressInvoice['address2']);
        $orderInvoiceEntity->setInvoicePostcode($addressInvoice['postcode']);
        $orderInvoiceEntity->setInvoiceCity($addressInvoice['city']);
        $orderInvoiceEntity->setInvoiceState('');
        $orderInvoiceEntity->setInvoiceCountry($addressInvoice['country_name']);
        $orderInvoiceEntity->setInvoiceCountryCode($addressInvoice['country_iso_code']);
        $orderInvoiceEntity->setWantInvoice(0);
        $orderEntity->setOrderInvoice($orderInvoiceEntity);

        $countQuery = $this->entityManager->createQuery('SELECT COUNT(o.id) FROM App\Core\Entity\Order o');
        $count = $countQuery->getSingleScalarResult();
        $orderId = $integration->getId() . '-' . $orderApi['id'] . '-' . $count + 1;
        $orderEntity->setOrderId($orderId);
        $this->entityManager->persist($orderEntity);

//        $this->ruleTrigger->triggerEvent('order.fetched.before.save', ['order' => $orderEntity]);
        $this->entityManager->flush();

        return $orderEntity;
    }
    
    private function filterPhoneNumber($arrayOfPhoneNumbers): string {
        $filteredPhoneNo = array_map(function ($phoneNo) {
            return str_replace([
                ' ',
                '+48',
                '-'
            ], '', trim($phoneNo));
        }, array_filter($arrayOfPhoneNumbers, function ($phoneNo) {
            return !empty($phoneNo);
        }));
        
        return !empty($arrayOfPhoneNumbers) ? reset($filteredPhoneNo) : '';
    }
    
    private function checkDiscount($orderApi): array {
        $isDiscount = false;
        $discount = Money::fromFloat(floatval($orderApi['total_discounts']))->getAmount();
        $discountPercentage = 0;
        if ($discount > 0) {
            $discountPercentage = $discount * 100 / Money::fromFloat(floatval($orderApi['total_products_wt']))->getAmount();
            $isDiscount = true;
        }
        
        return [
            $discountPercentage,
            $isDiscount
        ];
    }

    public function fetchAllOrders(Integration $integration, $lastId = null, int &$newOrders = 0, $filterExisingIds = true): int {
        $this->prestashopApiService->setConnectionData($integration->getIntegrationData()->getApiUrl(), $integration->getIntegrationData()->getApiToken());
        $time = new DateTime('now ' . self::TIME_PERIOD);
        $filters = ['date_add' => '>[' . $time->format('Y-m-d%20H:i:s') . ']'];
//        if ($filterExisingIds) {
//            $ids = $this->entityManager->getRepository(Order::class)->findAllSinceDate($time->getTimestamp());
//            if (!empty($ids)) {
//                $filters['id'] = '![' . implode('|', $ids) . ']';
//            }
        if (!is_null($lastId)) {
            $filters['id'] = '>[' . $lastId . ']';
        }
        $apiOrders = $this->prestashopApiService->getResource('sca_order', '[id]', 'JSON', self::ORDER_FETCH_LIMIT, $filters, '[id_ASC]');
        if (!is_null($apiOrders['data'])) {
            $newOrders += count($apiOrders['data']);
            $fetchedOrdersCount = count($apiOrders['data']);
            $ordersToSave = array_filter($apiOrders['data'], function ($order) use ($integration) {
                return !$this->entityManager->getRepository(Order::class)->findOneBy([
                    'order_source_id' => $integration->getId(),
                    'shop_order_id' => $order['id']
                ]);
            });
            if (!empty($ordersToSave)) {
                foreach ($ordersToSave as $orderApi) {
                    $message = new FetchSingleOrderMessage($orderApi['id'], $integration->getId());
                    $this->messageBus->dispatch($message);
                }
            }
            if ($fetchedOrdersCount == self::ORDERS_COUNT_STOP_LIMIT) {
                $this->fetchAllOrders($integration, end($apiOrders['data'])['id'], $newOrders, false);
            }
        }
        
        return $newOrders;
    }


    public function fetchMultipleOrderIds(Integration $integration, $lastId = null, int &$newOrders = 0, $filterExisingIds = true): int {
        $this->prestashopApiService->setConnectionData($integration->getIntegrationData()->getApiUrl(), $integration->getIntegrationData()->getApiToken());
        $time = new DateTime('now ' . self::TIME_PERIOD);
        $filters = ['date_add' => '>[' . $time->format('Y-m-d%20H:i:s') . ']'];
//        if ($filterExisingIds) {
//            $ids = $this->entityManager->getRepository(Order::class)->findAllSinceDate($time->getTimestamp());
//            if (!empty($ids)) {
//                $filters['id'] = '![' . implode('|', $ids) . ']';
//            }
        if (!is_null($lastId)) {
            $filters['id'] = '>[' . $lastId . ']';
        }
        $apiOrders = $this->prestashopApiService->getResource('sca_order', '[id]', 'JSON', self::ORDER_FETCH_LIMIT, $filters, '[id_ASC]');
        if (!is_null($apiOrders['data'])) {
            $fetchedOrdersCount = count($apiOrders['data']);
            $ordersToSave = array_filter($apiOrders['data'], function ($order) use ($integration) {
                return !$this->entityManager->getRepository(Order::class)->findOneBy([
                    'order_source_id' => $integration->getId(),
                    'shop_order_id' => $order['id']
                ]);
            });
            $newOrders += count($ordersToSave);
            if (!empty($ordersToSave)) {
                foreach ($ordersToSave as $orderApi) {
                    $message = new FetchSingleOrderMessage($orderApi['id'], $integration->getId());
                    $this->messageBus->dispatch($message);
                }
            }
            if ($fetchedOrdersCount == self::ORDERS_COUNT_STOP_LIMIT) {
                $this->fetchAllOrders($integration, end($apiOrders['data'])['id'], $newOrders, false);
            }
        }

        return $newOrders;
    }


    
    public function checkOrdersState(Integration $integration, array $orders = []): bool {
        return $this->commonStatusChecker($integration, $orders);
    }
    
    public function commonStatusChecker(Integration $integration, array $orders = []): bool {
        $this->prestashopApiService->setConnectionData($integration->getIntegrationData()->getApiUrl(), $integration->getIntegrationData()->getApiToken());
        $statusCancelled = 6;
        $orderIdList = [];
        $key = 0;
        foreach ($orders as $order) {
            if (isset($orderIdList[$key]) && count($orderIdList[$key]) == self::ORDER_FETCH_LIMIT) {
                $key++;
            }
            $orderIdList[$key][] = $order->getExternalOrderId();
        }
        $orderSortedByExternalOrderId = [];
        foreach ($orders as $order) {
            $orderSortedByExternalOrderId[$order->getExternalOrderId()] = $order;
        }
        foreach ($orderIdList as $orderIdListContent) {
            foreach ($orderIdListContent as $orderIdToCheck) {
                $filters = ['id' => $orderIdToCheck];
                $apiResponse = $this->prestashopApiService->getResource('sca_order', '[id, current_state]', 'JSON', count($orderIdListContent), $filters);
                $apiResponseData = $apiResponse['data'];
                if (null === $apiResponseData) {
                    continue;
                }
                foreach ($apiResponseData as $data) {
                    $currentOrder = $orderSortedByExternalOrderId[$data['id']];
                    $currentState = $data['current_state'];
                    if ($this->isPaid($integration, $data)) {
                        $currentOrder->setPaymentDone($currentOrder->getFullPriceWithDiscount() + $currentOrder->getOrderDelivery()->getDeliveryPrice());
                        $currentOrder->setPaid(true);
                        $this->entityManager->persist($currentOrder);
                        $this->entityManager->flush();
                        $this->ruleTrigger->triggerEvent('order.is.paid', ['order' => $currentOrder]);
                    } else if ($currentState == $statusCancelled) {
                        $currentOrder->setCancelled(true);
                        $this->entityManager->persist($currentOrder);
                        $this->entityManager->flush();
                        $this->ruleTrigger->triggerEvent('order.is.cancelled', ['order' => $currentOrder]);
                    } else {
                        $this->ruleTrigger->triggerEvent('order.is.not.paid', ['order' => $currentOrder]);
                    }
                }
            }
        }
        
        return true;
    }

    public function renewPrice(Order $order, Integration $integration) {
        $this->prestashopApiService->setConnectionData($integration->getIntegrationData()->getApiUrl(), $integration->getIntegrationData()->getApiToken());
        $filters = ['id' => $order->getShopOrderId()];
        $apiOrder = $this->prestashopApiService->getResource('sca_order', 'full', 'JSON', 1, $filters, [], true);
        $orderApi = $apiOrder['data'];
        [
            $discountPercentage,
            $isDiscount
        ] = $this->checkDiscount($orderApi);
        if ($isDiscount) {
            $order->setPaymentDone(Money::fromFloat(floatval($orderApi['total_paid_real']))->getAmount());
            $order->setDiscountValue(Money::fromFloat(floatval($orderApi['total_discounts']))->getAmount());
            if (isset($orderApi['sca_override'])) {
                if (json_validate($orderApi['sca_override'])) {
                    $decoded = json_decode($orderApi['sca_override'], true);
                    if (isset($decoded['cart_rules'][0]['name'])) {
                        $order->setDiscountName($decoded['cart_rules'][0]['name']);
                    }
                }
            }
        }
        else {
            $order->setPaymentDone(Money::fromFloat(floatval($orderApi['total_paid_real']))->getAmount());
        }
        if ($orderApi['module'] === 'ps_cashondelivery') {
            $order->setPaymentDone(0);
        }
        $order->setFullPrice(Money::fromFloat(floatval($orderApi['total_products_wt']))->getAmount());
        $orderDeliveryEntity = $order->getOrderDelivery();
        $orderDeliveryEntity->setDeliveryPrice(Money::fromFloat(floatval($orderApi['total_shipping_tax_incl']))->getAmount());
        foreach ($orderApi['associations']['order_rows'] as $key => $apiProduct) {
            $productToChange = null;
            foreach ($order->getProducts() as $orderProductEntity) {
                if ($orderProductEntity->getOrderProductId() == $apiProduct['product_id']) {
                    $productToChange = $orderProductEntity;
                    break;
                }
                continue;
            }
            if ($isDiscount) {
                $discountedPrice = Money::fromFloat(floatval($apiProduct['unit_price_tax_incl']))->getAmount() - (Money::fromFloat($apiProduct['unit_price_tax_incl'])->getAmount() / 100 * $discountPercentage);
                $productToChange->setPriceBrutto($discountedPrice);
                $productToChange->setFullPrice(Money::fromFloat($apiProduct['unit_price_tax_incl'])->getAmount());
            } else {
                $productToChange->setPriceBrutto(Money::fromFloat(floatval($apiProduct['unit_price_tax_incl']))->getAmount());
                $productToChange->setFullPrice(Money::fromFloat(floatval($apiProduct['unit_price_tax_incl']))->getAmount());
            }

            $this->entityManager->persist($productToChange);
        }

        $this->entityManager->persist($orderDeliveryEntity);
        $this->entityManager->persist($order);
        $this->entityManager->flush();
    }

    public function getOrderStates(Integration $integration): array  {
        $this->prestashopApiService->setConnectionData($integration->getIntegrationData()->getApiUrl(), $integration->getIntegrationData()->getApiToken());
        $apiStates = $this->prestashopApiService->getResource('order_states', "[id, paid, shipped, delivery, name]", 'JSON', 0, [], [], false);
        $return = [];
        if (isset($apiStates['data']) && !empty($apiStates['data'])) {
            $return =  $apiStates['data'];
        }
        return $return;
    }

    private function isPaid(Integration $integration, $orderApi): bool {
        $paidStatuses = $integration->getPaidStatusList();
        $isPaid = false;
        foreach ($paidStatuses as $paidStatus) {
            if ((int)$paidStatus->getIntegrationStatusId() === (int) $orderApi['current_state']) {
                $isPaid = true;
                break;
            }
        }
        return $isPaid;
    }
}
