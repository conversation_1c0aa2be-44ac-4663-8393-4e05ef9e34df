<?php

namespace App\Core\Service\Fetcher\Api;

use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class PrestashopApiService implements ApiFetcherInterface {

    private string $baseUrl;
    private string $apiKey;

    public function __construct(private readonly HttpClientInterface $client) {}

    public function setConnectionData(string $baseUrl, string $apiKey) {
        if (!str_starts_with($baseUrl, 'http://') && !str_starts_with($baseUrl, 'https://')) {
            $baseUrl = 'https://' . $baseUrl;
        }
        $this->baseUrl = $baseUrl;
        $this->apiKey = $apiKey;
    }

    public function getOrders(): array
    {
        return $this->getResource('orders', 'full','JSON', 5, []);
    }

    public function getProductImagesBySku($sku) {
        $product = $this->getResource('combinations', 'full', 'JSON', 1, ['reference' => $sku], [], true);
        if (!empty($product['data'])) {
            return $product['data']['associations']['images'][0]['id'] ?? false;
        }
        $product = $this->getResource('products', 'full', 'JSON', 1, ['reference' => $sku], [], true);
        if (!empty($product['data'])) {
            return $product['data']['id_default_image'] ?? false;
        }

        return false;
    }

    public function getProductImages(int $productId, $combinationId): array {
        if($combinationId) {

            return $this->getResource('combinations', 'full', 'JSON', 1, ['id' => $combinationId]);
        }

        return $this->getResource('products', 'full', 'JSON', 1, ['id'=> $productId]);
    }

    public function updateOrderStatusViaHistory(int $orderId, int $newStatusId): array {
        $xmlPayload = '<?xml version="1.0" encoding="UTF-8"?>
        <prestashop xmlns:xlink="http://www.w3.org/1999/xlink">
            <order_history>
                <id_order_state><![CDATA[' . $newStatusId . ']]></id_order_state>
                <id_order><![CDATA[' . $orderId . ']]></id_order>
                <id_employee><![CDATA[1]]></id_employee>
            </order_history>
        </prestashop>';

        $url = $this->baseUrl . '/api/order_histories?ws_key=' . $this->apiKey;

        $response = $this->client->request('POST', $url, [
            'headers' => [
                'Content-Type' => 'application/xml',
                'Accept' => 'application/xml',
            ],
            'body' => $xmlPayload,
        ]);

        return $this->handleResponse($response, 'order_history', 'XML', false);
    }

    public function getResource($endpoint, $display = 'full', $output_format = 'JSON', $limit = 0, $filter = [], $sort = [], $single = false): array {
        $filters = [
            'display' => $display,
            'output_format' => $output_format,
            'date' => 1,
            'filter' => $filter,
        ];
        $limit === 0 ?: $filters['limit'] = $limit  ;
        if (!empty($sort)) {
            $filters['sort'] = $sort;
        }
        $url = $this->prepareConnectionUrl($endpoint, $filters);
        $url = urldecode($url);
        $response = $this->response($url);

        return $this->handleResponse($response, $endpoint, $output_format ,$single);
    }

    private function prepareConnectionUrl(string $endpoint, array $filters): string {
        $url = $this->baseUrl . '/api/' . $endpoint;
        $url .= '?' . http_build_query($filters);

        return $url;
    }

    private function response($url): ResponseInterface {
        return $this->client->request('GET', $url, [
            'headers' => [
                'Authorization' => 'Basic ' . base64_encode($this->apiKey . ':'),
            ],
        ]);
    }
    private function handleResponse($response, $endpoint, $output_format, $single = false): array {
        $statusCode = $response->getStatusCode();
        $content = $response->getContent(FALSE);
        if ('sca_order' === $endpoint) {
            $endpoint = 'orders';
        }
        if ('bl_order' === $endpoint) {
            $endpoint = 'orders';
        }
        if($output_format === "JSON") {
            $encoded = json_decode($content, TRUE);
        } else {
            $encoded[$endpoint] = $content;
        }
        if (in_array($statusCode, [200, 201]) && !empty($encoded)) {
            if ($endpoint) {
                $data = $single ? array_shift($encoded[$endpoint]) : $encoded[$endpoint];
            } else {
                $data = $encoded;
            }

            return ['data' => $data, 'error' => null, 'code' => $statusCode];
        } elseif (!empty($encoded[$endpoint])) {
            return $encoded;
        }
        else {
            $errorMessage = $data['error']['message'] ?? 'Unknown error occurred';

            return ['data' => null, 'error' => "Error ($statusCode): $errorMessage", 'code' => $statusCode];
        }
    }

    public function getOrderCarrier(int $id): array {
        return $this->getResource('order_carriers', 'full', 'JSON', 1, ['id' => $id]);
    }
}
