<?php

namespace App\Core\Service\Fetcher\Api;

use App\Core\Service\Settings;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class BaselinkerApiService implements ApiFetcherInterface {

    private string $baseUrl;
    private string $apiKey;

    public function __construct(
        private readonly HttpClientInterface $client,
        private readonly Settings $settings,
    ) {
        $this->baseUrl = $this->settings->get('BASELINKER_API_URL');
        $this->apiKey = $this->settings->get('BASELINKER_TOKEN');
    }

    public function getOrders(array $filters = []): array {
        $url = $this->baseUrl . '/connector.php';

        $parameters = [
            'method' => 'getOrders',
            'parameters' => json_encode($filters),
        ];

        $response = $this->request($url, $parameters);
        return $this->requestHandler($response);
    }

    public function getSources(): array {
        $url = $this->baseUrl . '/connector.php';

        $parameters = [
            'method' => 'getOrderSources',
        ];

        $response = $this->request($url, $parameters);
        return $this->requestHandler($response);
    }

    public function getOrder($idOrder): array {
        $url = $this->baseUrl;

        $parameters = [
            'method' => 'getOrders',
            'parameters' => json_encode([
                'order_id' => $idOrder,
            ])
        ];

        try {
            $response = $this->request($url, $parameters);
            if (Response::HTTP_OK === $response['statusCode'] && isset($response['data']['status']) && 'SUCCESS' === $response['data']['status']) {
                return $response['data']['orders'][array_key_first($response['data']['orders'])];
            } else {
                throw new \Exception(json_encode($response));
            }

        } catch (\Exception $exception) {
            throw $exception;
        }


        return $this->requestHandler($response);
    }

    public function getStatusesList(): array {
        $url = $this->baseUrl . '/connector.php';

        $parameters = [
            'method' => 'getOrderStatusList',
        ];

        $response = $this->request($url, $parameters);

        return $this->requestHandler($response);
    }

    private function request(string $url, array $parameters): array {
        $encodedParameters = http_build_query($parameters);

        try {
            $response = $this->client->request('POST', $url, [
                'headers' => [
                    'X-BLToken' => $this->apiKey,
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
                'body' => $encodedParameters,
            ]);

            $statusCode = $response->getStatusCode();
            $content = $response->getContent(false);
            $data = json_decode($content, true);

            return ['statusCode' => $statusCode, 'data' => $data];
        } catch (\Exception $e) {
            return ['statusCode' => 500, 'data' => null, 'error' => 'Failed to retrieve data: ' . $e->getMessage()];
        }
    }

    private function requestHandler(array $response): array {
        $statusCode = $response['statusCode'];
        $data = $response['data'];

        if ($statusCode === 200) {
            return ['data' => $data, 'error' => null, 'code' => $statusCode];
        } else {
            $errorMessage = $data['error']['message'] ?? 'Unknown error occurred';
            return ['data' => null, 'error' => "Error ($statusCode): $errorMessage", 'code' => $statusCode];
        }
    }
}
