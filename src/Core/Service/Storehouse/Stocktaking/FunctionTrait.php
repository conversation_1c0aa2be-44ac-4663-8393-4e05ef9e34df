<?php

namespace App\Core\Service\Storehouse\Stocktaking;
use App\Core\Entity\Ean;
use App\Core\Entity\EanShelfQuantity;
use App\Core\Entity\Rack;
use App\Core\Entity\Shelf;
use App\Core\Entity\Storehouse;

trait  FunctionTrait {

    function traitLoadShelves($filePath): void {
        $firstStorehouse = new Storehouse();
        $firstStorehouse->setInternalName('Pakowania');
        $firstStorehouse->setType('Podstawowy');
        $this->entityManager->persist($firstStorehouse);
        $secondStorehouse = new Storehouse();
        $secondStorehouse->setInternalName('Sklep');
        $secondStorehouse->setType('Podstawowy');
        $this->entityManager->persist($secondStorehouse);
        if (file_exists($filePath)) {
            $content = json_decode(file_get_contents($filePath), TRUE);
            foreach ($content as $containerKey => $container) {
                if (in_array($containerKey, [1, '01', 2, '02', 3, '03', 4, '04', 5, 6, 23])) {
                    $storehouse = $firstStorehouse;
                } else {
                    $storehouse = $secondStorehouse;
                }
                foreach ($container as $rackNo => $shelves) {
                    $rack = new Rack();
                    $rack->setRackNo($containerKey . '.' . $rackNo);
                    $rack->setStorehouse($storehouse);
                    $rack->setEan13($this->generateEan());
                    foreach ($shelves as $key => $shelfName) {
                        $newShelf = new Shelf();
                        $newShelf->setShelfNo($containerKey . '.' . $rackNo . '.' . $shelfName);
                        $newShelf->setEan13($this->generateEan());
                        $newShelf->setType('Podstawowa');
                        $rack->addShelf($newShelf);
                    }
                    $this->entityManager->persist($rack);
                }
            }
        }

        $this->entityManager->flush();
    }
    public function traitMapShelves($filePath): array
    {
        $toAdd = [];
        if (file_exists($filePath)) {
            $content = json_decode(file_get_contents($filePath), TRUE);
            foreach ($content as $shelf) {
                $parts = explode('.', $shelf['shelf_name']);
                $toAdd[$parts[0]][$parts[1]][] = $parts[2];
            }
            $this->sort($toAdd);
        }

        return $toAdd;
    }

    public function sort(&$array): void {
        if (is_array($array) ) {
            if (is_array(reset($array))) {
                ksort($array);
                foreach ($array as &$element) {
                    $this->sort($element);
                }
            } else {
                asort($array);
                $array = array_values($array);
            }
        }
    }

    private function generateEan(): string {
        $date = new \DateTime();
        $time = $date->getTimestamp();

        $code = '20' . str_pad($time, 10, '0');
        $weightflag = true;
        $sum = 0;

        for ($i = strlen($code) - 1; $i >= 0; $i--) {
            $sum += (int)$code[$i] * ($weightflag ? 3 : 1);
            $weightflag = !$weightflag;
        }
        $code .= (10 - ($sum % 10)) % 10;

        return $code;
    }

    public function traitCheckFckingProducts($filePath): void {
        $products = [];
        $duplicates = [];
        if (file_exists($filePath)) {
            $data = json_decode(file_get_contents($filePath), TRUE);

            foreach ($data as $product) {
                $sku = trim($product['sku']);
                if (!empty($products[$sku])) {
                    foreach ($products[$sku] as $addedProduct) {
                        if (trim($product['shelf_name']) === trim($addedProduct['shelf_name'])) {
                            if (isset($duplicates[$sku][$product['shelf_name']])) {
                                foreach ($duplicates[$sku][$product['shelf_name']] as $duplicatedProduct) {
                                    if ($duplicatedProduct['datetime'] === $product['datetime']) {
                                        break 2;
                                    }
                                }
                                $duplicates[$sku][$product['shelf_name']][] = $product;
                            } else {
                                $duplicates[$sku][$product['shelf_name']] = [$addedProduct, $product];
                            }
                        }
                    }
                }
                $products[$sku][] = $product;
            }
        }
        $counter = 0;
        foreach ($duplicates as $duplicate) {
            foreach ($duplicate as $shelf) {
                $counter += count($shelf);
            }
        }
        foreach ($duplicates as &$sku) {
            foreach ($sku as &$shelf) {
                $typedShelf = reset($shelf);
                $foundShelf = $this->entityManager->getRepository(Shelf::class)->findOneBy(['shelf_no' => $typedShelf['shelf_name']]);
                $eans = $this->entityManager->getRepository(Ean::class)->findBy(['ean' => $typedShelf['ean']]);
                foreach ($eans as $ean) {
                    if (NULL !== ($esq = $this->entityManager->getRepository(EanShelfQuantity::class)->findOneBy(['ean' => $ean, 'shelf' => $foundShelf]))) {
                        $shelf['esq'][] = [
                            'quantity' => $esq->getQuantity()
                        ];
                    }
                }
            }
        }
    }
}
