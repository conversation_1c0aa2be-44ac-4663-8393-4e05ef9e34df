<?php

namespace App\Core\Service\Storehouse\Stocktaking;

use App\Core\Entity\DocumentShelfStocktaking;
use App\Core\Entity\DocumentShelfStocktakingData;
use App\Core\Entity\DocumentStocktaking;
use App\Core\Entity\Ean;
use App\Core\Entity\EanShelfQuantity;
use App\Core\Entity\Product;
use App\Core\Entity\Rack;
use App\Core\Entity\Shelf;
use App\Core\Entity\Storehouse;
use App\Core\Message\MigrateStockMessage;
use App\Core\Service\Subiekt\Products\SubiektProductsService;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Messenger\MessageBusInterface;

readonly class StocktakingService {

    use FunctionTrait;
    public function __construct(private EntityManagerInterface $entityManager, private SubiektProductsService $subiektProductsService, private MessageBusInterface $bus) {

    }

    public function migrateStockMessage(MigrateStockMessage $migrateStockMessage): array {
        $shelfName = $migrateStockMessage->getShelfName();
        $storehouse = explode('.', $shelfName)[0];
        if (in_array($storehouse, [1, '01', 2, '02', 3, '03', 4, '04', 5, 6, 23])) {
            $internalNo = 1;
            $sh = $this->entityManager->getRepository(Storehouse::class)->findOneBy(['internal_name' => 'Pakowania']);

        } else {
            $sh =  $this->entityManager->getRepository(Storehouse::class)->findOneBy(['internal_name' => 'Sklep']);;
            $internalNo = 2;
        }
        if (NULL === ($storehouseDocument = $this->entityManager->getRepository(DocumentStocktaking::class)->findOneBy(['storehouse' => $sh, 'finished' => FALSE]))) {
            $storehouseDocument = new DocumentStocktaking();
            $storehouseDocument->setStorehouse($sh);
            $storehouseDocument->setInternalNumber($internalNo);
            $this->entityManager->persist($storehouseDocument);
            $this->entityManager->flush();
        }
        if (NULL === ($shelf = $this->entityManager->getRepository(Shelf::class)->findOneBy(['shelf_no' => $shelfName]))) {
            return ['message' => sprintf('Shelf %s not found', $shelfName), 'info' => 'error'];
        }
        if (NULL === ($shelfDocument = $this->entityManager->getRepository(DocumentShelfStocktaking::class)->findOneBy(['shelf' => $shelf, 'finished' => FALSE]))) {
            $shelfDocument = new DocumentShelfStocktaking();
            $shelfDocument->setShelf($shelf);
        }

        $eanToSearch = $migrateStockMessage->getEan();
        if (NULL === ($ean = $this->createProduct($eanToSearch, $shelf, FALSE))) {
            return ['message' => sprintf('Ean %s not found', $eanToSearch), 'info' => 'error'];
        }
        $stocktakingData = new DocumentShelfStocktakingData();
        $stocktakingData->setEan($ean);
        $stocktakingData->setQuantity($migrateStockMessage->getQuantityAfter());
        $stocktakingData->setOldQuantity($migrateStockMessage->getQuantityBefore());
        $shelfDocument->addStock($stocktakingData);
        $storehouseDocument->addDocumentShelfStocktaking($shelfDocument);
        $this->entityManager->persist($storehouseDocument);
        $this->entityManager->flush();

        return ['message' => sprintf('Correctly added product with ean %s to shelf %s', $eanToSearch, $shelfName), 'info' => 'success'];

    }
    public function migrateStock($filePath): int
    {
        $content = json_decode(file_get_contents($filePath), TRUE);
        $count = 0;
        foreach ($content as $shelf) {
            $message = new MigrateStockMessage($shelf['ean'], $shelf['quantity_after'], $shelf['quantity_before'], $shelf['shelf_name']);
            $this->bus->dispatch($message);
            $count++;
        }

        return $count;
    }

    public function createProduct($ean, Shelf $shelf, $flush = TRUE) {
        if (NULL == ($eanObject = $this->entityManager->getRepository(Ean::class)->findOneBy(['ean' => $ean]))) {
            $subiektProduct = $this->subiektProductsService->findProductByEan($ean);

            if (!isset($subiektProduct['data']['code'])) {
                return NULL;
            }
            $eans = $subiektProduct['data']['eans'];
            $newProduct = new Product();
            $newProduct->setName($subiektProduct['data']['name']);
            $newProduct->setSku($subiektProduct['data']['code']);
            $argEan = new Ean();
            $argEan->setEan($ean);
            $newProduct->addEan($argEan);
            $eanShelfQuantity = new EanShelfQuantity();
            $eanShelfQuantity->setShelf($shelf);
            $eanShelfQuantity->setQuantity(0);
            $eanShelfQuantity->setVirtualQuantity(0);
            $argEan->addEanShelfQuantity($eanShelfQuantity);
            foreach ($eans as $eanCode) {
                if ($ean === $eanCode) {
                    continue;
                }
                $newEan = new Ean();
                $newEan->setEan($eanCode);
                $newProduct->addEan($newEan);
            }
            $this->entityManager->persist($newProduct);
            if ($flush) {
                $this->entityManager->flush();
            }

            return $argEan;
        } else {
            $eanShelfQuantity = $eanObject->getEanShelfQuantities()->filter(function($esq) use ($shelf) {
                return $esq->getShelf() === $shelf;
            })->first();
            if ($eanShelfQuantity) {
                return $eanObject;
            }
            $newEanShelfQuantity = new EanShelfQuantity();
            $newEanShelfQuantity->setShelf($shelf);
            $newEanShelfQuantity->setQuantity(0);
            $newEanShelfQuantity->setVirtualQuantity(0);
            $eanObject->addEanShelfQuantity($newEanShelfQuantity);
            $this->entityManager->persist($eanObject);
            $this->entityManager->persist($newEanShelfQuantity);

            if ($flush) {
                $this->entityManager->flush();
            }

            return $eanObject;
        }
    }

    public function guessScannedObjectType(string $scanNo)
    {
        if (NULL === ($shelf = $this->entityManager->getRepository(Shelf::class)->findOneBy(['shelf_no' => $scanNo]))) {
            if (NULL === ($rack = $this->entityManager->getRepository(Rack::class)->findOneBy(['rack_no' => $scanNo]))) {
                return NULL;
            }
            return $rack;
        }
        return $shelf;
    }

    public function returnScannedData(string $scanNo): array {
        $return = [];
        $object = $this->guessScannedObjectType($scanNo);
        switch (get_class($object)) {
            case Rack::class: {
                return $this->scanRack($object);
            }
            case Shelf::class: {
                return $this->scanShelf($object);
            }
            default:
                return $return;
        }
    }


    public function scanRack(Rack $rack): array {
        $data = [];
        foreach ($rack->getShelves() as $shelf) {
            $data['shelves'][] = $shelf->getShelfNo();
        }

        return $data;
    }
    public function scanShelf(Shelf $shelf): array {
        if (NULL !== ($documentShelfStocktaking = $this->entityManager->getRepository(DocumentShelfStocktaking::class)->findOneBy(['shelf' => $shelf, 'finished' => FALSE]))) {
            $products = $this->getProductFromStock($documentShelfStocktaking->getStock());
            $returnData = ['products' => $products, 'shelfDocument' => $documentShelfStocktaking->getId(), 'shelfId' => $shelf->getId(), 'shelfNo' => $shelf->getShelfNo()];
            if (NULL !== $documentShelfStocktaking->getDocumentStocktaking()) {
                $returnData['storehouseDocument'] = $documentShelfStocktaking->getDocumentStocktaking()->getId();
            }
            return $returnData;
        }
        $products = $this->getProductsFromShelfQuantities($shelf->getEanShelfQuantities());

        return  ['products' => $products, 'shelfDocument' => NULL, 'shelfId' => $shelf->getId(), 'shelfNo' => $shelf->getShelfNo()];
    }

    public function getProductFromStock(Collection $stockCollection): array {
        $products = [];
        foreach ($stockCollection as $stock) {
            $products[] = [
                'id' => $stock->getEan()->getProduct()->getId()->toRfc4122(),
                'name' => $stock->getEan()->getProduct()->getName(),
                'sku' => $stock->getEan()->getProduct()->getSku(),
                'ean' => [
                    'id' => $stock->getEan()->getId(),
                    'ean' => $stock->getEan()->getEan(),
                ],
                'quantity' => $stock->getQuantity(),
                'oldQuantity' => $stock->getOldQuantity()
            ];
        }

        return $products;
    }

    public function getProductsFromShelfQuantities(Collection $shelfQuantities): array {
        $products = [];
        foreach ($shelfQuantities as $eanShelfQuantity) {
            $products[] = [
                'id' => $eanShelfQuantity->getEan()->getProduct()->getId()->toRfc4122(),
                'name' => $eanShelfQuantity->getEan()->getProduct()->getName(),
                'sku' => $eanShelfQuantity->getEan()->getProduct()->getSku(),
                'ean' => [
                    'id' => $eanShelfQuantity->getEan()->getId(),
                    'ean' => $eanShelfQuantity->getEan()->getEan(),
                ],
                'quantity' => $eanShelfQuantity->getQuantity(),
                'oldQuantity' => $eanShelfQuantity->getQuantity(),
            ];
        }

        return $products;
    }

    public function finishMainDocument(DocumentStocktaking $document): array {
        $return = [];
        if ($document->isFinished()) {
            return ['error' => TRUE, 'message' => 'Can\'t finish document already finished', $document->getId()->toRfc4122() => FALSE];
        }

        foreach ($document->getDocumentShelfStocktaking() as $shelfDocument) {
            $return['shelvesDocuments'][] = $this->finishShelfDocument($shelfDocument, FALSE);
        }
        $document->setFinished(TRUE);
        $this->entityManager->persist($document);
        $this->entityManager->flush();
        $return['document'] = [$document->getId()->toRfc4122() => TRUE];

        return $return;
    }

    public function finishShelfDocument(DocumentShelfStocktaking $document, $flush = TRUE): array {
        if ($document->isFinished()) {
            return ['error' => TRUE, 'message' => 'Can\'t finish document already finished', $document->getId()->toRfc4122() => FALSE];
        }
        $document->setFinished(TRUE);
        foreach ($document->getStock() as $stock) {
            $shelf = $document->getShelf();
            $ean = $stock->getEan();
            foreach ($ean->getEanShelfQuantities() as $esq) {
                if ($shelf === $esq->getShelf()) {
                    $esq->setQuantity($stock->getQuantity());
                    $esq->setVirtualQuantity($esq->getQuantity());
                    $this->entityManager->persist($esq);
                }
            }
        }

        $this->entityManager->persist($document);
        if ($flush) {
            $this->entityManager->flush();
        }

        return ['message' => 'success', $document->getId()->toRfc4122() => TRUE];
    }

    public function mapShelves($filePath): array {
        return $this->traitMapShelves($filePath);
    }

    public function loadShelves($filePath) {
        $this->traitLoadShelves($filePath);
    }

    public function checkFckingProducts($filePath) {
        $this->traitCheckFckingProducts($filePath);
    }

}