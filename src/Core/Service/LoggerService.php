<?php

namespace App\Core\Service;

use App\Core\Entity\Basket;
use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Entity\User;
use App\Core\Taxonomy\DatabaseLoggerActionTaxonomy;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Security\Core\User\UserInterface;

class LoggerService {
    
    public function __construct(private LoggerInterface $actionLogger) {}
    
    public function logError(string $errorMessage) {
        $this->actionLogger->info($errorMessage, [
            'entity_id' => null,
            'entity_type' => null,
            'user' => null,
            'action' => 'ERROR',
        ]);
    }
    
    public function logOrderAddedToBasket(Order $order, UserInterface $user, Basket $basket): void {
        $this->actionLogger->info('Dodano zamówienie do koszyka', [
                'entity_id' => $order->getId(),
                'entity_type' => get_class($order),
                'user' => $user,
                'action' => DatabaseLoggerActionTaxonomy::ORDER_ADDED_TO_BASKET,
                'basket_id' => $basket->getId(),
            ]);
    }
    
    public function logBasketGotOrders(Basket $basket, UserInterface $user): void {
        $this->actionLogger->info('Dodano zamówienia do koszyka', [
                'entity_id' => $basket->getId(),
                'entity_type' => get_class($basket),
                'user' => $user,
                'action' => DatabaseLoggerActionTaxonomy::BASKET_ADD_ORDERS,
                'basket_id' => $basket->getId(),
            ]);
    }
    
    public function basketChangeStatus(Basket $basket, $user, $basketStatus): void {
        $this->actionLogger->info('Koszyk zmienił status', [
                'entity_id' => $basket->getId(),
                'entity_type' => get_class($basket),
                'user' => $user,
                'action' => DatabaseLoggerActionTaxonomy::BASKET_CHANGE_STATUS,
                'basketStatus' => $basketStatus
            ]);
    }
    
    public function orderChangeStatus(Order $order, $user, OrderStatus $orderStatusPrev, OrderStatus $orderStatusNew): void {
        $this->actionLogger->info('Zamówienie zmieniło status', [
                'entity_id' => $order->getId(),
                'entity_type' => get_class($order),
                'user' => $user,
                'action' => DatabaseLoggerActionTaxonomy::ORDER_CHANGE_STATUS,
                'order_status_prev_id' => $orderStatusPrev->getId(),
                'order_status_new_id' => $orderStatusNew->getId(),
                'order_status_prev_name' => $orderStatusPrev->getName(),
                'order_status_new_name' => $orderStatusNew->getName(),
            ]);
    }
    
    public function emailSent(array $email, $user, ?Order $order = null, $error = ''): void {
        $fromAddresses[] = $email['from'];
        $toAddresses[] = $email['to'];
        if (!empty($toAddresses) && is_array($toAddresses)) {
            $toAddresses = array_map(function ($address) {
                return $address;
            }, $toAddresses);
        }
        if (!empty($fromAddresses) && is_array($fromAddresses)) {
            $fromAddresses = array_map(function ($address) {
                return $address;
            }, $fromAddresses);
        }
        $this->actionLogger->info('Wysłano email', [
                'entity_id' => is_object($order) ? $order->getId() : null,
                'entity_type' => is_object($order) ? get_class($order) : gettype($order),
                'user' => $user,
                'action' => DatabaseLoggerActionTaxonomy::EMAIL_SENT,
                'email_subject' => $email['subject'],
                'email_sender' => implode(',', $fromAddresses),
                'email_to' => implode(',', $toAddresses),
                'status' => (empty($error)) ? 'Email Wysłany' : $error
            ]);
    }
    
    public function logSubiektOrderAdd(Order $order, UserInterface $user, $response = []) {
        $log = [
            'entity_id' => $order->getId(),
            'entity_type' => get_class($order),
            'user' => $user,
            'action' => DatabaseLoggerActionTaxonomy::SUBIEKT_ORDER_ADD,
            'order_id' => $order->getId(),
            'state' => '',
            'order_ref' => ''
        ];
        if (is_array($response) && !empty($response)) {
            $log[] = $response;
        }
        $this->actionLogger->info('Tworzenie ZK', $log);
    }
    
    public function logUserManuallyAddCoDPrice(Order $order, UserInterface $user, float $price) {
        $this->actionLogger->info('Ręcznie zmieniona cena wysyłki', [
            'entity_id' => $order->getId(),
            'entity_type' => get_class($order),
            'user' => $user,
            'action' => DatabaseLoggerActionTaxonomy::MANUALLY_ADDED_COD_PRICE,
            'price' => $price,
        ]);
    }
    
    public function logInvoiceError(Order $order, $message, $content, $contentType, $code, UserInterface $user) {
        $this->actionLogger->info('Błąd podczas tworzenia faktury', [
            'entity_id' => $order->getId(),
            'entity_type' => get_class($order),
            'user' => $user,
            'content' => $content,
            'message' => $message,
            'code' => $code,
            'content_type' => $contentType ?? '',
            'action' => DatabaseLoggerActionTaxonomy::SUBIEKT_ERRORS,
        ]);
    }
}