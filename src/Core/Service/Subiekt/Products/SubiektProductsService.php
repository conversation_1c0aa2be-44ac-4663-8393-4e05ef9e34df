<?php

namespace App\Core\Service\Subiekt\Products;

use App\Core\Entity\Order;
use App\Core\Service\Subiekt\SubiektApiService;
use Doctrine\ORM\EntityManagerInterface;

class SubiektProductsService
{
    public function __construct(
        private readonly SubiektApiService $subiektApiService,
        private readonly EntityManagerInterface $entityManager
    ) {}

    public function add(string $name): array {
        return [
            'code' => 201,
            'message' => 'Product created'
        ];
    }

    public function list(): array {
        return [];
    }

    public function listByChars($chars): array {

        $products = $this->subiektApiService->makeRequest('products', 'getProductListByChar', ['chars' => $chars, 'magazyn' => 4]);

        if('success' === $products['status']){
            return [
                'code' => 200,
                'message' => 'Products found',
                'data' => $products['content']
            ];
        }
        return [
            'code' => 404,
            'message' => 'Products not found'
        ];
    }

    public function get($code) {
        $product = $this->subiektApiService->makeRequest('product', 'get', ['code' => $code]);

        if('success' === $product['status']){
            return [
                'code' => 200,
                'message' => 'Products found',
                'data' => $product['content']
            ];
        }
        return [
            'code' => 404,
            'message' => 'Products not found'
        ];

    }

    public function edit($product, $data): array {
        return ['message' => 'Product updated', 'code' => 200];
    }

    public function delete($product): array{
        return ['message' => 'Product deleted', 'code' => 200];
    }

    public function assignEans($documentItemId, $eans): array {
        return $this->subiektApiService->assignEansToProduct($documentItemId, $eans);
    }

    public function addProduct($data){
        $response = $this->subiektApiService->makeRequest('product', 'add', $data);

        if($response['status'] === 'success' && $response['content']['status'] !== 'error'){
            return [
                'status' => 'success',
                'message' => 'Product created',
                'data' => $response['content']['data'],
                'code' => 201
            ];
        } elseif ('fail' === $response['status']) {
            return [
                'status' => 'error',
                'message' => $response['error'],
                'code' => 400
            ];
        } else {
            return [
                'status' => 'error',
                'message' => $response['content']['message'],
                'data' => $response['content'],
                'code' => 400
            ];
        }
    }

    public function assignEansByCode(string $code, $eans) {
        return $this->subiektApiService->assignEansToProductByCode($code, $eans);
    }

    public function findProductByEan($ean):array {
        $response = $this->subiektApiService->makeRequest('products', 'findProductByEan', ['ean' => $ean]);

        if($response['status'] === 'success' && $response['content']['state'] !== 'fail'){
            return [
                'status' => 'success',
                'message' => 'Product found',
                'data' => $response['content']
            ];
        }

        return [
            'status' => 'error',
            'message' => 'Product not found',
            'data' => $response['content']
        ];
    }


    public function getAllEans($sku): array {
        $response = $this->subiektApiService->makeRequest('product', 'getAllEans', ['code' => $sku]);

        if($response['status'] === 'success' && $response['content']['status'] === 'success'){
            return [
                'status' => 'success',
                'message' => 'Eans found',
                'data' => $response['content']
            ];
        }

        return [
            'status' => 'error',
            'message' => 'Product not found',
            'data' => $response['content']
        ];
    }

    public function getQuantityInWarehouse(array $skus, array $warehouses){
        $response = $this->subiektApiService->makeRequest('products', 'getQuantityInWarehouse', ['skus' => $skus, 'warehouses' => $warehouses]);

        return $response;
    }
}