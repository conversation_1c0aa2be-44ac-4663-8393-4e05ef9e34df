<?php

namespace App\Core\Service\Subiekt\Orders;

use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Entity\User;
use App\Core\Service\LoggerService;
use App\Core\Service\Subiekt\Mapper\SubiektPaymentMapper;
use App\Core\Service\Subiekt\Model\SubiektCustomerModel;
use App\Core\Service\Subiekt\Model\SubiektOrdersModel;
use App\Core\Service\Subiekt\Model\SubiektProductModel;
use App\Core\Service\Subiekt\SubiektApiService;
use App\Core\Taxonomy\CountryIsoCodeEnum;
use App\Core\Taxonomy\SubiektIsoCountryEnum;
use App\Core\Taxonomy\SubiektVatSymbolEnum;
use App\Core\Utility\Money;
use Doctrine\ORM\EntityManagerInterface;

class SubiektOrdersService {
    
    const RESOURCE = 'order';
    
    public function __construct(
        private readonly SubiektApiService $subiektApiService,
        private readonly EntityManagerInterface $entityManager,
        private readonly LoggerService $loggerService
    ) {}
    
    public function addOrder(Order $order) {
        $orderId = $order->getOrderId();
        $parts = explode('-', $orderId);
        if (count($parts) >= 2) {
            $desiredOrderId = $parts[0] . '-' . $parts[1];
        }
        else {
            $desiredOrderId = $orderId;
        }
        $requestData = [
            'orgNumber' => $desiredOrderId
        ];
        $response = $this->subiektApiService->makeRequest('document', 'CheckIfZkExistByOriginalNumber', $requestData);
        if ('success' === $response['status'] && isset($response['content']['doc_ref'])) {
            return $response['content']['doc_ref'];
        }
        $subiektOrder = $this->prepareOrder($order);
        if ($subiektOrder instanceof SubiektOrdersModel) {
            $user = $this->entityManager->getRepository(User::class)->findOneBy(['username' => 'system']);
            $response = $this->subiektApiService->makeRequest(self::RESOURCE, 'add', $subiektOrder->toArray());
            if (is_array($response) && $response['status'] === 'success' && $response['content']) {
                $logResponse = [
                    'status' => 'success',
                    'data' => $response['content']['order_ref'],
                ];
                $this->loggerService->logSubiektOrderAdd($order, $user, $logResponse);
                
                return $response['content']['order_ref'];
            }
            else if (is_array($response) && $response['status'] === 'warning' && $response['content']['order_ref']) {
                $logResponse = [
                    'status' => 'success',
                    'data' => $response['content']['order_ref'],
                ];
                $this->loggerService->logSubiektOrderAdd($order, $user, $logResponse);
                
                return $response['content']['order_ref'];
            }
            else if (isset($response['error']) && $response['error']) {
                $logResponse = [
                    'status' => 'error',
                    'data' => $response['error'],
                ];
                $this->loggerService->logSubiektOrderAdd($order, $user, $logResponse);
                throw new \Exception('Failed to send order to Subiekt: ' . $response['error']);
            }
            else {
                $this->loggerService->logSubiektOrderAdd($order, $user, $response);
                throw new \Exception($response);
            }
        }
        
        return false;
    }
    
    public function update(Order $order) {
        $subiektOrder = $this->prepareOrder($order);
        $orderToUpdate = $subiektOrder->toArray();
        $orderToUpdate['order_ref'] = $order->getOrderInvoice()->getInvoiceNumber();
        if ($subiektOrder instanceof SubiektOrdersModel) {
            $response = $this->subiektApiService->makeRequest(self::RESOURCE, 'update', $orderToUpdate);
            if (is_array($response) && $response['status'] === 'success' && $response['content']) {
                return $response['content']['order_ref'];
            }
            else if (is_array($response) && $response['status'] === 'warning' && $response['content']['order_ref']) {
                return $response['content']['order_ref'];
            }
            else if (isset($response['error']) && $response['error']) {
                throw new \Exception('Failed to send order to Subiekt: ' . $response['error']);
            }
            else {
                throw new \Exception($response);
            }
        }
        
        return false;
    }
    
    public function makeSaleDoc(Order $order): array {
        try {
            $readyResponse = $this->checkIfReadyForInvoice($order);
            if ($readyResponse['status'] === 'alreadyRealized') {
                $order->getOrderInvoice()->setInvoiceNumber($readyResponse['content']);
                $this->entityManager->persist($order->getOrderInvoice());
                $this->entityManager->flush();
                
                return [
                    'status' => 'success',
                    'message' => 'Order already realized, invoice number assigned',
                    'content' => $readyResponse['content'],
                    'code' => 200
                ];
            }
            if ($readyResponse['status'] !== 'success') {
                return [
                    'status' => 'error',
                    'message' => 'Invoice creation skipped: ' . $readyResponse['message'],
                    'content' => $readyResponse['content'] ?? '',
                    'code' => 400
                ];
            }
            $response = $this->subiektApiService->makeRequest(self::RESOURCE, 'makeSaleDoc', [
                'order_ref' => $order->getOrderInvoice()->getInvoiceNumber(),
            ]);
            if (isset($response['error']) && $response['error']) {
                return [
                    'status' => 'error',
                    'message' => 'Failed to create sale document: ' . $response['error'],
                    'code' => 400
                ];
            }
            if (
                is_array($response)
                && isset($response['status'])
                && $response['status'] === 'success'
                && isset($response['content']['doc_state'])
                && $response['content']['doc_state'] === 'ok')
            {
                $order->getOrderInvoice()->setInvoiceNumber($response['content']['doc_ref']);
                $this->entityManager->persist($order->getOrderInvoice());
                $this->entityManager->flush();

                return [
                    'status' => 'success',
                    'message' => 'Sale document created successfully',
                    'content' => $response['content'],
                    'code' => 201
                ];
            }
            
            return [
                'status' => 'error',
                'message' => 'Unexpected response from Subiekt',
                'content' => $response,
                'code' => 400
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Exception: ' . $e->getMessage(),
                'code' => 500
            ];
        }
    }
    
    private function prepareOrder(Order $order): ?SubiektOrdersModel {
        $subiektOrder = new SubiektOrdersModel();
        $subiektOrder->setComments($this->makeComment($order));
        $subiektOrder->setReference($order->getOrderId());
        $subiektOrder->setCreateProductIfNotExists(0);
        $price = new Money($order->getFullPrice());
        $subiektOrder->setAmount( $price->toFloat() ?? '0');
        $subiektOrder->setPayType($this->getPaymentType($order));
        $subiektOrder->setCurrency($order->getCurrency());
        $customer = $this->prepareCustomer($order);
        $subiektOrder->setCustomer($customer);
        $vat = SubiektVatSymbolEnum::getVatSymbolByCountry($order->getOrderDelivery()->getDeliveryCountry());
        $subiektOrder->setIsoCountryId(SubiektIsoCountryEnum::getIdByIsoCode(CountryIsoCodeEnum::getIsoCodeByCountry($order->getOrderDelivery()->getDeliveryCountry())));
        foreach ($order->getProducts() as $product) {
            $subiektProduct = new SubiektProductModel();
            $subiektProduct->setCode($product->getSku());
            $subiektProduct->setQty($product->getQuantity());
            $priceBrutto = new Money($product->getPriceBrutto());
            $subiektProduct->setPrice($priceBrutto->toFloat());
            $subiektProduct->setPriceBeforeDiscount($priceBrutto->toFloat());
            $subiektProduct->setName($product->getName());
            $subiektProduct->setIdStore(2);
            $subiektProduct->setVat($vat);
            $subiektOrder->addProduct($subiektProduct);
        }
        if ($order->getOrderDelivery()->getDeliveryPrice() > 0) {
            $subiektProduct = new SubiektProductModel();
            $subiektProduct->setCode('SHIPPING');
            $subiektProduct->setQty(1);
            $price = new Money($order->getOrderDelivery()->getDeliveryPrice());
            $subiektProduct->setPrice($price->toFloat());
            $subiektProduct->setPriceBeforeDiscount($priceBrutto->toFloat());
            $subiektProduct->setName($order->getOrderDelivery()->getDeliveryMethod());
            $subiektProduct->setIdStore(2);
            $subiektProduct->setVat($vat);
            //            $subiektProduct->setType(2);
            $subiektOrder->addProduct($subiektProduct);
            $subiektOrder->setAmount($subiektOrder->getAmount() + $price->toFloat());
        }
        
        return $subiektOrder;
    }
    
    public function checkSaleDoc(Order $order) {
        return $this->subiektApiService->makeRequest(self::RESOURCE, 'checkSaleDocArray', [
            'order_ref' => $order->getOrderInvoice()->getInvoiceNumber(),
            'is_company' => true
        ]);
    }
    
    public function get(Order $order) {
        return $this->subiektApiService->makeRequest(self::RESOURCE, 'get', [
            'order_ref' => $order->getOrderInvoice()->getInvoiceNumber(),
            'is_company' => true
        ]);
    }
    
    public function checkIfReadyForInvoice(Order $order): array {
        $invoiceNumber = $order->getOrderInvoice()->getInvoiceNumber();
        if (str_starts_with($invoiceNumber, 'ZK')) {
            $document = $this->checkSaleDoc($order);
            if ($document['status'] === 'success' && $document['content']['status'] === 'orderReady') {
                return [
                    'status' => 'success',
                    'message' => 'Order is ready for invoice'
                ];
            }
            if ($document['status'] === 'success' && $document['content']['status'] === 'orderAlreadyRealized') {
                return [
                    'status' => 'alreadyRealized',
                    'message' => 'Order already realized',
                    'content' => $document['content']['doc_ref']
                ];
            }
            
            return [
                'status' => 'error',
                'message' => $document['content']['message'] ?? 'Unknown error',
                'content' => $document['content']['products'] ?? ''
            ];
        }
        if (str_starts_with($invoiceNumber, 'FS')) {
            return [
                'status' => 'error',
                'message' => 'Order already has an invoice: ' . $invoiceNumber,
                'content' => $invoiceNumber
            ];
        }
        
        return [
            'status' => 'error',
            'message' => 'Order is not ready for invoice'
        ];
    }
    
    public function prepareCustomer(Order $order): SubiektCustomerModel {
        $customer = new SubiektCustomerModel();
        $customer->setFirstname($order->getOrderDelivery()->getDeliveryFirstname());
        $customer->setLastname($order->getOrderDelivery()->getDeliveryLastname());
        $customer->setEmail($order->getEmail() ?? '');
        $customer->setAddress($order->getOrderInvoice()->getInvoiceAddress() ?? $order->getOrderDelivery()->getDeliveryAddress());
        $customer->setAddressNo('');
        $customer->setCity($order->getOrderInvoice()->getInvoiceCity() ?? $order->getOrderDelivery()->getDeliveryCity());
        $customer->setPostCode($order->getOrderInvoice()->getInvoicePostcode() ?? $order->getOrderDelivery()->getDeliveryPostcode());
        $customer->setPhone($order->getPhone());
        $customer->setRefId(!empty($order->getOrderInvoice()->getInvoiceNip()) ? $order->getOrderInvoice()->getInvoiceNip() : str_replace("-", "", $order->getOrderId()));
        $customer->setIsCompany(!empty($order->getOrderInvoice()->getInvoiceNip()));
        $customer->setCompanyName($order->getOrderInvoice()->getInvoiceCompany() ?? '');
        $customer->setTaxId($order->getOrderInvoice()->getInvoiceNip() ?? '');
        $customer->setPole1('undefined');
        $customer->setCountry($order->getOrderDelivery()->getDeliveryCountry());
        
        return $customer;
    }
    
    public function prepareCustomerAsStore($orderId): SubiektCustomerModel {
        $customer = new SubiektCustomerModel();
        $customer->setFirstname("Magazyn");
        $customer->setLastname("Stołeczny");
        $customer->setEmail('');
        $customer->setAddress('Stołeczna 1');
        $customer->setAddressNo('');
        $customer->setCity("Białystok");
        $customer->setPostCode("15-000");
        $customer->setPhone("1234");
        $customer->setRefId(!empty(str_replace("-", "", $orderId)));
        $customer->setIsCompany(false);
        $customer->setCompanyName('');
        $customer->setTaxId('');
        $customer->setPole1('undefined');
        
        return $customer;
    }
    
    private function makeComment(Order $order): string {
        $comment = 'name:' . $order->getOrderDelivery()->getDeliveryFirstname() . ' ' . $order->getOrderDelivery()->getDeliveryLastname() . '|';
        $comment .= 'idOrder:' . $order->getOrderId() . '|';
        $comment .= 'payment:' . SubiektPaymentMapper::getValueForName($order->getPaymentMethod()) . '|';
        $comment .= 'delivery:' . $order->getOrderDelivery()->getDeliveryMethod() . '|';
        $source = $this->entityManager->getRepository(Integration::class)->findOneBy(['id' => $order->getOrderSourceId()]);
        if ($source) {
            $name = null;
            if ('prestashop' === $source->getType()) {
                $name = 'shop';
            }
            else if ('baselinker' === $source->getType()) {
                $name = $source->getSettingsByKey('TYPE');
            }
            if ($name) {
                $comment .= 'source:' . $name . '|';
            }
        }
        
        return $comment;
    }
    
    private function getPaymentType(Order $order) {
        $paymentType = 'transfer';
        $paymenrsArray = [
            'PayU',
            'Zen',
            'Card MASTERCARD (via Revolut)',
            'Card VISA (via Revolut)',
            'Card via Stripe'
        ];
        if (in_array($order->getPaymentMethod(), $paymenrsArray)) {
            $paymentType = 'cart';
        }
        
        return $paymentType;
    }
    
    private function getSubiektVatSymbol($country) {
        $vatMapping = [
            "Hungary" => 1000012,
            "Austria" => 1000001,
            "Belgium" => 1000002,
            "Italy" => 1000014,
            "Luxemburg" => 1000016,
            "France" => 1000009,
            "Latvia" => 1000017,
            "Lithuania" => 1000015,
            "Slovakia" => 1000023,
            "Slovenia" => 1000022,
            "Sweden" => 1000021,
            "Portugal" => 1000019,
            "Romania" => 1000020,
            "Bulgaria" => 1000003,
            "Ireland" => 1000013,
            "Spain" => 1000007,
            "Finland" => 1000008,
            "Estonia" => 1000006,
            "Denmark" => 1000005,
            "Germany" => 1000000,
            "Greece" => 1000010,
            "Croatia" => 1000011,
            "Netherlands" => 1000018,
            "Poland" => 100001,
        ];
        
        return $vatMapping[$country] ?? null;
    }
}