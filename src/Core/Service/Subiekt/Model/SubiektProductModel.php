<?php

namespace App\Core\Service\Subiekt\Model;

class SubiektProductModel {
    
    private string $code;
    
    private int $qty;
    
    private float $price;
    
    private float $priceBeforeDiscount;
    
    private string $name;
    
    private int $idStore;
    
    private int $type = 1;
    
    private int $vat;
    
    public function getCode(): string {
        return $this->code;
    }
    
    public function setCode(string $code): void {
        $this->code = $code;
    }
    
    public function getQty(): int {
        return $this->qty;
    }
    
    public function setQty(int $qty): void {
        $this->qty = $qty;
    }
    
    public function getPrice(): float {
        return $this->price;
    }
    
    public function setPrice(float $price): void {
        $this->price = $price;
    }
    
    public function getPriceBeforeDiscount(): float {
        return $this->priceBeforeDiscount;
    }
    
    public function setPriceBeforeDiscount(float $priceBeforeDiscount): void {
        $this->priceBeforeDiscount = $priceBeforeDiscount;
    }
    
    public function getName(): string {
        return $this->name;
    }
    
    public function setName(string $name): void {
        $this->name = $name;
    }
    
    public function getIdStore(): int {
        return $this->idStore;
    }
    
    public function setIdStore(int $idStore): void {
        $this->idStore = $idStore;
    }
    
    public function getType(): int {
        return $this->type;
    }
    
    public function setType(int $type): void {
        $this->type = $type;
    }
    
    public function getVat(): int {
        return $this->vat;
    }
    
    public function setVat(int $vat): void {
        $this->vat = $vat;
    }
    
    public function toArray(): array {
        return [
            'code' => $this->code,
            'qty' => $this->qty,
            'price' => $this->price,
            'price_before_discount' => $this->priceBeforeDiscount,
            'name' => $this->name,
            'id_store' => $this->idStore,
            'rodzaj' => $this->type,
            'vat' => $this->vat,
        ];
    }
}