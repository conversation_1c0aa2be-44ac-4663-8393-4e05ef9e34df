<?php

namespace App\Core\Service\Subiekt\Model;

class SubiektOrdersModel {
    
    private string $comments;
    
    private string $reference;
    
    private int $createProductIfNotExists = 0;
    
    private float $amount;
    
    private string $payType = 'transfer';
    
    private SubiektCustomerModel $customer;
    
    private array $products = [];
    
    private string $warehouse;
    
    private string $currency = 'EUR';
    private int $isoCountryId;
    
    public function getComments(): string {
        return $this->comments;
    }
    
    public function setComments(string $comments): void {
        $this->comments = $comments;
    }
    
    public function getReference(): string {
        return $this->reference;
    }
    
    public function setReference(string $reference): void {
        $this->reference = $reference;
    }
    
    public function getCreateProductIfNotExists(): int {
        return $this->createProductIfNotExists;
    }
    
    public function setCreateProductIfNotExists(int $createProductIfNotExists): void {
        $this->createProductIfNotExists = $createProductIfNotExists;
    }
    
    public function getAmount(): float {
        return $this->amount;
    }
    
    public function setAmount(float $amount): void {
        $this->amount = $amount;
    }
    
    public function getPayType(): string {
        return $this->payType;
    }
    
    public function setPayType(string $payType): void {
        $this->payType = $payType;
    }
    
    public function getCustomer(): SubiektCustomerModel {
        return $this->customer;
    }
    
    public function setCustomer(SubiektCustomerModel $customer): void {
        $this->customer = $customer;
    }
    
    public function getProducts(): array {
        return $this->products;
    }
    
    public function setProducts(array $products): self {
        $this->products = $products;
        
        return $this;
    }
    
    public function addProduct(SubiektProductModel $product): self {
        $this->products[] = $product;
        
        return $this;
    }
    
    public function getWarehouse(): string {
        return $this->warehouse;
    }
    
    public function setWarehouse(string $warehouse): void {
        $this->warehouse = $warehouse;
    }
    
    public function getCurrency(): string {
        return $this->currency;
    }
    
    public function setCurrency(string $currency): void {
        $this->currency = $currency;
    }
    
    public function getIsoCountryId(): int {
        return $this->isoCountryId;
    }
    
    public function setIsoCountryId(int $isoCountryId): void {
        $this->isoCountryId = $isoCountryId;
    }
    
    public function toArray(): array {
        $productsArray = array_map(function ($product) {
            return $product->toArray();
        }, $this->products);
        $returnArray = [
            'comments' => $this->comments,
            'reference' => $this->reference,
            'create_product_if_not_exists' => $this->createProductIfNotExists,
            'amount' => $this->amount,
            'pay_type' => $this->payType,
            'customer' => $this->customer->toArray(),
            'products' => $productsArray,
            'currency' => $this->currency,
            'isoCountryId' => $this->isoCountryId,
        ];
        if (!empty($this->warehouse)) {
            $returnArray['mag_id'] = $this->warehouse;
        }
        
        return $returnArray;
    }
}