<?php

namespace App\Core\Service\Subiekt\Model;

class SubiektCustomerModel {
    
    private string $firstname;
    
    private string $lastname;
    
    private string $email;
    
    private string $address;
    
    private string $addressNo;
    
    private string $city;
    
    private string $postCode;
    
    private string $phone;
    
    private string $refId;
    
    private bool $isCompany;
    
    private string $companyName;
    
    private string $taxId;
    
    private string $pole1;
    
    private string $country;
    
    public function getFirstname(): string {
        return $this->firstname;
    }
    
    public function setFirstname(string $firstname): void {
        $this->firstname = $firstname;
    }
    
    public function getLastname(): string {
        return $this->lastname;
    }
    
    public function setLastname(string $lastname): void {
        $this->lastname = $lastname;
    }
    
    public function getEmail(): string {
        return $this->email;
    }
    
    public function setEmail(string $email): void {
        $this->email = $email;
    }
    
    public function getAddress(): string {
        return $this->address;
    }
    
    public function setAddress(string $address): void {
        $this->address = $address;
    }
    
    public function getAddressNo(): string {
        return $this->addressNo;
    }
    
    public function setAddressNo(string $addressNo): void {
        $this->addressNo = $addressNo;
    }
    
    public function getCity(): string {
        return $this->city;
    }
    
    public function setCity(string $city): void {
        $this->city = $city;
    }
    
    public function getPostCode(): string {
        return $this->postCode;
    }
    
    public function setPostCode(string $postCode): void {
        $this->postCode = $postCode;
    }
    
    public function getPhone(): string {
        return $this->phone;
    }
    
    public function setPhone(string $phone): void {
        $this->phone = $phone;
    }
    
    public function getRefId(): string {
        return $this->refId;
    }
    
    public function setRefId(string $refId): void {
        $this->refId = $refId;
    }
    
    public function isCompany(): bool {
        return $this->isCompany;
    }
    
    public function setIsCompany(bool $isCompany): void {
        $this->isCompany = $isCompany;
    }
    
    public function getCompanyName(): string {
        return $this->companyName;
    }
    
    public function setCompanyName(string $companyName): void {
        $this->companyName = $companyName;
    }
    
    public function getTaxId(): string {
        return $this->taxId;
    }
    
    public function setTaxId(string $taxId): void {
        $this->taxId = $taxId;
    }
    
    public function getPole1(): string {
        return $this->pole1;
    }
    
    public function setPole1(string $pole1): void {
        $this->pole1 = $pole1;
    }
    
    public function getCountry(): string {
        return $this->country;
    }
    
    public function setCountry(string $country): void {
        $this->country = $country;
    }
    
    public function toArray(): array {
        return [
            'firstname' => $this->firstname,
            'lastname' => $this->lastname,
            'email' => $this->email,
            'address' => $this->address,
            'address_no' => $this->addressNo,
            'city' => $this->city,
            'post_code' => $this->postCode,
            'phone' => $this->phone,
            'ref_id' => $this->refId,
            'is_company' => $this->isCompany,
            'company_name' => $this->companyName,
            'tax_id' => $this->taxId,
            'Pole1' => $this->pole1,
            'country' => $this->country,
        ];
    }
}