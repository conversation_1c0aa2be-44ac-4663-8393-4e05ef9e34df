<?php

namespace App\Core\Service\Subiekt\Documents;

use App\Core\Entity\Order;
use App\Core\Kernel;
use App\Core\Service\PdfCreatorService\PdfCreatorService;
use App\Core\Service\Subiekt\Mapper\SubiektPaymentMapper;
use App\Core\Service\Subiekt\SubiektApiService;
use Doctrine\ORM\EntityManagerInterface;

class SubiektDocumentsService
{
    public CONST RESOURCE = 'document';
    public CONST PDF_PATH = '/public/pdf';
    public CONST PDF_INVOICE_PATH = '/invoice';
    public CONST PDF_EXTENSION = '.pdf';
    public function __construct(
        private readonly SubiektApiService $subiektApiService,
        private readonly PdfCreatorService $PdfCreatorService,
        private readonly Kernel $kernel,
        private readonly EntityManagerInterface $entityManager
    ){}

    public function getPdf($order){
        $response = $this->createPdf($order);
        if($response['status'] == 'success' && $response['filePath']){
            return $response['filePath'];
        } else {
            return ['status' => 'error', 'message' => 'Couldnt get PDF file'];
        }
    }

    public function getState(Order $order){
        return $this->subiektApiService->makeRequest(self::RESOURCE, 'getstate', ['doc_ref' => $order->getOrderInvoice()->getInvoiceNumber()]);
    }

    public function get(Order $order){
        return $this->subiektApiService->makeRequest(self::RESOURCE, 'get', ['doc_ref' => $order->getOrderInvoice()->getInvoiceNumber()]);
    }

    public function updateZkReservation(Order $order, bool $productReservation, $warehouse = false){
        $data = ['doc_ref' => $order->getOrderInvoice()->getInvoiceNumber(),'productReservation' => $productReservation];
        if($warehouse && is_int($warehouse)){
            $data['mag_id'] = $warehouse;
            $data['doc_ref'] = $order->getOrderInvoice()->getAdditionalNumber();
        }
        return $this->subiektApiService->makeRequest(self::RESOURCE, 'updateState', $data);
    }
    public function updateZkReservationByZkNumber(string $zk, bool $productReservation){
        return $this->subiektApiService->makeRequest(self::RESOURCE, 'updateState', ['doc_ref' => $zk,'productReservation' => $productReservation]);
    }

    public function updateComment(Order $order) {
        return $this->subiektApiService->makeRequest(self::RESOURCE, 'addComment', ['doc_ref' => $order->getOrderInvoice()->getInvoiceNumber(),'comment' => SubiektPaymentMapper::getValueForName($order->getPaymentMethod())]);
    }

    public function getInvoice(Order $order){
        return $this->subiektApiService->makeRequest(self::RESOURCE, 'getInvoice', ['doc_ref' => $order->getOrderInvoice()->getInvoiceNumber()]);
    }

    public function createPdf(Order $order): ?array
    {
        $orderNumber = $order->getOrderInvoice()->getInvoiceNumber();
        $fileName = str_replace(['/', '\\',' '], '-', $orderNumber . self::PDF_EXTENSION);
        $filePath = $this->kernel->getProjectDir() .  self::PDF_PATH  . self::PDF_INVOICE_PATH;
        $file = $filePath . DIRECTORY_SEPARATOR . $fileName;

        if (file_exists($file)) {
            return ['status' => 'success', 'message' => 'PDF already created', 'file' => $fileName, 'filePath' => $file];
        }
        $response = $this->getInvoice($order);

        if ('success' !== $response['status'] && !$response['content']['is_exists']) {
            return ['status' => 'error', 'message' => 'Could not get document'];
        }

        $vatDetails = [];
        $totalVatAmount = 0;
        $maxCharsPerLine = 29;

        $highestLineCount = 1;


        foreach ($response['content']['products'] as $product) {
            $productLines = $this->estimateWrappedLines($product['name'], $maxCharsPerLine);
            $vatRate = $product['vat'];
            $vatAmount = $product['totalVat'];
            $netAmount = $product['totalNetto'];
            $grossAmount = $product['totalBrutto'];

            if (!isset($vatDetails[$vatRate])) {
                $vatDetails[$vatRate] = [
                    'vatAmount' => 0,
                    'netAmount' => 0,
                    'grossAmount' => 0,
                ];
            }

            $vatDetails[$vatRate]['vatAmount'] += $vatAmount;
            $vatDetails[$vatRate]['netAmount'] += $netAmount;
            $vatDetails[$vatRate]['grossAmount'] += $grossAmount;

            $totalVatAmount += $vatAmount;

            if ($productLines > $highestLineCount) {
                $highestLineCount = $productLines;
            }
        }

        $roundedVatDetails = [];

        foreach ($vatDetails as $vatRate => $amounts) {
            $roundedVatRate = round($vatRate, 0);
            $roundedVatDetails[$roundedVatRate] = [
                'vatAmount' => number_format((float)$amounts['vatAmount'], 2,','),
                'netAmount' => number_format((float)$amounts['netAmount'], 2),
                'grossAmount' => number_format((float)$amounts['grossAmount'], 2,','),
            ];
        }

        $vatDetails = $roundedVatDetails;


        [$firstPageMaxRows, $subsequentPagesMaxRows] = match ($highestLineCount) {
            2 => [18, 33],
            3 => [13, 21],
            4 => [10, 18],
            5 => [8, 14],
            default => [29, 51],
        };


        $currentPageLines = 0;
        foreach ($response['content']['products'] as $index => $product) {
            $productLines = $this->estimateWrappedLines($product['name'], $maxCharsPerLine);

            if ($currentPageLines + $productLines > ($index < $firstPageMaxRows ? $firstPageMaxRows : $subsequentPagesMaxRows)) {
                $currentPageLines = 0;
            }

            $currentPageLines += $productLines;
        }

        $eans = [];
        foreach ($order->getProducts() as $p) {
            $eans[$p->getSku()] = $p->getEan();
        }



        $vars = [
            'docRef' => $order->getOrderInvoice()->getInvoiceNumber(),
            'docDate' => $response['content']['docDate'],
            'orderDate' => \DateTimeImmutable::createFromFormat('U', $order->getDateAdd())->format('Y-m-d'),
            'orderId' => $order->getOrderId(),
            'customer' => [
                'name' => $response['content']['customer']['fullname'],
                'address' => $response['content']['customer']['address'],
                'city' => $response['content']['customer']['city'],
                'postCode' => $response['content']['customer']['post_code'],
                'taxId' => $response['content']['customer']['tax_id'] ?? '',
            ],
            'products' => array_map(function ($product) use ($eans) {
                return [
                    'name' => $product['name'],
                    'code' => $product['code'],
                    'ean' => $eans[$product['code']] ?? null,
                    'quantity' => number_format((float)$product['quantity'],3,','),
                    'unit' => $product['unit'],
                    'priceNetto' => number_format((float)$product['priceNetto'], 2,','),
                    'priceBrutto' => number_format((float)$product['priceBrutto'], 2,','),
                    'vat' => round($product['vat'],0),
                    'totalNetto' => number_format((float)$product['totalNetto'], 2,','),
                    'totalBrutto' => number_format((float)$product['totalBrutto'], 2,','),
                    'totalVat' => number_format((float)$product['totalVat'], 2,','),
                ];
            }, $response['content']['products']),
            'amount' => [
                'brutto' => number_format((float)$response['content']['totalAmountBrutto'], 2),
                'netto' => number_format((float)$response['content']['totalAmountNetto'], 2),
                'totalVat' => number_format((float)$totalVatAmount,2),
                'vatBreakdown' => $vatDetails,
                'inWords' => $this->amountInWords(round($response['content']['totalAmountBrutto'],2)),
            ],
            'pagination' => [
                'firstPageMaxRows' => $firstPageMaxRows,
                'subsequentPagesMaxRows' => $subsequentPagesMaxRows,
                'highestLineCount' => $highestLineCount,
            ],
        ];

        return $this->PdfCreatorService->createPdf('pdf/invoice_pdf/invoice.html.twig', $filePath, $fileName, $vars);
    }

    private function estimateWrappedLines($text, $maxCharsPerLine) {
        $lines = 0;
        $words = explode(' ', $text);
        $currentLineLength = 0;

        foreach ($words as $word) {
            $wordLength = strlen($word);

            if ($currentLineLength + $wordLength + 1 > $maxCharsPerLine) {
                $lines++;
                $currentLineLength = $wordLength + 1;
            } else {
                if ($currentLineLength > 0) {
                    $currentLineLength += 1;
                }
                $currentLineLength += $wordLength;
            }
        }

        if ($currentLineLength > 0) {
            $lines++;
        }

        return $lines;
    }

    function numberInWords($number) {
        $unity = [
            '', 'jeden', 'dwa', 'trzy', 'cztery', 'pięć', 'sześć', 'siedem', 'osiem', 'dziewięć'
        ];
        $nastki = [
            'dziesięć', 'jedenaście', 'dwanaście', 'trzynaście', 'czternaście', 'piętnaście', 'szesnaście', 'siedemnaście', 'osiemnaście', 'dziewiętnaście'
        ];
        $dozens = [
            '', '', 'dwadzieścia', 'trzydzieści', 'czterdzieści', 'pięćdziesiąt', 'sześćdziesiąt', 'siedemdziesiąt', 'osiemdziesiąt', 'dziewięćdziesiąt'
        ];
        $hundreds = [
            '', 'sto', 'dwieście', 'trzysta', 'czterysta', 'pięćset', 'sześćset', 'siedemset', 'osiemset', 'dziewięćset'
        ];

        if ($number < 10) {
            return $unity[$number];
        } elseif ($number < 20) {
            return $nastki[$number - 10];
        } elseif ($number < 100) {
            return $dozens[intval($number / 10)] . ' ' . $unity[$number % 10];
        } elseif ($number < 1000) {
            return $hundreds[intval($number / 100)] . ' ' . $this->numberInWords($number % 100);
        }

        if ($number < 1000000) {
            $thousands = intval($number / 1000);
            $remnant = $number % 1000;
            return $this->numberInWords($thousands) . ' tysiąc' . ($thousands > 1 ? 'e' : '') . ' ' . $this->numberInWords($remnant);
        } elseif ($number < 1000000000) {
            $milions = intval($number / 1000000);
            $remnant = $number % 1000000;
            return $this->numberInWords($milions) . ' milion' . ($milions > 1 ? 'y' : '') . ' ' . $this->numberInWords($remnant);
        } else {
            $bilions = intval($number / 1000000000);
            $remnant = $number % 1000000000;
            return $this->numberInWords($bilions) . ' miliard' . ($bilions > 1 ? 'y' : '') . ' ' .$this->numberInWords($remnant);
        }
    }

    function amountInWords($amount) {
        list($primaryAmount, $secondaryAmount) = explode('.', number_format($amount, 2, '.', ''));
        $inWordsPrimaryAmount = $this->numberInWords(intval($primaryAmount));
        $inWordsSecondaryAmount = $this->numberInWords(intval($secondaryAmount));

        return trim("$inWordsPrimaryAmount złotych i $inWordsSecondaryAmount groszy");
    }

    public function createMMByZk(Order $order, $magFromId = 8, $magToId = 4) {
        $zk = $this->subiektApiService->makeRequest(self::RESOURCE, 'get', ['doc_ref' => $order->getOrderInvoice()->getAdditionalNumber(), 'mag_id' => $magFromId]);
        if ('success' === $zk['status'] && isset($zk['content'])) {
            $products = [];
            foreach ($zk['content']['products'] as $product) {
                if ('0000000420' === $product['code']) {
                    continue;
                }
                $products[] = [
                    'code' => $product['code'],
                    'qty' => (int)round((float)$product['qty'])
                ];
            }

            $this->updateZkReservation($order, false, $magFromId);

            $response = $this->subiektApiService->makeRequest(self::RESOURCE, 'createMM', [
                'mag_to_id' => $magToId,
                'mag_from_id' => $magFromId,
                'products' => $products
            ]);

            if ('success' === $response['status'] && 'success' === $response['content']['status']) {
                if(isset($response['content']['doc_ref'])) {

                    $orderInvoice = $order->getOrderInvoice();
                    $orderInvoice->setMmNumber($response['content']['doc_ref']);
                    $this->entityManager->persist($orderInvoice);
                    $this->entityManager->flush();
                }
                return $response;
            } else if ('success' === $response['status'] && 'error' === $response['content']['status']) {
                return $response['content'];

            }
        }
        return false;


    }






}