<?php

namespace App\Core\Service\Subiekt\Mapper;

class SubiektPaymentMapper {
    const PRZELEWY24 = [
        'Przelewy24.pl',
        ' Przelewy24.pl',
        'przelewy24',
    ];
    const CASH_ON_DELIVERY = [
        '<PERSON><PERSON><PERSON><PERSON><PERSON>ć przy odbiorze',
    ];
    const ELECTRONIC_PAYMENTS = [
        'Płatności elektroniczne',
        'S<PERSON>bka platność - przelew',
        '<PERSON><PERSON><PERSON><PERSON> platność - karta kredytowa',
        '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bezpośrednia na konto sklepu',
    ];
    const PAYU_PAYMENTS = [
        'PayU',
        'Tpay',
    ];
    const FREE_ORDER = [
        'Darmowe zamówienie',
    ];

    const VALUE_PRZELEWY24 = 'przelewy24';
    const VALUE_CASH_ON_DELIVERY = 'P<PERSON>tność przy odbiorze';
    const VALUE_ELECTRONIC_PAYMENTS = '<PERSON><PERSON><PERSON><PERSON> platność - przelew';
    const VALUE_PAYU_PAYMENTS = 'electronic_payments';
    const VALUE_FREE_ORDER = 'free_order';
    const VALUE_DEFAULT = 'karta';

    private static $nameToValueMap = [];

    private static function initialize() {
        if (empty(self::$nameToValueMap)) {
            $groups = [
                self::VALUE_PRZELEWY24 => self::PRZELEWY24,
                self::VALUE_CASH_ON_DELIVERY => self::CASH_ON_DELIVERY,
                self::VALUE_ELECTRONIC_PAYMENTS => self::ELECTRONIC_PAYMENTS,
                self::VALUE_FREE_ORDER => self::FREE_ORDER,
                self::VALUE_PAYU_PAYMENTS => self::PAYU_PAYMENTS,
            ];

            foreach ($groups as $value => $names) {
                foreach ($names as $name) {
                    self::$nameToValueMap[$name] = $value;
                }
            }
        }
    }

    public static function getValueForName(string $name): ?string {
        self::initialize();
        return self::$nameToValueMap[$name] ?? self::VALUE_DEFAULT;
    }
}
