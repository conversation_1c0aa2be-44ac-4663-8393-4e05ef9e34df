<?php

namespace App\Core\Service\Subiekt;

use App\Core\Entity\Document;
use App\Core\Entity\Order;
use App\Core\Service\Settings;
use App\Core\Service\Subiekt\Auth\SubiektAuthService;
use App\Core\Service\Subiekt\Model\SubiektOrdersModel;
use App\Core\Taxonomy\SubiektDocumentEnumTaxonomy;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class SubiektApiService
{
    private ?string $subiektUrl;

    public function __construct(
        private readonly HttpClientInterface $httpClient,
        private readonly SubiektAuthService $subiektAuthService,
        private readonly Settings $settings,
    )
    {
        if('dev' === $_ENV['APP_ENV']) {
            $this->subiektUrl = $this->settings->get('SUBIEKT_BASE_LOCAL_URL');
        } else {
            $this->subiektUrl = $this->settings->get('SUBIEKT_BASE_REMOTE_URL');
        }
    }

    public function makeRequest(string $category, string $action, array $data)
    {
        $requestBody = [
            'json' => [
                'api_key' => $this->subiektAuthService->getApiKey(),
                'id_person' => "Darek Jaworski",
                'data' => $data,
            ],
            'headers' => [
                'Content-Type' => 'application/json'
            ],
        ];

        try {
            $response = $this->httpClient->request('POST', $this->prepareUrl($category,$action), $requestBody);
            $statusCode = $response->getStatusCode();
            $content = $response->getContent(false);

            if ($statusCode !== Response::HTTP_OK) {
                return ['error' => 'Something went wrong', 'status' => $statusCode, 'content' => $content];
            }

            if ($this->isJson($content)) {
                $decoded = json_decode($content, true);
                if($decoded['state'] === 'fail')
                    return ['status' => $decoded['state'], 'error' => $decoded['message']];
                else if($decoded['state'] === 'warning'){
                    return ['status' => $decoded['state'], 'content' => $decoded['data'] ?? '','error' => $decoded['error'] ?? '', 'message' => $decoded['message'] ?? ''];
                } else if($decoded['state'] === 'success'){
                    return ['status' => $decoded['state'],'content' => $decoded['data'] ?? '','message' => $decoded['message'] ?? ''];
                } else {
                    return $decoded['data'];
                }
            } else {
                return $content;
            }
        } catch (TransportExceptionInterface|ClientExceptionInterface|RedirectionExceptionInterface|ServerExceptionInterface $e) {
            return ['error' => $e->getMessage(), 'status' => $e->getCode()];
        }
    }

    public function sendOrder(SubiektOrdersModel $order) {
        $data = $order->toArray();
        $data['customer']['Pole1'] = 'test';
        $response = $this->makeRequest('order', 'add', $data);
        if (isset($response['error']) && $response['error']) {
            throw new \Exception('Failed to send order to Subiekt: ' . $response['error']);
        }
        return $response;
    }

    public function makeSaleDoc(Order $order) {
        $data = $order->getOrderInvoice()->getInvoiceNumber();
        $response = $this->makeRequest('order', 'makeSaleDoc', ['order_ref' => $data]);
        if (isset($response['error']) && $response['error']) {
            throw new \Exception('Failed to make sale dock to Order: ' . $response['error']);
        }

        return $response;
    }

    private function isJson($string): bool
    {
        json_decode($string);
        return (json_last_error() === JSON_ERROR_NONE);
    }

    private function prepareUrl(string $category, string $action): string
    {
        return sprintf('%s/%s/%s', $this->subiektUrl, $category, $action);
    }

    public function assignEansToProduct($documentItemId,$eans): array {

        $response = $this->makeRequest('product', 'updateAdditionalEans', [
            'code' => $documentItemId->getReference(),
            'additionalEans' => $eans
        ]);

        if($response['status'] === 'success'){
            return ['status' => 'success', 'message' => 'EAN assigned', 'data' => $response['content']];
        }
        return [];
    }

    public function generateDocument(Document $document) {
        return false;
        $products = $document->getDocumentItems();
        $doc = $this->makeRequest('document', 'add', ['doc_type' => SubiektDocumentEnumTaxonomy::GTA_SUBIEKT_DOKUMENT_FZ,'customerNip' => '5482515454']);
        if($doc['status'] === 'success' && $doc['content']['status'] !== 'error'){
            return [
                'status' => 'success',
                'message' => 'Document generated',
                'data' => $doc['content']
            ];
        }
        return [
            'status' => 'error',
            'message' => 'Error generating document',
            'data' => $doc['content']
        ];
    }

    public function addProduct(){
        $product = $this->makeRequest('product', 'addTowar', [
            'name' => 'testDodanegoProduktu',
            'ean' => '1234567890123',
            'is_exist' => true,
            'price' => 10.00,
            'tax' => 23,
            'unit' => 'szt',
            'category' => 'test',
        ]);

        return [
            'status' => 'success',
            'message' => 'Product created',
            'data' => $product['content']
        ];
    }

    public function assignEansToProductByCode(string $code, $eans): array {

        $response = $this->makeRequest('product', 'updateAdditionalEans', [
            'code' => $code,
            'additionalEans' => $eans
        ]);

        if($response['status'] === 'success'){
            return ['status' => 'success', 'message' => 'EAN assigned', 'data' => $response['content']];
        }
        return [];
    }

    public function getEans(string $code): array {
        $response = $this->makeRequest('product', 'getAllEans', ['code' => $code]);
        if($response['status'] === 'success'){
            return ['status' => 'success', 'message' => 'EAN assigned', 'data' => $response['content']];
        }
        return [];
    }
}