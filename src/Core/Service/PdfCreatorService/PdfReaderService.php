<?php

namespace App\Core\Service\PdfCreatorService;

use App\Core\Service\Subiekt\Documents\SubiektDocumentsService;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

readonly class PdfReaderService
{
    public function __construct(
        #[Autowire('%kernel.project_dir%')]
        private string $projectDir,
    ){}

    public function getPdf($order)
    {

        $pdf = $order->getOrderInvoice()->getInvoiceNumber();
        if(null === $pdf OR !str_starts_with($pdf, 'FS')){
            throw new \Exception('No Invoice found');
        }

        $pdfPath = str_replace(['/', '\\',' '], '-', $pdf);
        $pdfPath = $filePath = $this->projectDir . SubiektDocumentsService::PDF_PATH. SubiektDocumentsService::PDF_INVOICE_PATH . DIRECTORY_SEPARATOR . $pdfPath  . SubiektDocumentsService::PDF_EXTENSION;

        if(!file_exists($pdfPath)){
            throw new \Exception('No Invoice found');
        };
        return $pdfPath;
    }
}