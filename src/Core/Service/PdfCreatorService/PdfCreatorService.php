<?php

namespace App\Core\Service\PdfCreatorService;

use Knp\Snappy\Pdf;
use Twig\Environment;

readonly class PdfCreatorService {
    public function __construct(
        private Pdf $pdfCreatorService,
        private Environment $twig
    ){}


    public function createPdf($template, $filePath, $fileName, $vars = []): array
    {

        $file = $filePath . DIRECTORY_SEPARATOR . $fileName;
//        $vars['products'] = array_reduce($vars['products'], function($carry, $item) {
//            for ($i = 0; $i < 9; $i++) {
//                $carry[] = $item;
//            }
//            return $carry;
//        }, []);

        try {
            $this->pdfCreatorService->generateFromHtml(
                $this->twig->render($template, $vars),
                $file
            );
        } catch (\Exception $e) {
            if (file_exists($file)) {
                return ['status' => 'success', 'message' => 'PDF already exist', 'file' => $fileName, 'filePath' => $file];
            } else {
                return ['status' => 'error', 'message' => $e->getMessage()];
            }
        }

        if (file_exists($file)) {
            return ['status' => 'success', 'message' => 'PDF created', 'file' => $fileName, 'filePath' => $file];
        } else {
            return ['status' => 'error', 'message' => 'Could not create PDF'];
        }

    }
}