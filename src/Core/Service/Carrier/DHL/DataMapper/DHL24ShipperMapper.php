<?php

namespace App\Core\Service\Carrier\DHL\DataMapper;

use App\Core\Entity\Order;
use App\Core\Service\Address\AddressSplitService;
use App\Core\Utility\Money;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class DHL24ShipperMapper {

    public function __construct(
        private readonly ValidatorInterface  $validator,
         private readonly AddressSplitService $addressSplitService,
    ) {

    }

    public function fromOrder(Order $order, array $additionalSettings): array {
        $shipperMapper = new DHL24ShipmentModel();
        $address = $this->addressSplitService->split($order->getOrderDelivery()->getDeliveryAddress());
        $shipperMapper->setReceiver([
                'preaviso' => [
                    'personName' => $order->getOrderDelivery()->getDeliveryFullname(),
                    'phoneNumber' => $order->getPhone(),
                    'emailAddress' => $order->getEmail()
                ],
                'contact' => [
                    'personName' => $order->getOrderDelivery()->getDeliveryFullname(),
                    'phoneNumber' => $order->getPhone(),
                    'emailAddress' => $order->getEmail()
                ],
                'address' => [
                    'addressType' => 'B',
                    'country' => $order->getOrderDelivery()->getDeliveryCountryCode(),
                    'name' => $order->getOrderDelivery()->getDeliveryFullName(),
                    'postalCode' => str_replace('-', '', $order->getOrderDelivery()->getDeliveryPostcode()),
                    'city' => $order->getOrderDelivery()->getDeliveryCity(),
                    'street' => $address['street'],
                    'houseNumber' => $address['houseNumber'] . ' ' . $address['apartmentNumber'],
                ]
        ]);
        $shipperMapper->setContent('Suplementy');
        $shipperMapper->setPackage([
            'type' => 'PACKAGE',
            'weight' => 1,
            'width' => 10,
            'height' => 10,
            'length' => 10,
            'quantity' => 1,
        ]);

        if($order->getPaymentMethodCod()){
            $shipperMapper->setCOD($additionalSettings['manual_cod_price'] ?? (new Money($order->getTotalOrderValue()))->add(new Money($order->getOrderDelivery()->getDeliveryPrice()))->toFloat());
        }

        $errors = $this->validator->validate($shipperMapper);
        if (count($errors) > 0) {
            $errorMessages = [];
            foreach ($errors as $error) {
                $fieldName = $error->getPropertyPath();
                $errorMessages[] = $fieldName . ': ' . $error->getMessage();
            }
            throw new \Exception("Validation failed: " . implode(", ", $errorMessages));
        }

        return $shipperMapper->getDefaultData();

    }
}