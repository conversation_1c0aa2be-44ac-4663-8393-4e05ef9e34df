<?php

namespace App\Core\Service\Carrier\DHL\DataMapper;

use Symfony\Component\Validator\Constraints as Assert;

class DHL24ShipmentModel {

    #[Assert\Collection(
        fields: [
            "preaviso" => [
                new Assert\NotBlank(),
                new Assert\Collection([
                    "personName" => new Assert\NotBlank([], message: "testtino"),
                    "phoneNumber" => [new Assert\NotBlank(), new Assert\Length(min: 9, max: 12)],
                    "emailAddress" => [new Assert\NotBlank(), new Assert\Email()]
                ])
            ],
            "contact" => [
                new Assert\NotBlank(),
                new Assert\Collection([
                    "personName" => new Assert\NotBlank(),
                    "phoneNumber" => [new Assert\NotBlank(), new Assert\Length(min: 9, max: 12)],
                    "emailAddress" => [new Assert\NotBlank(), new Assert\Email()]
                ])
            ],
            "address" => [
                new Assert\NotBlank(),
                new Assert\Collection([
                    "addressType" => new Assert\NotBlank(),
                    "country" => new Assert\NotBlank(),
                    "name" => new Assert\NotBlank(),
                    "postalCode" => new Assert\NotBlank(),
                    "city" => new Assert\NotBlank(),
                    "street" => new Assert\NotBlank(),
                    "houseNumber" => new Assert\Optional(),
                    "apartmentNumber" => new Assert\Optional()
                ])
            ]
        ],
        allowExtraFields: false,
        allowMissingFields: false,
        extraFieldsMessage: "This field is not allowed."
    )]
    private array $receiver;

    #[Assert\NotBlank(message: "Content must not be blank.")]
    private string $content;

    #[Assert\Collection(
        fields: [
            "type" => [new Assert\NotBlank(), new Assert\Choice(choices: ["PACKAGE", "ENVELOPE"], message: "Invalid package type.")],
            "weight" => [new Assert\NotBlank(), new Assert\Type(type: "numeric")],
            "width" => [new Assert\NotBlank(), new Assert\Type(type: "numeric")],
            "height" => [new Assert\NotBlank(), new Assert\Type(type: "numeric")],
            "length" => [new Assert\NotBlank(), new Assert\Type(type: "numeric")],
            "quantity" => [new Assert\NotBlank(), new Assert\Type(type: "integer")]
        ],
        allowExtraFields: false,
        allowMissingFields: false
    )]
    private array $package;

    private array $defaultData = [];

    private array $defaultShipper = [];


    public function __construct() {
        $this->defaultShipper = [
            'preaviso' => [
            'personName' => 'Swiat Supli',
            'phoneNumber' => '577235000',
            'emailAddress' => '<EMAIL>',
        ],
            'contact' => [
                'personName' => 'Swiat Supli',
                'phoneNumber' => '577235000',
                'emailAddress' => '<EMAIL>',
            ],
            'address' => [
                'addressType' => 'B',
                'country' => 'PL',
                'name' => 'Swiat Supli',
                'postalCode' => str_replace('-', '', '33-300'),
                'city' => 'Bialystok',
                'street' => 'Stołeczna',
                'houseNumber' => '2/1',
            ]
        ];
        $this->setDeliveryDate();
        $this->setLabelType('ZBLP');
        $this->setShipmentInfo();
        $this->setShipper($this->defaultShipper);
//        $this->setLabelType('ZBLP300');
    }

    private function receiver(): void {
        if (!empty($this->receiver)) {
            $this->defaultData['shipment']['ship']['receiver'] = $this->receiver;
            error_log('Receiver is set: ' . json_encode($this->receiver));
        } else {
            error_log('Receiver data is empty');
        }
    }

    public function setReceiver(array $receiver): void {
        if (!empty($receiver)) {
            $this->receiver = $receiver;
            $this->receiver();
        }
    }

    private function content(): void {
        $this->defaultData['shipment']['content'] = $this->content;
    }

    public function setContent(string $content): void {
        $this->content = $content;
        $this->content();
    }

    private function package(): void {
        $this->defaultData['shipment']['pieceList']['item'] = $this->package;
    }

    public function setPackage(array $package): void {
        $this->package = $package;
        $this->package();
    }

    public function getDefaultData(): array {
        return $this->defaultData;
    }

    private function setDeliveryDate(): void {
        $this->defaultData['shipment']['shipmentInfo']['shipmentTime']['shipmentDate'] = date("Y-m-d");
    }

    private function setLabelType(string $labelType): void {
        $this->defaultData['shipment']['shipmentInfo']['labelType'] = $labelType;
    }

    private function setShipper(array $shipper): void {
        $this->defaultData['shipment']['ship']['shipper'] = $shipper;
    }

    public function setCustoms(array $custom): void {
        $this->defaultData['shipment']['customs'] = $custom;
    }

    public function setCOD(float $amount): void {
        $this->defaultData['shipment']['shipmentInfo']['specialServices'] = [];
        $this->defaultData['shipment']['shipmentInfo']['specialServices'][] = [
            'serviceType' => 'COD',
            'serviceValue' => $amount,
            'collectOnDeliveryForm' => 'BANK_TRANSFER'
        ];
        $this->defaultData['shipment']['shipmentInfo']['specialServices'][] = [
            'serviceType' => 'UBEZP',
            'serviceValue' => $amount
        ];
    }

    public function setShipmentInfo() {
        $this->defaultData['shipment']['serviceType'] = null;
        $this->defaultData['shipment']['shipmentInfo']['dropOffType'] = 'REGULAR_PICKUP';
        $this->defaultData['shipment']['shipmentInfo']['serviceType'] = 'AH';
        $this->defaultData['shipment']['shipmentInfo']['billing']['shippingPaymentType'] = 'SHIPPER';
        $this->defaultData['shipment']['shipmentInfo']['billing']['billingAccountNumber'] = '2391055';
        $this->defaultData['shipment']['shipmentInfo']['billing']['paymentType'] = 'BANK_TRANSFER';
        $this->defaultData['shipment']['shipmentInfo']['billing']['costsCenter'] = null;

    }


}