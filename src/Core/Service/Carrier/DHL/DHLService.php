<?php

namespace App\Core\Service\Carrier\DHL;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Address\AddressSplitService;
use App\Core\Service\Carrier\CarrierProviderInterface;
use App\Core\Service\Carrier\DHL\DataMapper\DHL24ShipperMapper;
use App\Core\Service\Carrier\DHL\Taxonomy\DhlParcelSettings;
use App\Core\Service\PrintNode\Enum\PrintNodeContentTypeEnum;
use Symfony\Component\Validator\Validator\ValidatorInterface;

final class DHLService implements CarrierProviderInterface {

//    private CONST CARRIER_TYPE = [
//        'Kurier DHL',
//        'DHL hurt',
//        'Kurier DHL pobranie',
//        'Kurier DHL przedpłata',
//        'DHL, Płatność przy odbiorze,Przesyłka kurierska pobraniowa',
//        ];

    private CONST CARRIER_TYPE = ['GG'];

    public function __construct(
        private readonly DHL24IntegrationRequestService $dhl24IntegrationRequestService,
        private readonly ValidatorInterface $validator,
        private readonly AddressSplitService $addressSplitService,

    ) {}

    public function supports(string $type): bool {
        return in_array($type, self::CARRIER_TYPE);
    }

    public function createShipment($order, $carrier, $additionalSettings) {
        $data = $this->mapData($order, $carrier, $additionalSettings);
        return $this->dhl24IntegrationRequestService->createShipment($data);
    }

    public function checkStatusOfShipment(string $shipmentId): array|bool {
        if ($shipmentId){
            return ['tracking_numbers' => [$shipmentId]];
        } else {
            return false;
        }
    }

    public function downloadShipmentLabel(string $shipmentId) {
        return $this->dhl24IntegrationRequestService->getLabel($shipmentId);
    }

    public function test(): bool {
        // TODO: Implement test() method.
    }

    public function mapData(Order $order, Carrier $carrier, array $additionalSettings): array {
        $mapObject = new DHL24ShipperMapper($this->validator, $this->addressSplitService);
        return $mapObject->fromOrder($order, $additionalSettings);
    }

    public function getParcelSettings(): array
    {
        return DhlParcelSettings::getAvailableParcelSettings();
    }

    public function getLabelType(Carrier $carrier): string {
        return PrintNodeContentTypeEnum::RAW_BASE64->value;
    }

    public function getCarrierType()
    {
        return self::CARRIER_TYPE;
    }

    public function setConnectionData(Carrier $carrier): void
    {
        $this->dhl24IntegrationRequestService->setConnectionData($carrier);
    }
    
    public function getAdditionalInfo(): array {
        return [];
    }
}