<?php


namespace App\Core\Service\Carrier\DHL\Taxonomy;

final class DhlParcelSettings {

    private const DIMENSIONS = [
        '1' =>[1],
        '5'=>[5],
        '30'=>[30],
    ];

    static public function getAvailableParcelSettings(): array {
        return self::DIMENSIONS;
    }

    static public function getParcelSettingByName($parcelName): array {
        return self::DIMENSIONS[$parcelName] ?? [];
    }
}