<?php

namespace App\Core\Service\Carrier\DHL;

use App\Core\Entity\Carrier;

class DHL24IntegrationRequestService
{
    public function __construct(private readonly DHL24SoapClientService $soapClient) {}

    public function setConnectionData(Carrier $carrier): void {
        $this->soapClient->setConnectionData($carrier);
    }
    public function getPrice(array $shipmentDetails)
    {
        try {
            return $this->soapClient->__soapCall('getPrice', [$shipmentDetails]);
        } catch (\SoapFault $e) {
            return $e->getMessage();
        }
    }

    public function createLabel(array $labelDetail)
    {
        try {
            return $this->soapClient->__soapCall('createShipment', [$labelDetail]);
        } catch (\SoapFault $e) {
            return $e->getMessage();
        }
    }

    public function createShipment(array $data){

        $result =  $this->createLabel($data);
        if (is_object($result) && isset($result->createShipmentResult->shipmentNotificationNumber)) {
            return ['id' => $result->createShipmentResult->shipmentNotificationNumber];
        } elseif (is_string($result)) {
            throw new \Exception($result);
        } else {
            throw new \Exception("Unexpected result type");
        }
    }

    public function getLabel($shipmentId) {
        $label = [
            'getLabels' => [
                'itemsToPrint' => [
                    'item' => [
                        'labelType' => 'ZBLP',
                        'shipmentId' => $shipmentId
                    ]
                ]
            ]
        ];

        $result = $this->soapClient->__soapCall('getLabels', $label);

        if (is_object($result)) {
            if (isset($result->getLabelsResult->item)) {
                $items = is_array($result->getLabelsResult->item) ? $result->getLabelsResult->item : [$result->getLabelsResult->item];

                $labelDataArray = [];
                foreach ($items as $item) {
                    if (isset($item->labelData)) {
                        $labelDataArray[] = $item->labelData;
                    }
                }
                if (!empty($labelDataArray)) {
                    return $labelDataArray[0];
                } else {
                    throw new \Exception("No labelData property is set in any item");
                }
            } else {
                throw new \Exception("item property is not set in getLabelsResult");
            }
        } elseif (is_string($result)) {
            throw new \Exception($result);
        } else {
            throw new \Exception("Unexpected result type");
        }
    }

}
