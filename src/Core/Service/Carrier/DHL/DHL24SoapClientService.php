<?php

namespace App\Core\Service\Carrier\DHL;

use App\Core\Entity\Carrier;

class DHL24SoapClientService
{

    private \SoapClient $soapClient;
    private mixed $username;
    private mixed $password;

    private array $options = [];

    public function __construct(array $options = []) {}

    public function setConnectionData(Carrier $carrier): void {
        $this->soapClient = new \SoapClient($carrier->getSettingsByKey('DHL24_WSDL_URL'), $this->options);
        $this->username = $carrier->getSettingsByKey('DHL24_USERNAME');
        $this->password = $carrier->getSettingsByKey('DHL24_PASSWORD');
    }

    public function __doRequest($request, $location, $action, $version, $oneWay = 0): ?string {
        $method = $this->extractActionName($action);
        $request = $this->addAuthenticationToRequest($request,$method);

        return $this->soapClient->__doRequest($request, $location, $action, $version, $oneWay);
    }


    private function addAuthenticationToRequest($request,$action): array|string|null {
        $authXml = "<authData><username>{$this->username}</username><password>{$this->password}</password></authData>";

        switch ($action) {
            case 'getLabels':
                $request = preg_replace('/<getLabels>/', "<getLabels>$authXml", $request);

            default:
                $request = preg_replace('/<authData xsi:nil="true"\/>/', $authXml, $request);
                break;
        }

        return $request;
    }
    private function extractActionName($actionUrl): string
    {
        // Extract the action name from the action URL
        $parsedUrl = parse_url($actionUrl);
        if (isset($parsedUrl['fragment'])) {
            return $parsedUrl['fragment'];
        }
        return '';
    }
}