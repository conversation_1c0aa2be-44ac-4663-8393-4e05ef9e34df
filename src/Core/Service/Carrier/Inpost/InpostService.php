<?php

namespace App\Core\Service\Carrier\Inpost;

use App\Core\Entity\Carrier;
use App\Core\Service\Address\AddressSplitService;
use App\Core\Service\Carrier\CarrierProviderInterface;
use App\Core\Service\Carrier\Inpost\DataMapper\ShipmentMapper;
use App\Core\Service\Carrier\Inpost\Taxonomy\ParcelSettings;
use App\Core\Service\PrintNode\Enum\PrintNodeContentTypeEnum;

final class InpostService implements CarrierProviderInterface {

    private CONST CARRIER_TYPE = [
        'Paczkomat Inpost',
        'Allegro Paczkomaty InPost',
        'Allegro Paczkomaty InPost pobranie',
        'Allegro Kurier24 InPost pobranie',
        'Paczkomaty InPost, Płatność z góry,Przesyłka',
    ];

    public function __construct(
        private readonly ApiHandler          $apiHandler,
        private readonly AddressSplitService $addressSplitService,
    ) {}

    public function test(): bool  {
        $this->apiHandler->getOrganizations();
        return true;
    }

    public function setConnectionData(Carrier $carrier): void
    {
        $this->apiHandler->createConnection($carrier);
    }

    public function createShipment($order, $carrier, $additionalSettings) {
        $data = $this->mapData($order, $carrier, $additionalSettings);

        return $this->apiHandler->createShipment($data);
    }

    public function checkStatusOfShipment(string $shipmentId): array|bool {
        return  $this->apiHandler->checkStatusOfShipment($shipmentId);
    }

    public function downloadShipmentLabel(string $shipmentId): array
    {

        return [base64_encode($this->apiHandler->downloadShipmentLabel($shipmentId))];
    }

    public function mapData($order, Carrier $carrier, array $additionalSettings): array
    {
        $mapObject = new ShipmentMapper($this->addressSplitService);

        return $mapObject->fromOrder($order, $additionalSettings);
    }

    public function supports(string $type): bool {
        return in_array($type, self::CARRIER_TYPE);
    }

    public function getParcelSettings(): array
    {
        return ParcelSettings::getAvailableParcelSettings();
    }

    public function getLabelType(Carrier $carrier): string {
        return PrintNodeContentTypeEnum::PDF_BASE64->value;
    }

    public function getCarrierType(): array
    {
        return self::CARRIER_TYPE;
    }

    static public function getCarrierTypeStatic(): array
    {
        return self::CARRIER_TYPE;
    }
    
    public function getAdditionalInfo(): array {
        return [];
    }
}