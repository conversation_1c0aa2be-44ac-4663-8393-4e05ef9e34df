<?php

namespace App\Core\Service\Carrier\Inpost;

use App\Core\Entity\OrderDeliveryShipmentData;
use App\Core\Service\Settings;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class InpostWebhookService {

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private HttpClientInterface             $httpClient, private readonly Settings $settings,
    ) {
    }


    public function handleShipmentCreated(array $payload) {
    }

    public function handleShipmentUpdated(array $payload): bool {
        if ($package = $this->entityManager->getRepository(OrderDeliveryShipmentData::class)->findOneBy(['tracking_id' => $payload['tracking_number']])) {
            $package->setShipmentStatus($payload['status']);
            $package->setShipmentStatusDate();
            $this->entityManager->persist($package);
            $this->entityManager->flush();
            return true;
        } else {
            return false;
        }
    }

    public function handleShipmentDeleted(array $payload) {
    }

    public function handleShipmentStatus($status) {
        $inpostUrl = $this->settings->get('INPOST_API_URL');
        $statusRequest = $this->httpClient->request('GET', $inpostUrl . '/v1/statuses');
        $statusData = json_decode($statusRequest->getContent(), true);

        if (!empty($statusData['items'])) {
            $names = array_column($statusData['items'], 'name');
            $index = array_search($status, $names);

            if ($index !== false) {
                return [
                    'status' => $statusData['items'][$index]['title'],
                    'description' => $statusData['items'][$index]['description']
                ];
            }
        }

        return ['status' => false];
    }
}

