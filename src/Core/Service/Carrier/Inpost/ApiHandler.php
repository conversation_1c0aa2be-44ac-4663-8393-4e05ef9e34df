<?php

namespace App\Core\Service\Carrier\Inpost;

use App\Core\Entity\Carrier;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\HttpClientInterface;

final class ApiHandler {

    private const SHIPMENT_ENDPOINT = '/v1/organizations/:orgId/shipments';
    private const GET_ORGANIZATIONS = '/v1/organizations/:orgId';
    private const GET_SHIPMENT_LABEL = '/v1/shipments/:shipment_id/label?format=Pdf&type=A6';
    private const GET_STATUS_OF_SHIPMENT = '/v1/shipments/:shipment_id';
    private string $url;
    private string $token;
    private string $orgId;

    public function __construct(private readonly HttpClientInterface $httpClient) {}

    public function createConnection(Carrier $carrier): void
    {
        $this->url = $carrier->getSettingsByKey('INPOST_API_URL');
        $this->token = $carrier->getSettingsByKey('INPOST_API_TOKEN');
        $this->orgId = $carrier->getSettingsByKey('INPOST_API_ORG_ID');
    }

    public function createShipment($options) {
        $url = $this->url . str_replace(':orgId', $this->orgId, self::SHIPMENT_ENDPOINT);

        return $this->handleResponse($this->request('POST', $url, $options));
    }

    public function downloadShipmentLabel(string $shipmentId) {
        $url = $this->url . str_replace(':shipment_id', $shipmentId, self::GET_SHIPMENT_LABEL);

        return $this->handleResponse($this->request('GET', $url, []));
    }

    public function getOrganizations() {
        $url = $this->url . str_replace(':orgId', $this->orgId, self::GET_ORGANIZATIONS);

        return $this->handleResponse($this->request('GET', $url, []));
    }

    public function checkStatusOfShipment(string $shipmentId) {
        $url = $this->url . str_replace(':shipment_id', $shipmentId, self::GET_STATUS_OF_SHIPMENT);
        $response = $this->handleResponse($this->request('GET', $url, []));
        if($response['tracking_number']){
            return ['tracking_numbers' => [$response['tracking_number']]];
        } else {
            return $this->handleResponse($this->request('GET', $url, []));
        }
    }

    public function request(string $method, $url, array $options) {
        $data = [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ],
        ];
        if (!empty($options)) {
            $data['body'] = json_encode($options, JSON_UNESCAPED_UNICODE);
        }
        return $this->httpClient->request($method, $url, $data);
    }

    public function handleResponse($response) {
        if (in_array($response->getStatusCode(), [Response::HTTP_CREATED, Response::HTTP_OK])) {
            $content = $response->getContent();

            return json_validate($content) ? json_decode($content, TRUE) : $content;
        }

        throw new \Exception($response->getContent(false));
    }
}