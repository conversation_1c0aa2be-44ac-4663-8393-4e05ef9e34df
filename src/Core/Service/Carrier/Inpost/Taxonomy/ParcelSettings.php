<?php

namespace App\Core\Service\Carrier\Inpost\Taxonomy;

final class ParcelSettings {

    private const DIMENSIONS = [
        'A' => [
            'dimensions' => [
                'length' => '80',
                'width' => '380',
                'height' => '640',
            ],
            'weight' => [
                'amount' => '24'
            ]
        ],
        'B' => [
            'dimensions' => [
                'length' => '190',
                'width' => '380',
                'height' => '640',
            ],
            'weight' => [
                'amount' => '24'
            ]
        ],
        'C' => [
            'dimensions' => [
                'length' => '410',
                'width' => '380',
                'height' => '640',
            ],
            'weight' => [
                'amount' => '24'
            ]
        ],
    ];

    static public function getAvailableParcelSettings(): array {
        return self::DIMENSIONS;
    }

    static public function getParcelSettingByName($parcelName): array {
        return self::DIMENSIONS[$parcelName] ?? [];
    }
}