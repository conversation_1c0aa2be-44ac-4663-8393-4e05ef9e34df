<?php

namespace App\Core\Service\Carrier\Inpost\DataMapper;

use App\Core\Entity\Order;
use App\Core\Service\Address\AddressSplitService;
use App\Core\Service\Carrier\Inpost\Taxonomy\ParcelSettings;
use App\Core\Taxonomy\InpostTaxonomy;
use App\Core\Utility\Money;

class ShipmentMapper {

    public function __construct(
        private readonly AddressSplitService $addressSplitService,
    ) {

    }
    public function fromOrder(Order $order, $additionalSettings): array {
        $data = [
            'receiver' => [
                'company_name' => $order->getOrderDelivery()->getDeliveryCompany(),
                'first_name' => $order->getOrderDelivery()->getDeliveryFirstname(),
                'last_name' => $order->getOrderDelivery()->getDeliveryLastname(),
                'email' => $order->getEmail(),
                'phone' => $order->getPhone(),
            ],
            'custom_attributes' => [
                'sending_method' => 'dispatch_order',
            ],
            'service' => InpostTaxonomy::getValueForName($order->getOrderDelivery()->getDeliveryMethod()),
            'reference' => $order->getOrderId(),
        ];
        if ($order->getPaymentMethodCod() && !isset($additionalSettings['disable_cod'])) {
            $codPayment = $additionalSettings['manual_cod_price'] ?? (new Money($order->getTotalOrderValue()))->add(new Money($order->getOrderDelivery()->getDeliveryPrice()))->toFloat();
            $data['cod'] = [
                'amount' => $codPayment,
                'currency' => $order->getCurrency(),
            ];
        }
        if('' !== $order->getOrderDelivery()->getDeliveryPointName()) {
            $data['custom_attributes']['target_point'] = str_replace('Paczkomat ', '', $order->getOrderDelivery()->getDeliveryPointName());
        } else {
            $address = $this->addressSplitService->split($order->getOrderDelivery()->getDeliveryAddress());
            $data['receiver']['address'] = [
                'street' => $address['street'],
                'building_number' => $address['houseNumber'] . ' ' . $address['apartmentNumber'],
                "city" => $order->getOrderDelivery()->getDeliveryCity(),
                'post_code' => str_replace('-', '', $order->getOrderDelivery()->getDeliveryPostcode()),
                "country_code" => $order->getOrderDelivery()->getDeliveryCountryCode(),
            ];
        }

        if (isset($additionalSettings['parcelSettings'])) {
            $data['parcels'] = ParcelSettings::getParcelSettingByName($additionalSettings['parcelSettings']);;
        }
        if (!empty($additionalSettings['forceStandardService'])) {
            $data['service'] = 'inpost_locker_standard';
        }

        return $data;
    }
}