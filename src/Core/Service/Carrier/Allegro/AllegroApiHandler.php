<?php

namespace App\Core\Service\Carrier\Allegro;

use App\Core\Service\Allegro\AllegroApiIntegrationService;

class AllegroApiHandler {
    public function __construct(
        private readonly AllegroApiIntegrationService $allegroApiIntegrationService,
    ) {
    }

    public function getAvailableDeliveryMethods(): array {
        return $this->allegroApiIntegrationService->orders()->shipment()->getAvailableDeliveryMethods();
    }
}