<?php

namespace App\Core\Service\Carrier\Allegro;

use App\Core\Entity\Carrier;
use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Entity\OrderDelivery;
use App\Core\Entity\OrderDeliveryShipmentData;
use App\Core\Service\Allegro\AllegroApiIntegrationService;
use App\Core\Service\Carrier\Allegro\Taxonomy\AllegroSettingsTaxonomy;
use App\Core\Service\Carrier\CarrierProviderInterface;
use App\Core\Service\PrintNode\Enum\PrintNodeContentTypeEnum;
use Doctrine\ORM\EntityManagerInterface;

class AllegroService implements CarrierProviderInterface {

    private CONST CARRIER_TYPE = [
        'Allegro Kurier DPD',
        'Allegro Kurier DPD pobranie',
        'Allegro One Box, UPS',
        'Allegro Kurier DHL Słowacja pobranie',
        'Allegro Kurier DHL Słowacja',
        'Allegro Odbiór w Punkcie Pocztex',
        'Allegro One Box, DPD',
        'Allegro One Punkt, UPS',
        'Allegro One Punkt, DPD',
        'Allegro Kurier DHL',
        'Allegro Kurier DHL pobranie',
        'Allegro Wysyłka z Polski do Czech - Odbiór w Punkcie Packeta',
        'Allegro Kurier Pocztex pobranie',
        'Allegro Odbiór w Punkcie Pocztex pobranie',
        'Allegro Automat DHL BOX 24/7',
        'Allegro Kurier Pocztex',
        'Allegro Odbiór w Punkcie DHL',
        'Allegro Kurier DHL Czechy',
        'Allegro Odbiór w Punkcie DPD Pickup',
        'Allegro Wysyłka z Polski do Czech - Odbiór w Punkcie Packeta pobranie',
        'Allegro Wysyłka z Polski do Słowacji - Odbiór w Punkcie Packeta',
        'Allegro Kurier24 InPost',
        'Allegro Automat ORLEN Paczka',
        'Allegro Odbiór w Punkcie ORLEN Paczka',
    ];

    public function __construct(
        private readonly AllegroApiIntegrationService $allegroApiIntegrationService,
        private readonly EntityManagerInterface $entityManager
) {
    }

    public function test(): bool {
        // TODO: Implement test() method.
    }

    public function supports(string $type): bool {
        return in_array($type, self::CARRIER_TYPE);
    }

    public function createShipment($order, $carrier, $additionalSettings): array
    {
        $data = $this->mapData($order, $carrier, $additionalSettings);
        $integration = $this->entityManager->getRepository(Integration::class)->findOneBy(['id' => $data['integrationId']]);
        $response = $this->allegroApiIntegrationService->orders()->shipment()->createShipment($data, $integration);
        return [
            'id' => $response['commandId']
        ];
    }

    public function checkStatusOfShipment(string $shipmentId): bool|array {
        if ($shipmentId){
            return ['tracking_numbers' => [$shipmentId]];
        } else {
            return false;
        }
    }

    public function downloadShipmentLabel(string $shipmentId) {
        $orderDeliveryShipmentData = $this->entityManager->getRepository(OrderDeliveryShipmentData::class)->findOneBy(['shipment_id' => $shipmentId]);
        /** @var OrderDelivery $orderDelivery */
        $orderDelivery = $orderDeliveryShipmentData->getOrderDeliveryId();
        /** @var Order $order */
        $order = $orderDelivery->getOrder();
        $integration = $this->entityManager->getRepository(Integration::class)->findOneBy(['id' => $order->getOrderSourceId()]);
        $getShipment = $this->allegroApiIntegrationService->orders()->shipment()->getShipmentId($shipmentId, $integration);
         return [base64_encode($this->allegroApiIntegrationService->orders()->shipment()->getShipmentLabel($getShipment, $integration))];
    }

    public function mapData(Order $order, Carrier $carrier, $additionalSettings): array {
        return [
            'orderId' => $order->getShopOrderId(),
            'externalOrderId' => $order->getExternalOrderId(),
            'integrationId' => $order->getOrderSourceId(),
            'additionalSettings' => $additionalSettings
        ];
    }

    public function getParcelSettings(): array {
        return AllegroSettingsTaxonomy::getAvailableParcelSettings();
    }

    public function getLabelType(Carrier $carrier): string {
         return PrintNodeContentTypeEnum::RAW_BASE64->value;
    }

    public function getCarrierType() {
       return self::CARRIER_TYPE;
    }

    public function setConnectionData(Carrier $carrier)
    {
        // TODO: Implement setConnectionData() method.
    }
    
    public function getAdditionalInfo(): array {
        return [];
    }
}