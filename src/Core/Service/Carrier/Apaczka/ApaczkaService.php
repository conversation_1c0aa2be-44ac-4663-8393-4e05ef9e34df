<?php

namespace App\Core\Service\Carrier\Apaczka;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Carrier\Apaczka\DataMapper\ServiceStructure;
use App\Core\Service\Carrier\Apaczka\DataMapper\ShipmentMapper;
use App\Core\Service\Carrier\CarrierProviderInterface;
use App\Core\Service\PrintNode\Enum\PrintNodeContentTypeEnum;
use Symfony\Component\HttpFoundation\Response;

class ApaczkaService implements CarrierProviderInterface
{

    public function __construct(
        private ApaczkaApi $apiHandler,
        private readonly ServiceStructure $serviceStructure,
        private ShipmentMapper $mapper
    ) {}
    public function test(): bool
    {
        return TRUE;
    }

    public function setConnectionData(Carrier $carrier)
    {
        $this->apiHandler->setConnectionData($carrier);
    }

    public function supports(string $type): bool
    {
        // TODO: Implement supports() method.
    }

    public function createShipment($order, $carrier, $additionalSettings): array {
        $data = $this->mapData($order, $carrier, $additionalSettings);
        $shipment = json_decode($this->apiHandler->order_send($data), TRUE);
        if (200 === $shipment['status']) {
            return [
                'id' => $shipment['response']['order']['id'],
                'type' => $shipment['response']['order']['service_name'],
                'tracking_url' => $shipment['response']['order']['tracking_url'],
            ];
        } else {
            throw new \Exception($shipment['message']);
        }
    }

    public function checkStatusOfShipment(string $shipmentId): array|bool
    {
        // TODO: Implement checkStatusOfShipment() method.
    }

    public function downloadShipmentLabel(string $shipmentId)
    {
        $response = $this->apiHandler->waybill($shipmentId);
        if (json_validate($response)) {
            $decodedResponse = json_decode($response, true);

            if (Response::HTTP_OK ===  $decodedResponse['status']) {
                return $decodedResponse['response']['waybill'];
            } else {
                throw new \Exception($decodedResponse['message']);
            }

        }

        throw new \Exception('Returned JSON response is invalid!');
    }

    public function mapData(Order $order, Carrier $carrier, array $additionalSettings): array
    {
        return $this->mapper->fromOrder($order, $carrier, $additionalSettings);
    }

    public function getParcelSettings(): array
    {
        // TODO: Implement getParcelSettings() method.
    }

    public function getLabelType(Carrier $carrier): string
    {
        $printType =  $carrier->getSettingsByKey('PRINT_TYPE');
        switch ($printType) {
            case 0 : {
                return PrintNodeContentTypeEnum::RAW_BASE64->value;
            }
            case 1 : {
                    return PrintNodeContentTypeEnum::PDF_BASE64->value;
                }
            default: {
                throw new \Exception('Unknown Print Type');
            }
        }
    }

    public function getCarrierType()
    {
        // TODO: Implement getCarrierType() method.
    }

    public function orders(array $data)
    {
        return $this->apiHandler->orders(1, 10);
    }

    public function order_send(array $order) {
        return $this->apiHandler->order_send($order);
    }

    public function service_structure() {
        return $this->apiHandler->service_structure();
    }

    public function getOrderlyServiceStructure(): array {

        return $this->serviceStructure->getOrderlyServiceStructure();
    }

    public function points(string $type) {
        return $this->apiHandler->points($type);
    }
    
    public function getAdditionalInfo() {
        return $this->getOrderlyServiceStructure();
    }
}