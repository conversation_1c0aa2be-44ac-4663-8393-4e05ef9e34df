<?php

namespace App\Core\Service\Carrier\Apaczka;

use App\Core\Entity\Carrier;

class ApaczkaApi {
	private string $appId = '2161835_3VKrS4LccBajSe1kYpUxQyfT';
	private string $appSecret = 'dx9sluzgats0xvyimvdv17gh9tkglw54';

	private string $apiUrl = 'https://www.apaczka.pl/api/v2/';

	const SIGN_ALGORITHM = 'sha256';

	const EXPIRES = '+25min';

    public function setConnectionData(Carrier $carrier) {
        $this->appId = $carrier->getSettingsByKey('APP_ID');
        $this->appSecret = $carrier->getSettingsByKey('APP_SECRET');
        $this->apiUrl = $carrier->getSettingsByKey('API_URL');
        date_default_timezone_set('Europe/Warsaw');
    }
	public function request( $route, $data = null) {
		$ch = curl_init();
		curl_setopt( $ch, CURLOPT_URL, $this->apiUrl . $route );
		curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
		curl_setopt($ch, CURLOPT_VERBOSE, true);

		curl_setopt( $ch, CURLOPT_POSTFIELDS, http_build_query( self::buildRequest($route, $data) ) );

		$result = curl_exec( $ch );
  
  
		if ( $result === false ) {
			curl_close( $ch );

			return false;
		}
		curl_close( $ch );

		return $result;
	}

	public  function buildRequest( $route, $data = [] ) {
		$data = json_encode($data, JSON_UNESCAPED_UNICODE);
		$expires = strtotime( self::EXPIRES );
		return [
			'app_id'    => $this->appId,
			'request'   => $data,
			'expires'   => $expires,
			'signature' => self::getSignature(self::stringToSign($this->appId, $route, $data, $expires), $this->appSecret)
		];
	}

	public  function order( $id ) {
		return self::request( __FUNCTION__ . '/' . $id . '/' );
	}

	public  function orders ($page = 1, $limit = 10) {
		return self::request( __FUNCTION__ . '/', [
			'page' => $page,
			'limit' => $limit
		]);
	}

	public  function waybill( $id ) {
		return self::request( __FUNCTION__ . '/' . $id . '/' );
	}

	public  function pickup_hours ($postal_code, $service_id = false) {
		return self::request( __FUNCTION__ . '/', [
			'postal_code' => $postal_code,
			'service_id' => $service_id
		]);
	}

	public  function order_valuation ($order) {
		return self::request( __FUNCTION__ . '/', [
			'order' => $order
		]);
	}

	public  function order_send ($order) {
		return self::request( __FUNCTION__ . '/', [
			'order' => $order
		]);
	}

	public  function cancel_order( $id ) {
		return self::request( __FUNCTION__ . '/' . $id . '/' );
	}

	public  function service_structure () {
		return self::request( __FUNCTION__ . '/');
	}

	public  function points (string $type = '') {
		return self::request( __FUNCTION__ . '/' . $type . '/');
	}

	public  function customer_register ($customer) {
		return self::request( __FUNCTION__ . '/', [
			'customer' => $customer
		]);
	}

	public  function turn_in( $order_ids = [] ) {
		return self::request( __FUNCTION__ . '/', [
			'order_ids' => $order_ids
		]);
	}

	public  function getSignature( $string, $key ) {
		return hash_hmac( self::SIGN_ALGORITHM, $string, $key );
	}

	public  function stringToSign( $appId, $route, $data, $expires ) {
		return sprintf( "%s:%s:%s:%s", $appId, $route, $data, $expires );
	}
}
