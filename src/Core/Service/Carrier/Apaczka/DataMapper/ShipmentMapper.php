<?php

namespace App\Core\Service\Carrier\Apaczka\DataMapper;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Address\AddressSplitService;
use App\Core\Service\Carrier\Inpost\Taxonomy\ParcelSettings;
use App\Core\Taxonomy\InpostTaxonomy;
use App\Core\Utility\Money;

class ShipmentMapper {

    public function __construct(
        private readonly AddressSplitService $addressSplitService,
    ) {

    }
    public function fromOrder(Order $order, Carrier $carrier, $additionalSettings): array {

        $address = $this->addressSplitService->split($order->getOrderDelivery()->getDeliveryAddress());
        $data = [
            'service_id'     => $additionalSettings['serviceId'],
            'address'        => [
                'sender'   => [
                    'country_code'   => 'PL',
                    'name'           => $carrier->getSettingsByKey('COMPANY_NAME'),
                    'line1'          => $carrier->getSettingsByKey('ADDRESS'),
                    'line2'          => $carrier->getSettingsByKey('ADDRESS'),
                    'postal_code'    => $carrier->getSettingsByKey('POSTAL_CODE'),
                    'state_code'     => '',
                    'city'           => $carrier->getSettingsByKey('CITY'),
                    'is_residential' => 0,
                    'contact_person' => $carrier->getSettingsByKey('CONTACT_PERSON'),
                    'email'          => $carrier->getSettingsByKey('EMAIL'),
                    'phone'          => $carrier->getSettingsByKey('PHONE'),
                    'foreign_address_id' => '',
                ],
                'receiver' => [
                    'country_code'   => $order->getOrderDelivery()->getDeliveryCountryCode(),
                    'name'           => $order->getOrderDelivery()->getDeliveryCompany() . ' ' . $order->getOrderDelivery()->getDeliveryFirstname() . ' ' . $order->getOrderDelivery()->getDeliveryLastname(),
                    'line1'          => $address['street'],
                    'line2'          => $address['houseNumber'] . ' ' . $address['apartmentNumber'],
                    'postal_code'    => $order->getOrderDelivery()->getDeliveryPostcode(),
                    'state_code'     => '',
                    'city'           => $order->getOrderDelivery()->getDeliveryCity(),
                    'is_residential' => 0,
                    'contact_person' => $order->getOrderDelivery()->getDeliveryFirstname() . ' ' . $order->getOrderDelivery()->getDeliveryLastname(),
                    'email'          => $order->getEmail(),
                    'phone'          => $order->getPhone(),
                    'foreign_address_id' => '',
                ]
            ],
            'shipment_value' => (new Money($order->getTotalOrderValue()))->add(new Money($order->getOrderDelivery()->getDeliveryPrice())),
            
            'pickup' => [
                'type' => 'COURIER',//, COURIER, SELF, BOX_MACHINE, POCZTA
                'date' => date('Y-m-d', strtotime($additionalSettings['date'])),
                'hours_from' => $carrier->getSettingsByKey('PICKUP_HOURS_FROM'),
                'hours_to' => $carrier->getSettingsByKey('PICKUP_HOURS_TO'),
            ],
            'cod'            => [
                'amount'      => 0, // wartość w groszach
                'bankaccount' => '' // tylko cyfry
            ],
            'shipment' => [
                [
                    'dimension1' => $additionalSettings['dimension1'],
                    'dimension2' => $additionalSettings['dimension2'],
                    'dimension3' => $additionalSettings['dimension3'],
                    'weight'     => $additionalSettings['weight'],
                    'is_nstd'    => 0,
                    'shipment_type_code' => $additionalSettings['shipmentTypeCode'],
                ]
            ],
            'content' => $additionalSettings['content'],
            'comment' => '',
            'is_zebra' => $carrier->getSettingsByKey('PRINT_TYPE'),
        ];
        if (isset($additionalSettings['options'])) {
            foreach ($additionalSettings['options'] as $option) {
                $data['options'][] = [
                    $option['name'] => $option['value']
                ];
            }
        }
        
        //waga produktów z presty

        return $data;
    }
}