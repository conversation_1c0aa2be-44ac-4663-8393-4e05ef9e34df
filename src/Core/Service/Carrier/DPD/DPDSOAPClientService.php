<?php

namespace App\Core\Service\Carrier\DPD;

use App\Core\Service\Settings;
use Psr\Log\LoggerInterface;
use SoapClient;

class DPDSOAPClientService extends SoapClient
{
    private string $login;
    private string $password;
    private string $masterFid;
    private array $params;
    private LoggerInterface $logger;

    public function __construct(Settings $settings, LoggerInterface $logger, array $options = [])
    {
        $this->logger = $logger;
        $this->login = $settings->get('DPD_LOGIN');
        $this->password = $settings->get('DPD_PASSWORD');
        $this->masterFid = $settings->get('DPD_MASTER_FID');

        $this->params = [
            'authDataV1' => [
                'login' => $this->login,
                'password' => $this->password,
                'masterFid' => $this->masterFid,
            ],
        ];

        $options = array_merge($options, [
            'trace' => 1,
            'exceptions' => true,
        ]);

        parent::__construct($settings->get('DPD_WSDL_URL'), $options);
    }

    public function __call($name, $args): mixed
    {
        $result = null;

        if (isset($args[0]) && is_array($args[0])) {
            $this->params = array_merge($this->params, $args[0]);

            try {
                $this->logger->info('SOAP Request', ['function' => $name, 'params' => $this->params]);
                $result = parent::__call($name, [$this->params]);
                $this->logger->info('SOAP Response', ['response' => $result]);
            } catch (\SoapFault $e) {
                $this->logger->error('SOAP Error', ['error' => $e->getMessage()]);
                return $e->getMessage();
            }

            if (isset($result->return)) {
                $result = $result->return;
            }

            if (isset($result->faultstring)) {
                $this->logger->error('SOAP Fault', ['fault' => $result->faultstring]);
                return $result->faultstring;
            }

            return $this->objectToArray($result);
        }

        return false;
    }

    private function objectToArray($response)
    {
        if (!is_object($response) && !is_array($response)) {
            return $response;
        }

        return array_map([$this, 'objectToArray'], (array) $response);
    }
}