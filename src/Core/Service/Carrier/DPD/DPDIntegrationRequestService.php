<?php

namespace App\Core\Service\Carrier\DPD;

class DPDIntegrationRequestService
{
    private DPDSOAPClientService $soapClient;

    public function __construct(DPDSOAPClientService $soapClient)
    {
        $this->soapClient = $soapClient;
    }

    public function generatePackageNumbers(array $shipmentDetails)
    {
        try {
            return $this->soapClient->__call('generatePackagesNumbersV9', [$shipmentDetails]);
        } catch (\SoapFault $e) {
            return $e->getMessage();
        }
    }

    public function orderCourier(array $orderDetails)
    {
        try {
            return $this->soapClient->__call('packagesPickupCallV4', [$orderDetails]);
        } catch (\SoapFault $e) {
            return $e->getMessage();
        }
    }
}