<?php


namespace App\Core\Service\Carrier\DPD;

use App\Core\Service\Settings;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Psr\Log\LoggerInterface;

class DPDMetaClientServices
{
    private HttpClientInterface $client;
    private LoggerInterface $logger;
    private string $apiUrl;
    private string $login;
    private string $password;
    private string $buCode;

    public function __construct(HttpClientInterface $client, LoggerInterface $logger,private readonly Settings $settings) {
        $this->client = $client;
        $this->logger = $logger;
        $this->apiUrl = $this->settings->get('DPD_META_API_URL');
        $this->login = $this->settings->get('DPD_META_LOGIN');
        $this->password = $this->settings->get('DPD_META_PASSWORD');
        $this->buCode = $this->settings->get('DPD_META_BUCODE');
    }

    public function authenticate(): ?string
    {
        $url = $this->apiUrl . '/login';
        $headers = [
            'X-DPD-BUCODE' => $this->buCode,
            'X-DPD-LOGIN' => $this->login,
            'X-DPD-PASSWORD' => $this->password,
            'accept' => '*',
        ];

        try {
            $response = $this->client->request('POST', $url, [
                'headers' => $headers,
            ]);

            $data = $response->getHeaders()['x-dpd-token'][0] ?? null;
            return $data ?? null;
        } catch (TransportExceptionInterface | ClientExceptionInterface | ServerExceptionInterface $e) {
            $this->logger->error('Authentication error: ' . $e->getMessage());
            return null;
        }
    }

    public function createShipment(array $shipmentDetails, string $token, string $labelFormat = 'ZPL'): array
    {
        $url = $this->apiUrl . '/shipment?LabelPrintFormat=' . $labelFormat;
        $headers = [
            'Authorization' => 'Bearer ' . $token,
            'Content-Type' => 'application/json',
        ];

        try {
            $response = $this->client->request('POST', $url, [
                'headers' => $headers,
                'json' => $shipmentDetails,
            ]);

            $content = $response->getContent(false);

            return ['response' => $content];
        } catch (TransportExceptionInterface | ClientExceptionInterface | ServerExceptionInterface $e) {
            $this->logger->error('Create shipment error: ' . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }
}