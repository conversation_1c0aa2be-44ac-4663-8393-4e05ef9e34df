<?php

namespace App\Core\Service\Carrier\GLS;

use App\Core\Entity\Carrier;
use App\Core\Entity\OrderDeliveryShipmentData;
use Doctrine\ORM\EntityManagerInterface;

class GLSIntegrationRequestService {

    private CONST PRINTING_MODE_ZEBRA = 'roll_160x100_zebra';

    public function __construct(
        private readonly GLSApiHandler $GLSApiHandler, private readonly EntityManagerInterface $entityManager,
    ) {
    }

    public function setConnectionData(Carrier $carrier): void {
        $this->GLSApiHandler->setConnectionData($carrier);
    }

    public function createShipment(array $data) {
        $response = $this->GLSApiHandler->request('adePreparingBox_Insert', $data);
        if($response->getStatusCode() === 200){
            $content = json_decode($response->getContent());
            return ['id' => $content->return->id] ?? NULL;
        } else {
            return  NULL;
        }
    }

    public function checkPreparingId(string $id): array|false{
        $response = $this->GLSApiHandler->request('adePreparingBox_GetConsign', ['id' => $id]);
        if ($response->getStatusCode() === 200) {
            $content = json_decode($response->getContent());
            if (!empty($content->return->parcels->items)) {
                $trackingNumbers = [];
                $both = false;

                foreach ($content->return->parcels->items as $item) {
                    if (!empty($item->number)) {
                        $trackingNumbers[] = $item->number;
                        $both = true;
                    }
                }

                if ($both) {
                    return ['tracking_numbers' => $trackingNumbers];
                } else {
                    // If both items don't have a number, proceed with the else block
                    $this->prepareLabelFromPreparing($id);
                    $responseSecond = $this->GLSApiHandler->request('adePreparingBox_GetConsign', ['id' => $id]);

                    if ($responseSecond->getStatusCode() === 200) {
                        $contentSecond = json_decode($responseSecond->getContent());
                        if (!empty($contentSecond->return->parcels->items)) {
                            if (is_array($contentSecond->return->parcels->items)) {
                                $count = count($contentSecond->return->parcels->items);
                            } else {
                                $count = 1;
                            }
                            $orderDetails = $this->entityManager->getRepository(OrderDeliveryShipmentData::class)->findBy(['shipment_id' => $id]);

                            foreach ($orderDetails as $key => $item) {
                                $item->setTrackingId(($count > 1)? $contentSecond->return->parcels->items[$key]->number : $contentSecond->return->parcels->items->number);
                                $this->entityManager->persist($item);
                            }

                            $this->entityManager->flush();

                            $trackingNumbersSecond = [];
                            if($count > 1){
                                foreach ($contentSecond->return->parcels->items as $item) {
                                    $trackingNumbersSecond[] = $item->number;
                                }
                            } else {
                                $trackingNumbersSecond[] = $contentSecond->return->parcels->items->number;
                            }

                            return ['tracking_numbers' => $trackingNumbersSecond];
                        }
                    }
                }
            } else {
                $this->prepareLabelFromPreparing($id);
                $responseSecond = $this->GLSApiHandler->request('adePreparingBox_GetConsign', ['id' => $id]);
                if ($responseSecond->getStatusCode() === 200) {
                    $contentSecond = json_decode($responseSecond->getContent());
                    if (!empty($contentSecond->return->parcels->items)) {
                        $orderDetails = $this->entityManager->getRepository(OrderDeliveryShipmentData::class)->findBy(['shipment_id' => $id]);
                        foreach ($orderDetails as $key => $item) {
                            $item->setTrackingId($contentSecond->return->parcels->items[$key]->number);
                            $this->entityManager->persist($item);
                        }
                        $this->entityManager->flush();

                        // Extract and return the tracking numbers as an array
                        $trackingNumbersSecond = [];
                        foreach ($contentSecond->return->parcels->items as $item) {
                            $trackingNumbersSecond[] = $item->number;
                        }

                        return ['tracking_numbers' => $trackingNumbersSecond];
                    }
                }
            }
        } else {
            return false;
        }
    }

    public function getLabelFromPreparing(int $id): array|false {
        $responseCheck = $this->checkPreparingId($id);
        if($responseCheck['tracking_numbers']){
                $labels = [];
                $response = $this->GLSApiHandler->request('adePreparingBox_GetConsignLabelsExt', ['id' => $id , 'mode' => self::PRINTING_MODE_ZEBRA]);
                $content = json_decode($response->getContent());
                if(is_array($content->return->items)){
                    foreach ($content->return->items as $label) {
                        $labels[] = (string)$label->file;
                    }
                } else {
                    $labels[] = (string)$content->return->items->file;
                }


            return $labels ?? false;
        } else
            return false;
    }

    public function getLabelByTrackingNumber(string $id): string {
        $response = $this->GLSApiHandler->request('adePickup_GetConsignLabels', ['id' => $id , 'mode' => self::PRINTING_MODE_ZEBRA]);
        return $response->return->labels ?? '';
    }

    public function getIdsToPickup(): array {
        $response = $this->GLSApiHandler->request('adePreparingBox_GetConsignIDs', ['id_start' => '0']);
        return $response->return->items ?? '';
    }

    public function createPickup(): string {
        $array = $this->getIdsToPickup();
        $response = $this->GLSApiHandler->request('adePickup_Create', ['consigns_ids' => $array,'desc' => 'nadanie']);
        return $response->return->items ?? '';
    }

    private function prepareLabelFromPreparing(string $id) {
        return $this->GLSApiHandler->request('adePreparingBox_GetConsignLabels', ['id' => $id , 'mode' => self::PRINTING_MODE_ZEBRA]);
    }


}