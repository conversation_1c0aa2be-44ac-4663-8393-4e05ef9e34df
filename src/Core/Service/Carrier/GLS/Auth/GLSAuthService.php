<?php

namespace App\Core\Service\Carrier\GLS\Auth;

use App\Core\Entity\Carrier;
use SoapClient;

class GLSAuthService
{
    private string $username;
    private string $password;
    private array|NULL $sessions;
    private string $wsdl;

    public function setConnectionData(Carrier $carrier): void {
        $this->username = $carrier->getSettingsByKey('GLS_USERNAME');
        $this->password = $carrier->getSettingsByKey('GLS_PASSWORD');
        $this->wsdl = $carrier->getSettingsByKey('GLS_WSDL_URL');
        $this->sessions = json_decode($carrier->getSettingsByKey('GLS_SESSIONS_JSON'), true);
    }

    public function authenticate(): array
    {
        $params = [
            'user_name' => $this->username,
            'user_password' => $this->password,
        ];

        $response = $this->authRequest($params);

        if (isset($response->return->session)) {
            $sessionId = $response->return->session;
            $this->addSession($sessionId);
            return ['Authenticated' => true, 'SessionId' => $sessionId];
        } else {
            throw new \Exception('Authentication failed: ' . $response->ErrorMessage);
        }
    }

    public function getValidSessionId(): ?string
    {
        if($this->sessions === null) {
            return null;
        }
        $currentTime = time();
        foreach ($this->sessions as $sessionId => $creationTime) {
            if (($currentTime - $creationTime) < 1800) {
                return $sessionId;
            }
        }
        return null;
    }

    public function updateSessionTime(string $sessionId): void
    {
        $this->sessions[$sessionId] = time();
        $this->saveSessions();
    }

    private function addSession(string $sessionId): void
    {
        if ($this->sessions !== NULL && count($this->sessions) >= 10) {
            array_shift($this->sessions);
        }
        $this->sessions[$sessionId] = time();
        $this->saveSessions();
    }

    private function saveSessions(): void
    {
        $this->settings->set('GLS_SESSIONS_JSON', json_encode($this->sessions));
    }

    private function authRequest($params){
        $client = new SoapClient($this->wsdl);
        try {
            return $client->__soapCall('adeLogin', [$params]);
        } catch (\SoapFault $fault) {
            throw new \Exception('SOAP Error: ' . $fault->faultstring);
        }
    }
}