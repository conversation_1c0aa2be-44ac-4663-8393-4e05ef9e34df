<?php

namespace App\Core\Service\Carrier\GLS;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Address\AddressSplitService;
use App\Core\Service\Carrier\CarrierProviderInterface;
use App\Core\Service\Carrier\GLS\DataMapper\GLSShipmentMapper;
use App\Core\Service\PrintNode\Enum\PrintNodeContentTypeEnum;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class GLSService implements CarrierProviderInterface {

    private CONST CARRIER_TYPE = [
        'Kurier DHL',
        'DHL hurt',
        'GLS Hurt',
        'Kurier DHL pobranie',
        'Kurier DHL przedpłata',
        'DHL, Płatność przy odbiorze,Przesyłka kurierska pobraniowa',
        'GLS',
        'GLS Kurier',
    ];

    public function __construct(
        private readonly GLSIntegrationRequestService $glsIntegrationRequestService,
        private readonly ValidatorInterface $validator,
        private readonly AddressSplitService $addressSplitService,

    ) {}

    public function test(): bool {
        return true;
    }

    public function supports(string $type): bool {
        return in_array($type, self::CARRIER_TYPE);
    }

    public function createShipment($order, $carrier, $additionalSettings)
    {
        $data = $this->mapData($order, $carrier, $additionalSettings);
        return $this->glsIntegrationRequestService->createShipment($data);
    }

    public function checkStatusOfShipment(string $shipmentId): array|bool
    {
        return $this->glsIntegrationRequestService->checkPreparingId($shipmentId);
    }

    public function downloadShipmentLabel(string $shipmentId)
    {
        return $this->glsIntegrationRequestService->getLabelFromPreparing($shipmentId);
    }

    public function mapData(Order $order, Carrier $carrier, array $additionalSettings): array
    {
        $mapObject = new GLSShipmentMapper($this->validator, $this->addressSplitService);
        return $mapObject->fromOrder($order, $additionalSettings);
    }

    public function getParcelSettings(): array
    {
        return ['1'=>'1kg'];
    }

    public function getLabelType(Carrier $carrier): string {
        return PrintNodeContentTypeEnum::RAW_BASE64->value;
    }

    public function getCarrierType(): array {
        return self::CARRIER_TYPE;
    }

    public function setConnectionData(Carrier $carrier): void
    {
        $this->glsIntegrationRequestService->setConnectionData($carrier);
    }
    
    public function getAdditionalInfo(): array {
        return [];
    }
}