<?php

namespace App\Core\Service\Carrier\GLS;

use App\Core\Entity\Carrier;
use App\Core\Service\Carrier\GLS\Auth\GLSAuthService;
use App\Core\Service\Settings;
use SoapClient;
use SoapFault;
use Symfony\Component\HttpFoundation\Response;

class GLSApiHandler {

    private string $wsdl;

    public function __construct(private readonly GLSAuthService $glsAuthService) {}

    public function setConnectionData(Carrier $carrier): void {
        $this->wsdl = $carrier->getSettingsByKey('GLS_WSDL_URL');
        $this->glsAuthService->setConnectionData($carrier);
    }

    public function request(string $functionName, array $params)
    {
        $sessionId = $this->glsAuthService->getValidSessionId();
        if (!$sessionId) {
            $sessionId = $this->glsAuthService->authenticate()['SessionId'];
        } else {
            $this->glsAuthService->updateSessionTime($sessionId);
        }

        $params['session'] = $sessionId;

        $client = new SoapClient($this->wsdl);
        try {
            $response = $client->__soapCall($functionName, [$params]);
            return $this->handleResponse($response);
        } catch (SoapFault $fault) {
            return $this->handleFault($fault, $client);
        }
    }

    private function handleResponse($response): Response
    {
        if (isset($response->ErrorMessage)) {
            throw new \Exception('SOAP Error: ' . $response->ErrorMessage);
        }
        return new Response(json_encode($response));
    }

    private function handleFault(SoapFault $fault, SoapClient $client)
    {
        // Log or print fault details for debugging
//        return 'Code: ' . $fault->faultcode . ', FaultString: ' . $fault->faultstring . PHP_EOL;

        // For detailed debugging (uncomment to use)
        // echo '<h2>Request</h2>';
        // echo '<pre>' . $client->__getLastRequestHeaders() . '</pre>';
        // echo '<pre>' . htmlspecialchars($client->__getLastRequest(), ENT_QUOTES) . '</pre>';
        // echo '<h2>Response</h2>';
        // echo '<pre>' . $client->__getLastResponseHeaders() . '</pre>';
        // echo '<pre>' . htmlspecialchars($client->__getLastResponse(), ENT_QUOTES) . '</pre>';

        return ['error' => true ,'message' => $fault->faultcode, 'code' => 404];
    }
}