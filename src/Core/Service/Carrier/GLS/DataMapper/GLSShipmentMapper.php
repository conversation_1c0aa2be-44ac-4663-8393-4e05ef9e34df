<?php

namespace App\Core\Service\Carrier\GLS\DataMapper;

use App\Core\Entity\Order;
use App\Core\Service\Address\AddressSplitService;
use App\Core\Utility\Money;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class GLSShipmentMapper
{
    public function __construct(
        private readonly ValidatorInterface $validator,
        private readonly AddressSplitService $addressSplitService
    ) {
    }

    public function fromOrder(Order $order, $additionalSettings): array
    {
        $shipperMapper = new GLSShipmentModel();
        $address = $this->addressSplitService->split($order->getOrderDelivery()->getDeliveryAddress());
        $receiverData = [
            'rname1' => $order->getOrderDelivery()->getDeliveryFullname(),
            'rcountry' => $order->getOrderDelivery()->getDeliveryCountryCode(),
            'rzipcode' => str_replace('-', '', $order->getOrderDelivery()->getDeliveryPostcode()),
            'rcity' => $order->getOrderDelivery()->getDeliveryCity(),
            'rstreet' => $address['street'] . ' ' . $address['houseNumber'] . ' ' . $address['apartmentNumber'],
            'rphone' => $order->getPhone(),
            'rcontact' => $order->getEmail(),
            'references' => $order->getOrderId(),
            'weight' => 1,
            'quantity' => $additionalSettings['quantity'] ?? 1
        ];

        $shipperMapper->setReceiver($receiverData);

        if($order->getPaymentMethodCod()){
            $serviceBooleans = [
            'cod' => 1,
            'cod_amount' => $additionalSettings['manual_cod_price'] ?? (new Money($order->getTotalOrderValue()))->add(new Money($order->getOrderDelivery()->getDeliveryPrice()))->toFloat()
        ];
        $shipperMapper->setServiceBooleans($serviceBooleans);
        }

        $quantity = $additionalSettings['quantity'] ?? 1;
        $parcels = [];

        for ($i = 0; $i < $quantity; $i++) {
            $parcel = new \stdClass();
            $parcel->weight = 1.0;
            $parcels[] = $parcel;
        }

        $shipperMapper->setParcels($parcels);

        $errors = $this->validator->validate($shipperMapper);
        if (count($errors) > 0) {
            $errorMessages = [];
            foreach ($errors as $error) {
                $fieldName = $error->getPropertyPath();
                $errorMessages[] = $fieldName . ': ' . $error->getMessage();
            }
            throw new \Exception("Validation failed: " . implode(", ", $errorMessages));
        }

        return $shipperMapper->getShipmentData();
    }
}
