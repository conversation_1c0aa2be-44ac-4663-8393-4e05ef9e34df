<?php

namespace App\Core\Service\Carrier\GLS\DataMapper;

use Symfony\Component\Validator\Constraints as Assert;

class GLSShipmentModel
{
    #[Assert\Collection(
        fields: [
            "rname1" => new Assert\NotBlank(),
            "rname2" => new Assert\Optional(),
            "rname3" => new Assert\Optional(),
            "rcountry" => new Assert\NotBlank(),
            "rzipcode" => new Assert\NotBlank(),
            "rcity" => new Assert\NotBlank(),
            "rstreet" => new Assert\NotBlank(),
            "rphone" => new Assert\NotBlank(),
            "rcontact" => new Assert\Optional(),
            "references" => new Assert\Optional(),
            "notes" => new Assert\Optional(),
            "weight" => [new Assert\NotBlank(), new Assert\Type(type: "numeric")],
            "quantity" => [new Assert\NotBlank(), new Assert\Type(type: "integer")]
        ],
        allowExtraFields: true,
        allowMissingFields: false
    )]
    private array $consign_prep_data;

    private array $parcels = [];

    public function setReceiver(array $receiver): void
    {
        $this->consign_prep_data = $receiver;
    }

    public function setServiceBooleans(array $serviceBooleans): void
    {
        $this->consign_prep_data['srv_bool'] = $serviceBooleans;
    }

    public function setParcels(array $parcels): void
    {
        $this->consign_prep_data['parcels'] = $parcels;
    }

    public function getShipmentData(): array
    {
        return [
            'consign_prep_data' => $this->consign_prep_data,
            'parcels' => $this->parcels
        ];
    }
}
