<?php

namespace App\Core\Service\Carrier;

use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;

readonly class CarrierFactory {

    public function __construct(#[AutowireIterator(CarrierProviderInterface::class)] private iterable $carriers) {}

    public function getCarrierProvider($type): CarrierProviderInterface {
        foreach ($this->carriers as $provider) {
            if ($provider->supports(trim($type))) {

                return $provider;
            }
        }

        throw new \Exception('No provider found');
    }

    public function getAvailableCarriers(): array {
        $providers = [];
        foreach ($this->carriers as $provider) {
            foreach ($provider->getCarrierType() as $carrier) {
                $providers[] = $carrier;
            }
        }

        return $providers;
    }

}