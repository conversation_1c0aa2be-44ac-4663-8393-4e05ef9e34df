<?php

namespace App\Core\Service\Carrier\Epaka;

use App\Core\Entity\Carrier;
use App\Core\Entity\CarrierSettings;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class EpakaApi {
	private string $login = '';
	private string $password = '';

	private string $apiUrl = '';

    private null|array $session = null;

    public function __construct(
        private HttpClientInterface $httpClient,
        private SerializerInterface $serializer,
        private EntityManagerInterface $entityManager,
    ) {
    }

    public function setConnectionData(Carrier $carrier) {
        $this->login = $carrier->getSettingsByKey('EMAIL');
        $this->password = md5($carrier->getSettingsByKey('PASSWORD'));
        $this->apiUrl = $carrier->getSettingsByKey('API_URL');
        $this->handleSession($carrier);
    }
    
    private function handleSession(Carrier $carrier) {
        if (NULL === ($session = $carrier->getSettingObjectByKey('API_SESSION'))) {
            $data = $this->login();
            
            $this->session = ['session' => $data['session'], 'time' => time()];
            $setting = new CarrierSettings();
            $setting->setName('API_SESSION');
            $setting->setValue(json_encode($this->session));
            $carrier->addCarrierSetting($setting);
            $this->entityManager->persist($carrier);
            $this->entityManager->flush();
            
        } else {
            $sessionData = json_decode($session->getValue(), true);
            $time = time();
            $sessionTime = $sessionData['time'];
            $diff = round(abs($time - $sessionTime) / 60, 2);
            if ($diff > 25 || null === $sessionData['session']) {
                $data = $this->login();
                
                $this->session = ['session' => $data['session'], 'time' => time()];
                $session->setName('API_SESSION');
                $session->setValue(json_encode($this->session));
                $this->entityManager->persist($session);
                $this->entityManager->flush();
            } else {
                $this->session = $sessionData;
            }
        }
    }

	public function request($route, $data = null) {
        if (NULL === $this->session && $route !== 'login.xml') {
            $this->login();
        }
        if (NULL !== $this->session) {
            $data['session'] = $this->session['session'];
        }

		return $this->handleResponse($this->httpClient->request('POST', $this->apiUrl . $route, ['body' => $data]));
	}
    
    public function makeRequest($url, $data) {
        return self::request($url . '.xml', $data);
    }

    private function handleResponse($response) {
        if (in_array($response->getStatusCode(), [Response::HTTP_CREATED, Response::HTTP_OK])) {
            $content = $response->getContent();

            return $this->serializer->decode($content, 'xml');
        }
        throw new \Exception($response->getContent(false));
    }

    public function login()
    {
        return self::request(__FUNCTION__ . '.xml', ['email' => $this->login, 'password' => $this->password]);
    }
}
