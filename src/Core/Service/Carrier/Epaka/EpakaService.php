<?php

namespace App\Core\Service\Carrier\Epaka;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Carrier\CarrierProviderInterface;
use App\Core\Service\Carrier\Epaka\DataMapper\ServiceStructure;
use App\Core\Service\Carrier\Epaka\DataMapper\ShipmentMapper;
use App\Core\Service\PrintNode\Enum\PrintNodeContentTypeEnum;

class EpakaService implements CarrierProviderInterface {
    
    public function __construct(private EpakaApi $apiHandler, private ShipmentMapper $shipmentMapper, private ServiceStructure $serviceStructure) {}
    
    public function test(): bool {
        return true;
    }
    
    public function setConnectionData(Carrier $carrier) {
        $this->apiHandler->setConnectionData($carrier);
    }
    
    public function supports(string $type): bool {
        // TODO: Implement supports() method.
    }

    public function getDPDHours(Carrier $carrier, Order $order, $date): array {
        return $this->apiHandler->makeRequest('dpdHours', ['country' => $carrier->getSettingsByKey('COUNTRY'), 'postCode' => $carrier->getSettingsByKey('POSTAL_CODE'), 'date' => $date]);
    }

    public function getDHLHours() {

    }
    public function getUpsHours() {

    }
    public function createShipment($order, $carrier, $additionalSettings) {
        $data = $this->mapData($order, $carrier, $additionalSettings);
        $results = $this->apiHandler->makeRequest('checkData', $data);
        if ('OK' === $results['status']) {
            $results = $this->apiHandler->makeRequest('makeOrder', $data);
            if  ('OK' === $results['status'] && 1 === $results['orderProcessResult']) {
                return ['id' => $results['orderId'], 'type' => $this->serviceStructure->getTypeById($data['courierId'])];

            } else {
                throw new \Exception($results['message']);
            }
        } else {
            throw new \Exception($results['message']);
        }
    }
    
    public function checkStatusOfShipment(string $shipmentId): array|bool {
        // TODO: Implement checkStatusOfShipment() method.
    }

    public function cancelOrder($orderId) {
        $effect = $this->apiHandler->makeRequest('cancelOrder/' . $orderId, []);
    }
    
    public function downloadShipmentLabel(string $shipmentId) {
        $label = $this->apiHandler->makeRequest('labelZebra/' . $shipmentId, []);

        return trim(preg_replace('/\s+/', '', $label['label']));
    }
    
    public function mapData(Order $order, Carrier $carrier, array $additionalSettings): array {
        return $this->shipmentMapper->fromOrder($order, $carrier, $additionalSettings);
    }
    
    public function getParcelSettings(): array {
        // TODO: Implement getParcelSettings() method.
    }
    
    public function getLabelType(Carrier $carrier): string {
        $printType =  $carrier->getSettingsByKey('PRINT_TYPE');
        switch ($printType) {
            case 'ZEBRA' : {
                return PrintNodeContentTypeEnum::PDF_BASE64->value;
            }
            case 'PDF' : {
                return PrintNodeContentTypeEnum::RAW_BASE64->value;
            }
            default: {
                throw new \Exception('Unknown Print Type');
            }
        }
    }
    
    public function getCarrierType() {
        // TODO: Implement getCarrierType() method.
    }
    
    public function orders(array $data) {
        return $this->apiHandler->orders(1, 10);
    }
    
    public function order_send(array $order) {
        return $this->apiHandler->order_send($order);
    }
    

    public function getAdditionalInfo(): array {
        return [
            [
                'name' => 'packagingType',
                'type' => 'select',
                'label' => 'Typ Paczki',
                'options' => [
                    [
                        'value' => 'YOUR_PACKAGING',
                        'label' => 'Własna Paczka'
                    ],
                    [
                        'value' => 'FEDEX_ENVELOPE',
                        'label' => 'Pak'
                    ],
                    [
                        'value' => 'FEDEX_BOX',
                        'label' => 'Skrzynka'
                    ],
                    [
                        'value' => 'FEDEX_PAK',
                        'label' => 'Fedex Paczka'
                    ],
                    [
                        'value' => 'FEDEX_TUBE',
                        'label' => 'Fedex Tub'
                    ]
                ]
            ],
            'itemDescriptionForClearance' => [],
            [
                'name' => 'serviceType',
                'type' => 'select',
                'label' => 'Serwis',
                'options' => [
                    [
                        'value' => 'INTERNATIONAL_ECONOMY',
                        'label' => 'Międzynarodowy ekonomiczny'
                    ],
                    [
                        'value' => 'FEDEX_REGIONAL_ECONOMY',
                        'label' => 'Regionaly ekonomiczny'
                    ],
                ]
            ],
            [
                'name' => 'totalDeclaredValue',
                'type' => 'text',
                'label' => 'Wartość',
            ],
            [
                'name' => 'totalDeclaredValueCurrency',
                'type' => 'text',
                'label' => 'Waluta(np. PLN)',
            ],
            [
                'name' => 'paymentType',
                'type' => 'select',
                'label' => 'Kto płaci',
                'options' => [
                    [
                        'value' => 'SENDER',
                        'label' => 'Nadawca'
                    ],
                    [
                        'value' => 'RECIPIENT',
                        'label' => 'Adresat'
                    ],
                    [
                        'value' => 'THIRD_PARTY',
                        'label' => 'Trzecia strona'
                    ],
                ]
            ],
        ];
        return $this->serviceStructure->getStructure();
    }
    
    public function decodeCourier() {
        return $this->apiHandler->makeRequest('decodeCourierId', []);
    }
}