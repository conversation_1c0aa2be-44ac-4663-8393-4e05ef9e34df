<?php

namespace App\Core\Service\Carrier\Epaka\DataMapper;

class ServiceStructure {

    const DPD_SHIPMENT_CODE_TYPES = [
        'paczka',
        'paleta',
        'koperta',
    ];

    const FEDEX_SHIPMENT_CODE_TYPES = [
        'paczka',
        'paleta',
        'koperta',
    ];
    const UPS_SHIPMENT_CODE_TYPES = [
        'paczka',
        'paleta',
        'koperta',
    ];
    const GLS_SHIPMENT_CODE_TYPES = [
        'paczka',
        'koperta',
    ];

    const AVAILABLE_CARRIER_TYPES = [
        'DPD','GLS','DHL','UPS','FedEx'
    ];
    private string $structure = '[{"id":"1","name":"DPD"},{"id":"2","name":"<PERSON><PERSON><PERSON>"},{"id":"3","name":"UPS"},{"id":"4","name":"<PERSON><PERSON> Międzynarodowy"},{"id":"5","name":"FedEx"},{"id":"6","name":"InPost Paczkomaty"},{"id":"8","name":"DHL"},{"id":"9","name":"Stacja z Paczką"},{"id":"10","name":"FedEx Priority"},{"id":"11","name":"Orlen Paczka"},{"id":"12","name":"InPost Kurier"},{"id":"13","name":"Delta"},{"id":"14","name":"Meest"},{"id":"15","name":"FedEx Biznes"},{"id":"16","name":"Patron Service"},{"id":"17","name":"Paczka 48"},{"id":"18","name":"Pocztex 24"},{"id":"19","name":"ERONTRANS"},{"id":"20","name":"DPD PickUp"},{"id":"21","name":"Ambro Express"},{"id":"22","name":"DHL Servicepoint"},{"id":"23","name":"DHL Express"},{"id":"25","name":"epaka.pl economy"},{"id":"28","name":"Goniec\nWarszawa"},{"id":"30","name":"UPS Lotniczy"},{"id":"31","name":"UPS Standard"},{"id":"43","name":"UPS Access Point"},{"id":"45","name":"FedEx Punkt"},{"id":"46","name":"FedEx Economy"},{"id":"48","name":"Allegro"},{"id":"49","name":"Pallex"},{"id":"50","name":"GLS Krajowy"},{"id":"52","name":"DHL Biznes"},{"id":"53","name":"Nova Poshta"},{"id":"54","name":"DHL Parcelshop"},{"id":"55","name":"Poczta Polska"},{"id":"56","name":"Hellmann"},{"id":"57","name":"InPost International"}]';

    public function getStructure(): array
    {
        $decodedServices = json_decode($this->structure, true);
        $available = self::AVAILABLE_CARRIER_TYPES;
        $filtered = array_filter($decodedServices, function ($item) use ($available) {
            if ($pos = strpos($item['name'], ' ')) {
                return in_array(substr($item['name'], 0, $pos), $available);
            }
            return in_array($item['name'], $available);
        });
        $name = array_column($filtered, 'name');
        array_multisort($name, SORT_ASC, $filtered);
        array_walk($filtered, function (&$item) {
            $item['shipmentTypeCode'] = ['paczka', 'paleta', 'koperta'];
        });


        return $filtered;
    }

    public function getTypeById($id) {
        $struct = $this->getStructure();
        $filtered = array_filter($struct, function ($item) use ($id) {
            return $item['id'] === $id;
        });

        return $filtered[array_key_first($filtered)]['name'];
    }
}