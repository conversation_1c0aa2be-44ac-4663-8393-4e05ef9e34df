<?php

namespace App\Core\Service\Carrier\Epaka\DataMapper;

use App\Core\Entity\Carrier;

class PickUpServiceSettings {

    public function getPickupTime(Carrier $carrier, $serviceId): array
    {
        switch ($serviceId) {
            //DPD
            case '1':
                //DPD PickUp
            case '20': {
                $pickupTimeFrom = $carrier->getSettingsByKey('PICKUP_HOURS_DPD_FROM');
                $pickupTimeTo = $carrier->getSettingsByKey('PICKUP_HOURS_DPD_TO');

                break;
            }
            //UPS
            case '3':
                //UPS Lotniczy
            case '30':
                //UPS Standard
            case '31':
                //Ups Access Point
            case '41': {
                $pickupTimeFrom = '';
                $pickupTimeTo = '';
                break;
            }
            //GLS Międzynarodowy
            case '4':
                //GLS KRAJOWY
            case '50': {
                $pickupTimeFrom = '';
                $pickupTimeTo = '';

                break;
            }
            //FedEx
            case '5':
                //FedEx Priority
            case '10':
                //FedEx Binzes
            case '15':
                //FedEx Punkt
            case '45':
                //FexEx Economy
            case '46': {
                $pickupTimeFrom = $carrier->getSettingsByKey('PICKUP_HOURS_FEDEX_FROM');
                $pickupTimeTo = $carrier->getSettingsByKey('PICKUP_HOURS_FEDEX_TO');

                break;
            }
            //DHL
            case '8':
                //DHL Servicepoint
            case '22':
                //DHL express
            case '23':
                //DHL Biznes
            case '52':
                //DHL parcelshop
            case '54':{
                $pickupTimeFrom = $carrier->getSettingsByKey('PICKUP_HOURS_DHL_FROM');
                $pickupTimeTo = $carrier->getSettingsByKey('PICKUP_HOURS_DHL_TO');
                break;
            }
            default: {
                $pickupTimeFrom = '';
                $pickupTimeTo = '';
            }
        }
        return [$pickupTimeFrom, $pickupTimeTo];
    }
}