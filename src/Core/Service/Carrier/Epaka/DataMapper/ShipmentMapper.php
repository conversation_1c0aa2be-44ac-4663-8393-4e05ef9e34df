<?php

namespace App\Core\Service\Carrier\Epaka\DataMapper;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Address\AddressSplitService;
use App\Core\Service\Carrier\Inpost\Taxonomy\ParcelSettings;
use Symfony\Component\Validator\Constraints\Timezone;

class ShipmentMapper {

    public function __construct(
        private readonly AddressSplitService $addressSplitService,
        private readonly PickUpServiceSettings $pickUpServiceSettings
    ) {

    }
    public function fromOrder(Order $order, Carrier $carrier, $additionalSettings): array {

        list($defaultPickupTimeFrom, $defaultPickupTimeTo) = $this->pickUpServiceSettings->getPickupTime($carrier, $additionalSettings['serviceId']);
        $pickupTimeFrom = $additionalSettings['pickupTimeFrom'] ?? $defaultPickupTimeFrom;
        $pickupTimeTo = $additionalSettings['pickupTimeTo'] ?? $defaultPickupTimeTo;


        $address = $this->addressSplitService->split($order->getOrderDelivery()->getDeliveryAddress());

        //PIckupTimeFrom tylko dla DPD, DHL, FedEx i FedEx lotniczy i UPS
        //PIckupTimeFrom tylko dla DPD, DHL, FedEx i FedEx lotniczy i UPS
        //deliveryDate tylko  YYYY-mm-dd niewymagane dla KEX, UPS, DPD, FedEx, FedEx Lotniczy i GLS

        $mapped = [
            'paymentType' =>  2,// 1 - płatność online za złożone zamówienie, 2 - płatność z salda, 3 - płatność zabonamentem
            'courierId' => $additionalSettings['serviceId'],
            'senderName' => $carrier->getSettingsByKey('NAME'),
            'senderLastName' => $carrier->getSettingsByKey('SURNAME'),
            'senderCompany' => $carrier->getSettingsByKey('COMPANY_NAME'),
            'senderStreet' => $carrier->getSettingsByKey('STREET'),
            'senderHouseNumber' => $carrier->getSettingsByKey('HOUSE_NUMBER'),
            'senderFlatNumber' => $carrier->getSettingsByKey('APARTMENT_NUMBER'),
            'senderPostCode' => $carrier->getSettingsByKey('POSTAL_CODE'),
            'senderCity' => $carrier->getSettingsByKey('CITY'),
            'senderCountry' => $carrier->getSettingsByKey('COUNTRY'),
            'senderEmail' => $carrier->getSettingsByKey('EMAIL'),
            'senderPhone' => $carrier->getSettingsByKey('PHONE'),
            'receiverName' => $order->getOrderDelivery()->getDeliveryFirstname(),
            'receiverLastName' => $order->getOrderDelivery()->getDeliveryLastname(),
            'receiverCompany' => $order->getOrderDelivery()->getDeliveryCompany(),
            'receiverStreet' => $address['street'] ?? $address[0],
            'receiverHouseNumber' => $address['houseNumber'] ?? $address[0],
            'receiverFlatNumber' => $address['apartmentNumber'] ?? $address[0],
            'receiverPostCode' => str_replace([' ', '-'], '', $order->getOrderDelivery()->getDeliveryPostcode()),
            'receiverCity' => AddressSplitService::removeDiacritics($order->getOrderDelivery()->getDeliveryCity()),
            'receiverCountry' => $order->getOrderDelivery()->getDeliveryCountryCode(),
            'receiverEmail' => $order->getEmail(),
            'receiverPhone' => $order->getPhone(),
//            'receiverMachineName' =>  ????????????
            'packageType' => $additionalSettings['shipmentTypeCode'], //paczka, paleta, koperta (dla DPD, KEX, FEdEx i UPS), dla GLS 'paczka', 'koperta', dla KEX 'rower' 'opona'

            'packages' => [
                'weight' => (float) $additionalSettings['weight'],
                'length' => $additionalSettings['dimension1'],
                'width' => $additionalSettings['dimension2'],
                'height' => $additionalSettings['dimension3'],
            ],
            'content' => $additionalSettings['comments'],
            'pickupDate' => date('Y-m-d', strtotime($additionalSettings['date'])),
            'pickupTimeFrom' => $pickupTimeFrom,
            'pickupTimeTo' => $pickupTimeTo,
            'comments' => $additionalSettings['comments'],
            'eori' => $additionalSettings['eori'] ?? '', //????
            'cod' => 0,
            'insurance' => $additionalSettings['insurance'] ?? '',
            'declaredValue' => $additionalSettings['declaredValue'] ?? '',
        ];

        return $mapped;
    }
}