<?php

namespace App\Core\Service\Carrier;

use App\Core\Carrier\CarrierType\CarrierTypeInterface;
use App\Core\Entity\Carrier;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

class CarrierManager {
    
    public function __construct(
        #[AutowireIterator('carrier.type.handler')] private $carrierTypes,
        private readonly EntityManagerInterface $entityManager,
        private readonly SerializerInterface $serializer)
    {}
    
    public function getCarrierHandler($requestedCarrierType): ?CarrierTypeInterface {
        foreach ($this->carrierTypes as $carrierType) {
            if($requestedCarrierType === $carrierType->getCarrierType()) {
                return $carrierType;
            }
        }
        
        return null;
    }
    
    public function getAllCarriers(): array {
        return $this->entityManager->getRepository(Carrier::class)->findAll();
    }
    
    public function getAllCarriersByType($data): array {
        $carriers = $this->entityManager->getRepository(Carrier::class)->findBy(['type' => $data['type']]);
        $handler = $this->getCarrierHandler($data['type']);
        $provider = $handler->getCarrierProvider();

        return ['carriers' => $carriers, 'additionalInfo' => $provider->getAdditionalInfo($data)];
    }
    
    public function getAvailableCarrierTypes(): array {
        $availableCarrierTypes = [];
        foreach ($this->carrierTypes as $carrierType) {
            $availableCarrierTypes[] = [
                'type' => $carrierType->getCarrierType(),
                'requiredFields' => $carrierType->getRequiredFields()
            ];
        }
        
        return $availableCarrierTypes;
    }
    
    public function getRequiredFieldsForCarrier($requestedCarrierType): array {
        $carrierType = $this->getCarrierHandler($requestedCarrierType);
        if(null === $carrierType) {
            throw new \Exception('Carrier type not found');
        }
        
        return $carrierType->getRequiredFields();
    }
    
    public function createCarrier($data) {
        try {
            $requestedCarrierType = $data['type'];
            $carrierType = $this->getCarrierHandler($requestedCarrierType);
            if(null === $carrierType) {
                throw new \Exception('Carrier type not found');
            }
            $errors = $this->validateCarrier($carrierType, $data);
            if(count($errors) > 0) {
                return $errors;
            }
            
            return $carrierType->createCarrier($data);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
    
    public function editCarrier(Carrier $carrierObject, array $data): Carrier {
        $carrierObject = $this->serializer->denormalize($data, Carrier::class, 'array', [AbstractNormalizer::OBJECT_TO_POPULATE => $carrierObject]);
        $this->entityManager->persist($carrierObject);
        $this->entityManager->flush();
        
        return $carrierObject;
    }
    
    public function validateCarrier(CarrierTypeInterface $carrierType, $data): array {
        $errors = [];
        try {
            $requiredFields = $carrierType->getRequiredFields();
            foreach ($requiredFields as $fieldData) {
                if(!isset($data[$fieldData['name']])) {
                    $errors[$fieldData['name']] = 'Field ' . $fieldData['name'] . ' is required';
                }
                switch ($fieldData['type']) {
                    case 'text':
                    case 'tel':
                    {
                        if(!is_string($data[$fieldData['name']])) {
                            $errors[$fieldData['name']] = 'Field ' . $fieldData['name'] . ' must be a string';
                        }
                        break;
                    }
                    case 'select':
                    {
                        $error = true;
                        $types = [];
                        foreach ($fieldData['options'] as $option) {
                            $types[] = $option['value'];
                            if($data[$fieldData['name']] == $option['value']) {
                                $error = false;
                                break;
                            }
                        }
                        if($error) {
                            $errors[$fieldData['name']] = 'Field ' . $fieldData['name'] . ' must be one of ' . implode(', ', $types);
                            break;
                        }
                        break;
                    }
                }
            }
        } catch (\Exception $e) {
            $errors['exception'] = $e->getMessage();
        } finally {
            return $errors;
        }
    }
    
    public function sanitizeCarrier(CarrierTypeInterface $carrierType, $data): array {
        $sanitizedFields = [];
        $requiredFields = $carrierType->getRequiredFields();
        foreach ($requiredFields as $fieldData) {
            $sanitizedFields[$fieldData['name']] = $data[$fieldData['name']];
        }
        
        return $sanitizedFields;
    }
}