<?php

namespace App\Core\Service\Carrier\Fedex\DataMapper;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Taxonomy\CountryIsoCodeEnum;

class FedexDataMapper {

    public function mapData(Order $order, Carrier $carrier, array $additionalSettings): array {
        $data = [
            'requestedShipment' => [
                'pickupType' => 'DROPOFF_AT_FEDEX_LOCATION',
                'serviceType' => $additionalSettings['serviceType'],
                'packagingType' => $additionalSettings['packagingType'],
                'total_weight' => $order->getWeight(),
                'totalDeclaredValue' => [
                    'amount' => $additionalSettings['totalDeclaredValue'],
                    'currency' => $additionalSettings['totalDeclaredValueCurrency'],
                ],
                "preferredCurrency" =>$additionalSettings['totalDeclaredValueCurrency'],
                'documentShipment' => false,
                'shipper' => [
                    'contact' => [
                        'personName' => $carrier->getSettingsByKey('CONTACT_PERSON'),
                        'phoneNumber' => $carrier->getSettingsByKey('PHONE'),
                    ],
                    'address' => [
                        'streetLines' => [
                            $carrier->getSettingsByKey('ADDRESS'),
                        ],
                        'city' => $carrier->getSettingsByKey('CITY'),
                        'stateOrProvinceCode' => '',
                        'postalCode' => $carrier->getSettingsByKey('POSTAL_CODE'),
                        'countryCode' => CountryIsoCodeEnum::getIsoCodeByCountry($carrier->getSettingsByKey('COUNTRY')),
                    ],

                ],
                'recipients' => [
                    [
                        'address' => [
                            'streetLines' => [
                                $order->getOrderDelivery()->getDeliveryAddress(),

                            ],
                            'city' => $order->getOrderDelivery()->getDeliveryCity(),
                            'stateOrProvinceCode' => '',
                            'postalCode' => $order->getOrderDelivery()->getDeliveryPostcode(),
                            'countryCode' => $order->getOrderDelivery()->getDeliveryCountryCode(),
                        ],
                        'contact' => [
                            'personName' => $order->getOrderDelivery()->getDeliveryFirstname() . ' ' . $order->getOrderDelivery()->getDeliveryLastname(),
                            'phoneNumber' => $order->getPhone(),
                        ]
                    ],
                ],
                'shippingChargesPayment' => [
                    'paymentType' => $additionalSettings['paymentType'],
                ],
                'labelSpecification' => [
                    'labelStockType' => 'PAPER_4X6',
                    'imageType' => 'PDF',
                ],

            ],
            'accountNumber' => [
                'value' => $carrier->getSettingsByKey('API_ACCOUNT_NO')
            ],
            'labelResponseOptions' => 'URL_ONLY',
            'shopAction' => 'CONFIRM',
        ];
        if (!empty($additionalSettings['documentId'])) {

            $data['requestedShipment']['shipmentSpecialServices'] = [
                'specialServiceTypes' => [
                    $additionalSettings['specialServiceTypes'] ?? 'ELECTRONIC_TRADE_DOCUMENTS'
                ],
                'etdDetail' => [
                    'attachedDocuments' => [
                        [
                            'documentId' => $additionalSettings['documentId'],
                        ]
                    ],
                ],
            ];
        }
        $commodities = [];
        $reqPackageLineItems = [];
        $i = 1;

        foreach ($order->getProducts() as $product) {
            if(empty($additionalSettings['harmonizedCode' . $product->getProductId()])) {
                throw new \Exception(sprintf('Not provided harmonizedCode code for product %s', $product->getName()));
            }

            $harmonizedCode = $additionalSettings['harmonizedCode' . $product->getProductId()];
            $description = $additionalSettings['description' . $product->getProductId()] ?? $product->getName();
            $commodities[] = [
                'unitPrice' => [
                    'amount' => $product->getFullPrice(),
                    'currency' => $additionalSettings['totalDeclaredValueCurrency'],
                ],
                'additionalMeasures' => [
                    [
                        'quantity' => $product->getWeight(),
                        'units' => 'KG',
                    ]
                ],
                'numberOfPieces' => 1,
                'quantity' => $product->getQuantity(),
                'weight' => [
                    'quantity' => $product->getWeight(),
                    'units' => 'KG',
                ],
                'quantityUnits' => 'NO',
                'description' => $description,
                'name' => $product->getName(),
                'countryOfManufacture' => 'PL',
                'harmonizedCode' => $harmonizedCode,
            ];
            $reqPackageLineItems[] = [
                'sequenceNumber' => $i,
                'weight' => [
                    'value' => $product->getWeight(),
                    'units' => 'KG',
                ],
                'itemDescriptionForClearance' => $description,
            ];
            $i++;
        }
        $data['requestedShipment']['requestedPackageLineItems'] = $reqPackageLineItems;
        $data['requestedShipment']['customsClearanceDetail']['commodities'] = $commodities;

        return $data;
    }

    public function mapDataForRateAndTransitTimes(Order $order, Carrier $carrier, array $additionalSettings): array {
        $data = [
            'accountNumber' => [
                'value' => $carrier->getSettingsByKey('API_ACCOUNT_NO')
            ],
            'requestedShipment' => [
                'shipper' => [
                    'address' => [
                        'postalCode' => str_replace('-', '', $carrier->getSettingsByKey('POSTAL_CODE')),
                        'countryCode' => CountryIsoCodeEnum::getIsoCodeByCountry($carrier->getSettingsByKey('COUNTRY')),
                    ],

                ],
                'recipient' => [
                    'address' => [
                        'postalCode' => $order->getOrderDelivery()->getDeliveryPostcode(),
                        'countryCode' => $order->getOrderDelivery()->getDeliveryCountryCode(),
                    ],
                ],
                'preferredCurrency' => $additionalSettings['totalDeclaredValueCurrency'],
                'shipDateStamp' => (new \DateTime('now'))->format('Y-m-d'),
                'serviceType' => $additionalSettings['serviceType'],
                'pickupType' => 'DROPOFF_AT_FEDEX_LOCATION',
                'rateRequestType' => [
                    'PREFERRED',
                ],
            ],
        ];
        $reqPackageLineItems = [];
        $weight = 0;
        foreach ($order->getProducts() as $product) {
            $weight += $product->getWeight();
        }
        $reqPackageLineItems[] = [
            'weight' => [
                'value' => (string) $weight,
                'units' => 'KG',
            ],
        ];
        $data['requestedShipment']['requestedPackageLineItems'] = $reqPackageLineItems;

        return $data;
    }
}