<?php

namespace App\Core\Service\Carrier\Fedex;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Carrier\CarrierProviderInterface;
use App\Core\Service\Carrier\Fedex\DataMapper\FedexDataMapper;
use App\Core\Taxonomy\CountryIsoCodeEnum;
use App\Core\Utility\Money;
use Doctrine\ORM\EntityManagerInterface;
use SCA\FedexApi\Fedex;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Mime\MimeTypes;

final readonly class FedexService implements CarrierProviderInterface {
    public function __construct(
        private Fedex $fedex,
        private Filesystem $filesystem,
        private FedexDataMapper $dataMapper,
        private EntityManagerInterface $entityManager,
    ) {}

    public function test(): bool {
        return true;
    }

    public function setConnectionData(Carrier $carrier): void {
        $this->fedex->setCredentials(
            $carrier->getSettingsByKey('API_KEY'),
            $carrier->getSettingsByKey('API_SECRET'),
            $carrier->getSettingsByKey('API_ACCOUNT_NO'),
            $carrier->getSettingsByKey('API_URL'),
        );
    }

    public function supports(string $type): bool {}

    public function createShipment($order, $carrier, $additionalSettings) {
        try {
            if (!empty($additionalSettings['shipmentAttachment'])) {
                $uploadedFile = str_replace(chr(0), '', base64_decode($additionalSettings['shipmentAttachment']['data']));
                $mime = new MimeTypes([]);
                $format = $mime->getExtensions($additionalSettings['shipmentAttachment']['type']);
                $filename = $order->getOrderId() . '.' . $format[array_key_first($format)];
                $filePath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $filename;
                $file = fopen($filePath, 'w');
                fwrite($file, $uploadedFile);
                fclose($file);
                $resultsUpload = $this->fedex->uploadDocument($filePath, $additionalSettings['requestedDocumentTypes'], CountryIsoCodeEnum::getIsoCodeByCountry($carrier->getSettingsByKey('COUNTRY')), $order->getOrderDelivery()->getDeliveryCountryCode());
                $additionalSettings['documentId'] = $resultsUpload['output']['meta']['docId'];
            }

            $mapped = $this->mapData($order, $carrier, $additionalSettings);
            $shipment = $this->fedex->createShipment($mapped);
            if (isset($shipment["output"]["transactionShipments"][0]["pieceResponses"][0]["packageDocuments"][0]["url"])) {
                $label = file_get_contents($shipment["output"]["transactionShipments"][0]["pieceResponses"][0]["packageDocuments"][0]["url"]);
                $labelName = $order->getOrderId() . '.pdf';
                $this->filesystem->dumpFile($labelName, $label);
            }
             return  [
                'type' => $carrier->getType(),
                'id' => $shipment["transactionId"],
                'tracking_url' => $shipment["output"]["transactionShipments"][0]["masterTrackingNumber"]
            ];
        } catch (\Exception $exception) {
            throw $exception;
        }
    }

    public function checkStatusOfShipment(string $shipmentId): array|bool
    {
        // TODO: Implement checkStatusOfShipment() method.
    }

    public function downloadShipmentLabel(string $shipmentId)
    {
        // TODO: Implement downloadShipmentLabel() method.
    }

    public function getParcelSettings(): array
    {
        // TODO: Implement getParcelSettings() method.
    }

    public function getLabelType(Carrier $carrier): string
    {
        // TODO: Implement getLabelType() method.
    }

    public function getCarrierType()
    {
        // TODO: Implement getCarrierType() method.
    }

    public function getAdditionalInfo(): array {
        $additionalProductSettings  = [];
        try {
            $args = func_get_arg(0);
            if (!isset($args['order'])) {
                throw new \Exception('No provided required data');
            }
            $orderEntity = $args['order'];
            foreach ($orderEntity->getProducts() as $product) {
                $additionalProductSettings[$product->getId()->toRfc4122() . '-harmonizedCode'] = [
                    'name' => 'harmonizedCode'.$product->getProductId(),
                    'type' => 'text',
                    'label' => $product->getName() . ' - Harmonized Code'
                ];
                $additionalProductSettings[$product->getId()->toRfc4122() . '-description'] = [
                    'name' => 'description'.$product->getProductId(),
                    'type' => 'text',
                    'label' => $product->getName() . ' - Description',
                    'defaultValue' => $product->getName()
                ];
            }
        } catch (\Throwable $th) {
            throw new \Exception('No provided required data');
        }
        return $additionalProductSettings + [
            [
                'name' => 'packagingType',
                'type' => 'select',
                'label' => 'Typ Paczki',
                'defaultValue' => 'YOUR_PACKAGING',
                'options' => [
                    [
                        'value' => 'YOUR_PACKAGING',
                        'label' => 'Własna Paczka'
                    ],
                    [
                        'value' => 'FEDEX_ENVELOPE',
                        'label' => 'Pak'
                    ],
                    [
                        'value' => 'FEDEX_BOX',
                        'label' => 'Skrzynka'
                    ],
                    [
                        'value' => 'FEDEX_PAK',
                        'label' => 'Fedex Paczka'
                    ],
                    [
                        'value' => 'FEDEX_TUBE',
                        'label' => 'Fedex Tub'
                    ]
                ]
            ],
            [
                'name' => 'serviceType',
                'type' => 'select',
                'label' => 'Serwis',
                'defaultValue' => 'FEDEX_REGIONAL_ECONOMY',
                'options' => [
                    [
                        'value' => 'INTERNATIONAL_ECONOMY',
                        'label' => 'Międzynarodowy ekonomiczny'
                    ],
                    [
                        'value' => 'FEDEX_REGIONAL_ECONOMY',
                        'label' => 'Regionaly ekonomiczny'
                    ],
                ]
            ],
            [
                'name' => 'totalDeclaredValue',
                'type' => 'text',
                'label' => 'Wartość',
                'defaultValue' => (new Money($orderEntity->getFullPrice()))->toFloat()
            ],
            [
                'name' => 'totalDeclaredValueCurrency',
                'type' => 'text',
                'label' => 'Waluta(np. PLN)',
                'defaultValue' => 'PLN',
            ],
            [
                'name' => 'paymentType',
                'type' => 'select',
                'label' => 'Kto płaci',
                'defaultValue' => 'SENDER',
                'options' => [
                    [
                        'value' => 'SENDER',
                        'label' => 'Nadawca'
                    ],
                    [
                        'value' => 'RECIPIENT',
                        'label' => 'Adresat'
                    ],
                    [
                        'value' => 'THIRD_PARTY',
                        'label' => 'Trzecia strona'
                    ],
                ]
            ],
            [
                'name' => 'shipmentAttachment',
                'type' => 'file',
                'label' => 'Faktura'
            ],
            [
                'name' => 'requestedDocumentTypes',
                'label' => 'Typ dokumentu ETD',
                'type' => 'select',
                'options' => [
                    [
                        'value' => 'CERTIFICATE_OF_ORIGIN',
                        'label' => 'Świade pochodzenia'
                    ],
                    [
                        'value' => 'COMMERCIAL_INVOICE',
                        'label' => 'Faktura Vat'
                    ],
                    [
                        'value' => 'USMCA_CERTIFICATION_OF_ORIGIN',
                        'label' => 'Świadectwo pochodzenia USMCA'
                    ],
                    [
                        'value' => 'USMCA_COMMERCIAL_INVOICE_CERTIFICATION_OF_ORIGIN',
                        'label' => 'Faktura handlowa i świadectwo pochodzenia USMCA'
                    ],
                    [
                        'value' => 'OTHER',
                        'label' => 'Inny'
                    ],
                    [
                        'value' => 'PRO_FORMA_INVOICE',
                        'label' => 'Faktura Pro Forma'
                    ],
                ]

            ]
        ];
    }

    public function mapData(Order $order, Carrier $carrier, array $additionalSettings): array
    {
        return $this->dataMapper->mapData($order, $carrier, $additionalSettings);
    }

    public function calculateShipment($order, $carrier, $additionalSettings) {
        $data = $this->dataMapper->mapDataForRateAndTransitTimes($order, $carrier, $additionalSettings);
        $this->setConnectionData($carrier);

        return $this->fedex->calculateShipment($data);
    }
}