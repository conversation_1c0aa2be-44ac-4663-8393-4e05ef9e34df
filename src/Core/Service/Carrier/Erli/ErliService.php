<?php

namespace App\Core\Service\Carrier\Erli;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Carrier\CarrierProviderInterface;
use App\Core\Service\PrintNode\Enum\PrintNodeContentTypeEnum;
use App\Core\Utility\Money;
use Doctrine\ORM\EntityManagerInterface;

final class ErliService implements CarrierProviderInterface {

    private const CARRIER_TYPE = ['ERLI InPost Paczkomaty 24/7 - 25 kg'];

    public function __construct(private readonly EntityManagerInterface $entityManager) {
    }

    public function test(): bool {
        return true;
    }

    public function supports(string $type): bool {
        return in_array($type, self::CARRIER_TYPE);
    }

    public function createShipment($order, $carrier, $additionalSettings) {
        $data = $this->mapData($order, $carrier, $additionalSettings);
        return $data;
    }

    public function checkStatusOfShipment(string $shipmentId): array|bool {
        return ['tracking_numbers' => [$shipmentId]];
    }

    public function downloadShipmentLabel(string $shipmentId) {
        return [$this->createZplBase64($shipmentId)];
    }

    public function mapData(Order $order, Carrier $carrier, array $additionalSettings): array {
        return ['id' => $order->getOrderId()];
    }

    public function getParcelSettings(): array {
        return ['erli' => NULL];
    }

    public function getType(): array {
        return self::CARRIER_TYPE;
    }

    public function getLabelType(Carrier $carrier): string {
        return PrintNodeContentTypeEnum::RAW_BASE64->value;
    }

    public function createZplBase64($idOrder): string {
        $order = $this->entityManager->getRepository(Order::class)->findOneBy(['order_id' => $idOrder]);

        $labelWidth = 800; // 100mm = 100/25.4*203 = ~800 dots
        $labelHeight = 1200; // 150mm = 150/25.4*203 = ~1200 dots

        /** @var Order $order */

        $zpl = "^XA^CI31" .
            "^PW" . $labelWidth .
            "^LL" . $labelHeight .
            "^FS^CFZ" .
            "^FO600,50^A@R,150,150^FD" . $this->encodeForZpl($order->getOrderId()) . "^FS" .
            "^FO300,50^A@R,100,100^FD" . $this->encodeForZpl('ERLI') .$this->encodeForZpl($order->getOrderDelivery()->getDeliveryFullname() . $this->encodeForZpl("ERLI")) . "^FS";
            if($order->getPaymentMethodCod()) {

               $zpl .= "^FO50,50^A@R,100,100^FD" . $this->encodeForZpl((new Money($order->getTotalOrderValue()))->add(new Money($order->getOrderDelivery()->getDeliveryPrice()))->toFloat()) . "^FS";
            }
            $zpl .= "^XZ";

        return base64_encode($zpl);
    }
    private function encodeForZpl($text)
    {
        return transliterator_transliterate('Any-Latin; Latin-ASCII', $text);
    }

    public function getCarrierType()
    {
        return self::CARRIER_TYPE;
    }

    public function setConnectionData(Carrier $carrier)
    {
        // TODO: Implement setConnectionData() method.
    }
    
    public function getAdditionalInfo(): array {
        return [];
    }
}