<?php

namespace App\Core\Service\Carrier;

use App\Core\Attribute\AsCarrierProvider;
use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AsCarrierProvider]
#[AutoconfigureTag(CarrierProviderInterface::class)]
interface CarrierProviderInterface {
    public function test(): bool;

    public function setConnectionData(Carrier $carrier);

    public function supports(string $type): bool;

    public function createShipment(Order $order, Carrier $carrier, array $additionalSettings);

    public function checkStatusOfShipment(string $shipmentId):array|bool;

    public function downloadShipmentLabel(string $shipmentId);

    public function mapData(Order $order, Carrier $carrier, array $additionalSettings): array;

    public function getParcelSettings(): array;

    public function getLabelType(Carrier $carrier): string;

    public function getCarrierType();
    
    public function getAdditionalInfo();
}