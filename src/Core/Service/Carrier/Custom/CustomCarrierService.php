<?php

namespace App\Core\Service\Carrier\Custom;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Carrier\CarrierProviderInterface;

readonly class CustomCarrierService implements CarrierProviderInterface
{

    public function __construct(private CustomList $customList) {}

    public function test(): bool
    {
        // TODO: Implement test() method.
    }

    public function setConnectionData(Carrier $carrier)
    {
        // TODO: Implement setConnectionData() method.
    }

    public function supports(string $type): bool
    {
        // TODO: Implement supports() method.
    }

    public function createShipment($order, $carrier, $additionalSettings)
    {
        $data = $this->mapData($order, $carrier, $additionalSettings);

        $data['trackingUrl'] = str_replace($data['trackingId'], '', $data['trackingUrl']);
        return ['id' => $data['trackingId'], 'type' => 'custom: ' . $data['carrierName'], 'tracking_url' => $data['trackingUrl']];
    }

    public function checkStatusOfShipment(string $shipmentId): array|bool
    {
        // TODO: Implement checkStatusOfShipment() method.
    }

    public function downloadShipmentLabel(string $shipmentId)
    {
        // TODO: Implement downloadShipmentLabel() method.
    }

    public function mapData(Order $order, Carrier $carrier, array $additionalSettings): array
    {
        $additionalSettings['order'] = $order;

        return $additionalSettings;
    }

    public function getParcelSettings(): array
    {
        // TODO: Implement getParcelSettings() method.
    }

    public function getLabelType(Carrier $carrier): string
    {
        // TODO: Implement getLabelType() method.
    }

    public function getCarrierType()
    {
        // TODO: Implement getCarrierType() method.
    }

    public function getAdditionalInfo(): array
    {
        return ['id', 'tracking_url', 'availableCarriers' => $this->customList->getArrayOfCustomCarriers()];
    }
}