<?php

namespace App\Core\Service\Carrier\Custom;

class CustomList
{
    const LIST  = 'Ajio Shipping, AliExpress Shipping - Brazil, Allegro.pl, Allegro Fulfillment, Allekurier.pl, Altex Shipping, Alza Shipping, Amazon Buy Shipping, Amazon Easy Ship, Amazon Easy Ship BR, Amazon Easy Ship PL, Amazon Shipping, Ambro, Americanas Entrega, Apaczka, APC, APC Overnight, Aramex International, Asendia, ASENDIA, Balikobot, BaseLinker Connect, Bliskapaczka.pl, BLPaczka, Blue Dart, Bookurier, BPS, Broker System, Cargus, Casas Bahia Entrega, CBLog, Cel.ro, Ceska posta, Clickpost, Colissimo, Correios, Dachser, Dafiti Shipping, DATAFRETE, DB Schenker (deprecated), <PERSON> Schenker (new), Delhivery, DeliGoo, Deutsche Post, Deutsche Post International, Deutsche Post UK, DHL DE (deprecated), DHL DE (new), DHL Ecommerce, DHL Express, DHL Parcel UK, DHL PL, Direct Link (PostNord), DPD CZ, DPD DE, DPD DE (new), DPD EE, DPD HU, DPD LT, DPD LV, DPD Meta, DPD PL, DPD RO, DPD SK, DPD UK, DX Express, DX Freight, E-commerce Int. Ukraina, EasyPost, eBay Shipping, Ecom Express, Ekart Logistics, eMag.bg AWB, eMag.hu AWB, eMag.ro AWB, enviamé, Envíopack - AR, Envíopack - CL, Envíopack - MX, Epaka, ePost, Erli, eShipz, EuroHermes, EuroHermes WMS, Evri (Hermes UK), Evri Corporate, Express, FAN Courier, FAN Courier International, Fashion Days AWB, FedEx.com, FedEx.pl, Flixlog, Foxpost, Frenet, Frete Rápido, Fulfillment Amazon, Fulfillment Tiba, Furdeco, Furgonetka, FX Couriers, Geis, Geis CZ, Geis SK, Geodis (Pekaes), Global24, GlobKurier, GLS CZ, GLS DE, GLS HR, GLS HU, GLS PL, GLS PT, GLS RO, GLS SI, GLS SK, GO4, GO balik CZ, GO balik SK, Hellmann PL, Hermes DE, IAI Broker, Inne, Innoship, InPost (UK), InPost International, InPost Kurier, InPost Paczkomaty, Intelipost, InternetMarke, J&amp;T Express, Jadlog, JAS-FBG, Joom Logistics, Kangu, KurJerzy, Landmark, Loggi, Logistiko, Magalu Entregas, Magyar Post, Mall By DPD, Mall Delivery, Manda Bem, Mandaê, Melhor Envio, MenedzerWysylek.pl, Mercado Envios - AR, Mercado Envios - BR, Mercado Envios - CL, Mercado Envios - CO, Mercado Envios - MX, Mercado Envios - PE, Mintsoft Fulfillment, Mondial Relay, MRPacket, MRW, Myntra Shipping, NemoExpress, Neoship, Netshoes (Magalu Entregas), NoLimit, Nova Post, Nowe Kolory, Octopia Fulfillment, OEX, OEX Fulfillment, Olist, OlzaLogistic, One by Allegro, ORLEN Paczka, Packeta, Pactic, Paketo24, Parcel2Go, Parcelforce, ParcelHub, Patron Service, Patron Service Broker, Paxy (Czech Logistic), Pegaz Kurier, PickPack, Pigu, PKWiD, Poczta Polska, Polkurier, Posta bez hranic, Postis, PPL, Purolator, Qlink, Raben, Rhenus Freight, Rohlig SUUS, Romania Express, Royal Mail API, Royal Mail Click&amp;Drop, Sameday.hu, Sameday.ro, Sendcloud, Shadowfax, Shein Shipping, ShipBob Fulfillment, ShipGlobal, Shippix, Shiprocket, ShipStation, Ship with Walmart, Shopee, Skroutz shipping, Slovak Parcel Service, Slovenska Posta, SmartShip, SP Express, Spring, SuperFrete, TakeMore.net shipping, TanieNadawanie.pl, TCE RO, TikTok Shipping, TNT, Total Express, UPS, USPS, USPS by EasyPost, VeeZuu, Walmart MCS, Wayfair Shipping, WeDo.cz (deprecated), Whistl, WysylajNami.pl, X-press Couriers, Xpress Delivery, Yodel, Zadbano, Zalando Shipping, Zasilkovna, Zonos, ';
    const COURIER_LIST_WITH_TRACKING_URL = [
        [
            'name' => 'UPS',
            'link' => 'https://www.ups.com/track?loc=en_US&tracknum='
        ],
        [
            'name' => 'DHL',
            'link' => 'https://www.dhl.com/pl-en/home/<USER>'
        ],
        [
            'name' => 'GLS',
            'link' => 'https://gls-group.com/PL/pl/sledzenie-paczek/?match=+'
        ],
        [
            'name' => 'DPD',
            'link' => 'https://tracktrace.dpd.com.pl/parcelDetails?typ=1&p1='
        ],
        [
            'name' => 'RABEN',
            'link' => 'https://myraben.com/link/ShipmentInformation?shipmentNumber='
        ],
        [
            'name' => 'INPOST',
            'link' => 'https://inpost.pl/en/find-parcel?number='
        ],
        [
            'name' => 'FURGONETKA',
            'link' => 'https://furgonetka.pl/zlokalizuj/'
        ],
        [
            'name' => 'APACZKA',
            'link' => 'https://www.apaczka.pl/sledz-przesylke/?waybill='
        ],
        [
            'name' => 'EPAKA',
            'link' => 'https://www.epaka.pl/sledzenie-przesylek/'
        ],
        [
            'name' => 'GLOBKURIER',
            'link' => 'https://www.globkurier.pl/shipment-tracking/'
        ],
        [
            'name' => 'POLKURIER',
            'link' => 'https://www.polkurier.pl/sledzenie-przesylek?number='
        ],
        [
            'name' => 'GEODIS',
            'link' => 'https://iris.geodis.com/Account/GeneralSearch.aspx?searchvalue='
        ],
        [
            'name' => 'KUEHNE NAGEL',
            'link' => 'https://mykn.kuehne-nagel.com/public-tracking/shipments?query='
        ],

    ];
    public function getArrayOfCustomCarriers(): array {
        return self::COURIER_LIST_WITH_TRACKING_URL;
    }

}