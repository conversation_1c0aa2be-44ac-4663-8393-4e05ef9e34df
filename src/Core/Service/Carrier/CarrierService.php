<?php

namespace App\Core\Service\Carrier;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Entity\OrderDeliveryShipmentData;
use App\Core\Entity\OrderStatus;
use App\Core\Service\PrintNode\PrintNodePostService;
use App\Core\Taxonomy\InpostStatusTaxonomy;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final readonly class CarrierService {

    public function __construct(
        private CarrierFactory         $carrierFactory,
        private EntityManagerInterface $entityManager,
        private PrintNodePostService   $printNodePostService,
        private CarrierManager         $carrierManager
    ) {}

    public function test(Order $order) {
        if ($carrierProvider = $this->getCarrierProvider($this->getCarrierType($order))) {
            return $carrierProvider->test();
        }
    }

    public function createShipment(Order $order, array $additionalData, Carrier $carrier) {
            $carrierService = $this->carrierManager->getCarrierHandler($carrier->getType())->getCarrierProvider();
            $carrierService->setConnectionData($carrier);
            try {
                $response = $carrierService->createShipment($order, $carrier, $additionalData);
            } catch (\Exception $exception) {
                    throw $exception;
            }
            if (!empty($response)) {
                try {
                    $quantity = isset($additionalData['quantity']) ? (int) $additionalData['quantity'] : 1;
                    $orderDeliveryShipmentDataArray = [];

                    for ($i = 0; $i < $quantity; $i++) {
                        $orderDeliveryShipmentData = new OrderDeliveryShipmentData();
                        $orderDeliveryShipmentData->setCarrierType($response['type']);
                        $orderDeliveryShipmentData->setOrderDeliveryId($order->getOrderDelivery());
                        $orderDeliveryShipmentData->setShipmentId((string) $response['id']);
                        $orderDeliveryShipmentData->setShipmentStatus(InpostStatusTaxonomy::INPOST_STATUS_CREATED);
                        $orderDeliveryShipmentData->setShipmentStatusDate();
                        $orderDeliveryShipmentData->setCarrier($carrier);
                        $orderDeliveryShipmentData->setTrackingId($response['tracking_url'] ?? '');
                        $order->getOrderDelivery()->addOrderDeliveryShipmentData($orderDeliveryShipmentData);
                        $orderDeliveryShipmentDataArray[] = $orderDeliveryShipmentData;
                        $this->entityManager->persist($orderDeliveryShipmentData);
                    }
                    $this->entityManager->flush();

                    return $orderDeliveryShipmentDataArray;

                } catch (\Exception $e) {
                    throw new \Exception($e->getMessage());
                }
            }
    }

    public function checkStatusOfShipment(Order $order, string $idShipment, Carrier $carrier): array|bool {
        if ($carrierService = $this->carrierManager->getCarrierHandler($carrier->getType())->getCarrierProvider()) {
            return $carrierService->checkStatusOfShipment($idShipment);
        }

        return false;
    }

    public function downloadShipmentLabelByIdShipment(Order $order, string $idShipment, Carrier $carrier): string|array|bool {
        if ($carrierService = $this->carrierManager->getCarrierHandler($carrier->getType())->getCarrierProvider()) {
            $carrierService->setConnectionData($carrier);

            return $carrierService->downloadShipmentLabel($idShipment);
        }

        return false;
    }

    private function getCarrierType(Order $order): string {
        return $order->getOrderDelivery()->getDeliveryMethod();
    }

    private function getCarrierProvider($type): bool|CarrierProviderInterface {
        try {
            $carrierProvider = $this->carrierFactory->getCarrierProvider($type);
            $request = Request::createFromGlobals();
            if ($request->get('carrierType')) {
                if (NULL !== ($carrierType = $this->entityManager->getRepository(Carrier::class)->find($request->get('carrierType')))) {
                    $carrierProvider->setConnectionData($carrierType);
                }
            }

            return $carrierProvider;
        } catch (\Exception $exception) {
            return false;
        }
    }

    public function getParcelsSettings(Order $order) {
        if ($carrierProvider = $this->getCarrierProvider($this->getCarrierType($order))) {
            return $carrierProvider->getParcelSettings();
        }
    }

    public function getLabelType(Carrier $carrier) {
        if ($carrierProvider = $this->carrierManager->getCarrierHandler($carrier->getType())->getCarrierProvider()) {
            return $carrierProvider->getLabelType($carrier);
        }
    }

    public function getCarrierTypes() {
        return $this->carrierFactory->getAvailableCarriers();
    }

    public function setTrackingIdAndPrintLabels($order, $orderDeliveryShipmentData) {

        $printCode = [Response::HTTP_NOT_IMPLEMENTED];
        $printResponse = new JsonResponse(['data' => 'Contact With Administrator!']);
        foreach ($orderDeliveryShipmentData as $odsd) {
            if ($label = $this->downloadShipmentLabelByIdShipment($order, $odsd->getShipmentId(), $odsd->getCarrier())) {
//                if ('dev' === $_ENV['APP_ENV']) {
//                    // Simulated Response Content
//                    $content = json_encode(['message' => 'Resource created successfully']);
//                    $printCode = Response::HTTP_CREATED;
//                    $printResponse = new Response($content, Response::HTTP_CREATED, [
//                        'Content-Type' => 'application/json',
//                    ]);
//                    continue;
//                }
                $printResponse = $this->printNodePostService->printByUserPrinter($this->getLabelType($odsd->getCarrier()), $label);
                $printCode = $printResponse->getStatusCode();
                if (Response::HTTP_CREATED === $printCode) {
                    $odsd->setLabel(TRUE);
                    $order->setInternalStatusId($this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_TO_SEND]));
                    $this->entityManager->persist($order);
                    $this->entityManager->persist($odsd);
                } else {
                    throw new \Exception('Cannot create a label for orderId ' . $order->getId() . ' and shipmentId: ' . $odsd->getShipmentId());
                }
            } else {
                throw new \Exception('Cannot create a label for orderId ' . $order->getId() . ' and shipmentId: ' . $odsd->getShipmentId());
            }
        }

        $this->entityManager->flush();

        return ['code' => $printCode, 'message' => $printResponse->getContent()];
    }
}