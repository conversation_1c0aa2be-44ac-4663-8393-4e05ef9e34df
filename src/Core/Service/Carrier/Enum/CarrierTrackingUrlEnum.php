<?php

namespace App\Core\Service\Carrier\Enum;

enum CarrierTrackingUrlEnum: string
{
    case DHL = 'Kurier DHL';
    case DHL_COD = 'DHL COD';
    case INPOST = 'Inpost';
    case PACZKOMATY_INPOST = 'Paczkomaty inpost';
    case GLS = 'GLS Kurier';

    public function getTrackingUrl(): string
    {
        return match($this) {
            self::DHL, self::DHL_COD => 'https://www.dhl.com/pl-en/home/<USER>/tracking-parcel.html?submit=1&tracking-id=',
            self::INPOST, self::PACZKOMATY_INPOST => 'https://inpost.pl/sledzenie-przesylek?number=',
            self::GLS => 'https://gls-group.com/PL/pl/sledzenie-paczek/?match=',
        };
    }
}