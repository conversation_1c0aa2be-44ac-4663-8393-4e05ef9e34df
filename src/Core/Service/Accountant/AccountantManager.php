<?php

namespace App\Core\Service\Accountant;

use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Service\Accountant\Types\Account\AccountInterface;
use App\Core\Service\Accountant\Types\Service\AccountantInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

class AccountantManager {

    public function __construct(
        #[AutowireIterator(AccountantInterface::class)] private iterable $availableAccountantSystems,
        #[AutowireIterator(AccountInterface::class)] private iterable $accountantAccounts,

        private readonly EntityManagerInterface $entityManager,
        private DenormalizerInterface $denormalizer
    ) {}

    public function getAccountantTypes(): array {
        return $this->availableAccountantSystems->toArray();
    }

    public function getAccountantAccounts(): array {
        return $this->accountantAccounts->toArray();
    }

    public function getAccountantAccountsByType(string $type): array
    {
        $accounts = [];
        foreach ($this->accountantAccounts as $accountantAccount) {
            if ($accountantAccount->getType() === $type) {
                $accounts[] = $accountantAccount;
            }
        }

        return $accounts;
    }

    public function getAccountantAccountType(string $type): ?AccountInterface {
        foreach ($this->accountantAccounts as $accountantAccount) {
            if ($accountantAccount->getType() === $type) {
                return $accountantAccount;
            }
        }
    }

    public function createAccount($data) {
        $accountType = $this->getAccountantAccountType($data['type']);
        return $accountType->createAccount($data);
    }

    public function updateAccount(AccountInterface $account, array $data) {
        $account = $this->denormalizer->denormalize($data, Order::class, null, [
            AbstractNormalizer::OBJECT_TO_POPULATE => $account,
            AbstractObjectNormalizer::DEEP_OBJECT_TO_POPULATE => true
        ]);
        $this->entityManager->persist($account);
        $this->entityManager->flush();
    }

    public function deleteAccount(AccountInterface $account) {
        $this->entityManager->remove($account);
        $this->entityManager->flush();
    }
}