<?php

namespace App\Core\Service\Accountant\Types\Service;

use App\Core\Service\Accountant\Types\Account\AccountInterface;

class InFakt extends AbstractAccountantService implements AccountantInterface {

    const TYPE = 'in_fakt';

    public function createInvoice(): void
    {
        // TODO: Implement createInvoice() method.
    }

    public function removeInvoice(): void
    {
        // TODO: Implement removeInvoice() method.
    }

    public function getType(): string
    {
        return self::TYPE;
    }

    public function supports($type): bool {
        return $this->getType() === $type;
    }


}