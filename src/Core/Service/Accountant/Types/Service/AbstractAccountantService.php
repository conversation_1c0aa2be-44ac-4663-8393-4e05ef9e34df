<?php

namespace App\Core\Service\Accountant\Types\Service;

use App\Core\Service\Accountant\Types\Account\AccountInterface;

class AbstractAccountantService implements AccountantInterface {

    protected AccountInterface $account;
    public function setAccount(AccountInterface $account)
    {
        $this->account = $account;
    }

    public function createInvoice(): void
    {
        // TODO: Implement createInvoice() method.
    }

    public function removeInvoice(): void
    {
        // TODO: Implement removeInvoice() method.
    }

    public function getType(): string
    {
        // TODO: Implement getType() method.
    }

    public function supports($type): bool
    {
        // TODO: Implement supports() method.
    }
}