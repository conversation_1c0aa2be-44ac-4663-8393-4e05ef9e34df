<?php

namespace App\Core\Service\Accountant\Types\Service;

use App\Core\Service\Accountant\Types\Account\AccountInterface;
use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag(AccountantInterface::class)]
interface AccountantInterface {

    public function setAccount(AccountInterface $account);
    public function createInvoice(): void;

    public function removeInvoice(): void;

    public function getType(): string;

    public function supports($type): bool;
}