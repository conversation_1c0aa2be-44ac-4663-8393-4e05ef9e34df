<?php

namespace App\Core\Service\Accountant\Types\Account;

use App\Core\Entity\AccounantAccount;
use App\Core\Entity\AccounantAccountSettings;
use Doctrine\ORM\EntityManagerInterface;

abstract class AbstractAccount {

    public function __construct(private EntityManagerInterface $entityManager) {}
    public function createAccount($data)
    {
        $account = new AccounantAccount();
        $account->setType($this->getType());
        $account->setName($data['name']);

        foreach ($this->getRequiredFields() as $field) {
            $accountSetting = new AccounantAccountSettings();
            $accountSetting->setName($field['name']);
            $accountSetting->setValue($data[$field['name']]);
            $account->addSetting($accountSetting);
        }

        $this->entityManager->persist($account);;
        $this->entityManager->flush();

        return $account;
    }

    abstract public function getType(): string;

    abstract public function getRequiredFields(): array;
}