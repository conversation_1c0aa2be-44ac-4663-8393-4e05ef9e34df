<?php

namespace App\Core\Service\Accountant\Types\Account;

class Subiekt extends AbstractAccount implements AccountInterface {

    const TYPE = 'subiekt';

    private const REQUIRED_FIELDS = [
        [
            'name' => 'FIELD_NAME',
            'type' => 'text',
            'label' => 'FIELD LABEL',
        ],
        [
            'name' => 'SELECT FIELD TYPE',
            'type' => 'select',
            'options' => [
                [
                    'value' => 0,
                    'label' => 'OPTION 1'
                ],
                [
                    'value' => 1,
                    'label' => 'OPTION 2'
                ],
            ],
            'label' => 'FIELD LABEL',
        ],
    ];

    public function supports($type): bool {
        return $type === self::TYPE;
    }

    public function getRequiredFields(): array {
        return self::REQUIRED_FIELDS;
    }

    public function getType(): string
    {
        return self::TYPE;
    }
}