<?php

namespace App\Core\Service\Accountant;

use App\Core\Service\Accountant\Types\Service\AccountantInterface;

readonly class AccountantService {
    public function __construct(
        private AccountantInterface $accountant
    ) {
    }

    public function createInvoice() {
        $this->accountant->createInvoice();
    }

    public function removeInvoice() {
        $this->accountant->createInvoice();
    }
}