<?php

namespace App\Core\Service\Accountant;

use App\Core\Entity\Settings;
use App\Core\Service\Accountant\Types\Service\AccountantInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;

readonly class AccountantServiceFactory {

    const ACCOUNTANT_TYPE_KEY = 'ACCOUNTANT_TYPE';
    public function __construct(
        #[AutowireIterator(AccountantInterface::class)] private iterable $services,
        private EntityManagerInterface $entityManager,
    ) {}

    public function getAccountantService(): AccountantInterface
    {
        $type = $this->getAccountantServiceType();
        foreach ($this->services as $service) {
            if ($service->supports($type)) {
                return $service;
            }
        }

        throw new \RuntimeException('Nieobsługiwany system księgowy.');
    }

    private function getAccountantServiceType(): array {
        return $this->entityManager->getRepository(Settings::class)->findBy(['name' => self::ACCOUNTANT_TYPE_KEY]);
    }
}