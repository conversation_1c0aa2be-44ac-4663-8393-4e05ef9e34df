<?php

namespace App\Core\Service\EmailEvent;

use App\Core\Entity\Order;
use App\Core\Utility\Money;

class EmailDataParser
{
    const AVAILABLE_TAGS = [
        'status',
        'email',
        'telefon',
        'imie_nazwisko',
        'imie',
        'nazwisko',
        'firma',
        'adres',
        'kod_pocztowy',
        'miasto',
        'kraj',
        'klient_login',
        'data_zamowienia',
        'numer_zamowienia_sklep',
        'laczna_cena',
        'waluta',
        'faktura_imie_nazwisko',
        'faktura_adres',
        'faktura_kod_pocztowy',
        'faktura_miasto',
        'faktura_kraj',
        'przesylka_przewoznik',
        'przesylka_link_sledzenia_wszystkie',
        'kraj_en',
        'kraj_de',
    ];

    private function toCammelCase(string $string) {
        return 'get' . str_replace('_', '', ucwords($string, '_'));

    }
    public function getAvailableTags(): array
    {
        return self::AVAILABLE_TAGS;
    }

    public function parseData(string $data, Order $order): string
    {
        foreach (self::AVAILABLE_TAGS as $tag) {
            if (method_exists($this, $this->toCammelCase($tag))) {
                $data = str_replace('[' . $tag . ']', $this->{$this->toCammelCase($tag)}($order), $data);
            }
        }
        return $data;
    }

    private function getImieNazwisko(Order $order): string {
        return $order->getOrderInvoice()->getInvoiceFullname();
    }

    private function getStatus(Order $order) {
        return $order->getInternalStatusId()->getName();
    }

    private function getTelefon(Order $order) {
        return $order->getPhone();
    }

    private function getImie(Order $order) {
        return $order->getOrderInvoice()->getInvoiceFullname();
    }

    private function getFirma(Order $order) {
        return $order->getOrderInvoice()->getInvoiceCompany();
    }
    private function getAdres(Order $order) {
        return $order->getOrderInvoice()->getInvoiceAddress();
    }
    private function getKodPocztowy(Order $order) {
        return $order->getOrderInvoice()->getInvoicePostcode();
    }

    private function getMiasto(Order $order) {
        return $order->getOrderInvoice()->getInvoiceCity();
    }

    private function getKraj(Order $order) {
        return $order->getOrderInvoice()->getInvoiceCountry();
    }

    private function getKlientLogin(Order $order) {
        return $order->getUserLogin();
    }

    private function getDataZamowienia(Order $order) {
        return $order->getDateAdd()->format('Y-m-d');
    }

    private function getNumerZamowieniaSklep(Order $order) {
        return $order->getExternalOrderId();
    }

    private function getLacznaCena(Order $order) {
        $money = new Money($order->getFullPriceWithDelivery());
        return $money->toFloat();
    }

    private function getWaluta(Order $order) {
        return $order->getCurrency();
    }

    private function getFakturaImieNaziwsko(Order $order) {
        return $order->getOrderInvoice()->getInvoiceFullname();
    }


    private function getFakturaAdres(Order $order) {
        return $order->getOrderInvoice()->getInvoiceAddress();
    }

    private function getFakturaKodPocztowy(Order $order) {
        return $order->getOrderInvoice()->getInvoicePostcode();
    }

    private function getFakturaMiasto(Order $order) {
        return $order->getOrderInvoice()->getInvoiceCity();
    }

    private function getFakturaKraj(Order $order) {
        return $order->getOrderInvoice()->getInvoiceCountry();
    }

    private function getPrzesylkaPrzewoznik(Order $order) {
        return $order->getOrderDelivery()->getDeliveryMethod();
    }

    private function getPrzesylkaLinkSledzeniaWszystkie(Order $order) {
        $trackings = [];

        foreach ($order->getOrderDelivery()->getOrderDeliveryShipmentData() as $shipmentData) {
            $trackings[] = $shipmentData->getTrackingId() . $shipmentData->getShipmentId();
        }

        return implode(', ', $trackings);
    }

    private function getKrajEn(Order $order) {
        return $this->getKraj($order);
    }

    private function getKrajDe(Order $order) {
        return $this->getKraj($order);
    }
}