<?php

namespace App\Core\Service\EmailEvent;

use App\Core\Entity\EmailAccount;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mailer\Transport;
use Symfony\Component\Mime\Email;

final class EmailSender {

    private NULL|Mailer $mailer;
    public function __construct(#[Autowire(env: 'APP_ENV')] private readonly string $env) {}

    public function send(string $content, EmailAccount $from, array|string $to, string $subject): void
    {
        $this->configureConnection($from);

        $mail = new Email();
        $mail->from($from->getEmailAddress());
        if (is_array($to)) {
            foreach ($to as $recipient) {
                $mail->addTo($recipient);
            }
        } else {
            $mail->addTo($to);
        }
        $mail
        ->subject($subject)
        ->html($content);

        $this->mailer->send($mail);
    }

    private function configureConnection(EmailAccount $from): void
    {
        if ($this->env === 'dev') {
            $dsn = sprintf(
                'smtp://%s:%s@%s:%d',
                '',
                '',
                'mailcatcher',
                '1025'
            );
        } else {
            $dsn = sprintf(
                'smtp://%s:%s@%s:%d?encryption=%s&auth_mode=login',
                $from->getSmtpLogin(),
                urlencode($from->getSmtpPassword()),
                $from->getSmtpAddress(),
                $from->getSmtpPort(),
                $from->getSmtpSecurity()
            );
        }
        $transport = Transport::fromDsn($dsn);
        if ($from->getDomain()) {
            $transport->setLocalDomain($from->getDomain());
        }
        $this->mailer = new Mailer($transport);
    }
}