<?php

namespace App\Core\Service\EmailEvent;

use App\Core\Entity\EmailAccount;
use App\Core\Entity\EmailTemplate;
use App\Core\Entity\Order;
use App\Core\Entity\OrderEmailOnStatus;
use App\Core\Entity\OrderStatus;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\SerializerInterface;

readonly class EmailEventService {

    public function __construct(
        private EntityManagerInterface $entityManager,
        private SerializerInterface $serializer,
        private EmailDataParser $dataParser,
        private EmailSender $emailSender
    ) {}

    public function createMailTemplate($data) {
        try {
            $template = $this->serializer->denormalize($data, EmailTemplate::class, 'array');
            $this->entityManager->persist($template);
            $this->entityManager->flush();

            return $template;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function editMailTemplate(EmailTemplate $template, $data) {
        try {
            $template = $this->serializer->denormalize($data, EmailTemplate::class, 'array', ['object_to_populate' => $template]);
            $this->entityManager->persist($template);
            $this->entityManager->flush();

            return $template;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function deleteMailTemplate(EmailTemplate $template): void {
        $this->entityManager->remove($template);
        $this->entityManager->flush();
    }

    public function bindMailToTemplate(EmailTemplate $template, OrderStatus $status, ?EmailAccount $emailAccount = NULL): OrderEmailOnStatus {
        $mailEvent = new OrderEmailOnStatus();
        $mailEvent->setEmailTemplate($template);
        $mailEvent->setStatus($status);
        if (NULL !== $emailAccount) {
            $mailEvent->setEmailFrom($emailAccount);
        }
        $this->entityManager->persist($mailEvent);
        $this->entityManager->flush();

        return $mailEvent;
    }

    public function editOrderEmailOnStatus(OrderEmailOnStatus $oeos, $data) {
        try {
            if (isset($data['orderStatusId'])) {
                $data['status'] = $this->entityManager->getRepository(OrderStatus::class)->find($data['orderStatusId']);
            }
            if (isset($data['templateId'])) {
                $data['emailTemplate'] = $this->entityManager->getRepository(EmailTemplate::class)->find($data['templateId']);
            }
            if (isset($data['emailFromId'])) {
                $data['emailFrom'] = $this->entityManager->getRepository(EmailAccount::class)->find($data['emailFromId']);
            }

            $oeos = $this->serializer->denormalize($data, OrderEmailOnStatus::class, 'array', ['object_to_populate' => $oeos]);
            $this->entityManager->persist($oeos);
            $this->entityManager->flush();

            return $oeos;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function deleteOrderEmailOnStatus(OrderEmailOnStatus $oeos): void {
        $this->entityManager->remove($oeos);
        $this->entityManager->flush();
    }

    public function findAllMailTemplate(): array {
        $data = [];

        foreach ($this->entityManager->getRepository(EmailTemplate::class)->findAll() as $template) {
            $data[] = $this->serializer->normalize($template, 'array');
        }

        return $data;
    }

    public function findAllBindings(): array {
        $data = [];

        foreach ($this->entityManager->getRepository(OrderEmailOnStatus::class)->findAll() as $binding) {
            $data[] = $this->serializer->normalize($binding, 'array');
        }

        return $data;
    }

    public function sendEmailOnStatus(Order $order, OrderEmailOnStatus $emailOnStatus): void {
        try {
            $emailContent = $this->dataParser->parseData($emailOnStatus->getEmailTemplate()->getContent(), $order);
            $from = $emailOnStatus->getEmailFrom() ?? $emailOnStatus->getEmailTemplate()->getEmailAccount();
            $to = $order->getEmail();
            $subject = $emailOnStatus->getEmailTemplate()->getSubject();
            $this->emailSender->send($emailContent, $from, $to, $subject);

        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function getAvailableTags() {
        return $this->dataParser->getAvailableTags();
    }
}