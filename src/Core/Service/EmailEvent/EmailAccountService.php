<?php

namespace App\Core\Service\EmailEvent;

use App\Core\Entity\EmailAccount;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\SerializerInterface;

readonly class EmailAccountService
{

    public function __construct(
        private EntityManagerInterface $entityManager,
        private SerializerInterface    $serializer
    ) {}

    public function createEmailAccount($data) {
        try {
            $emailAccount = $this->serializer->denormalize($data, EmailAccount::class, 'array');
            $this->entityManager->persist($emailAccount);
            $this->entityManager->flush();

            return $emailAccount;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function editEmailAccount(EmailAccount $emailAccount, $data) {
        try {
            $emailAccount = $this->serializer->denormalize($data, EmailAccount::class, 'array', ['object_to_populate' => $emailAccount]);
            $this->entityManager->persist($emailAccount);
            $this->entityManager->flush();

            return $emailAccount;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function deleteEmailAccount(EmailAccount $emailAccount): void {
        $this->entityManager->remove($emailAccount);
        $this->entityManager->flush();
    }

    public function findAllEmailAccounts(): array
    {
        return $this->entityManager->getRepository(EmailAccount::class)->findAll();
    }


}