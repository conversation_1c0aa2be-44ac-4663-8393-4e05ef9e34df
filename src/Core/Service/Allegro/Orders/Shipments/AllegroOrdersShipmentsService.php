<?php

namespace App\Core\Service\Allegro\Orders\Shipments;

use App\Core\Entity\Order;
use App\Core\Service\Allegro\AllegroApiHandler;
use App\Core\Service\Allegro\Orders\Order\AllegroOrdersOrderInterface;
use App\Core\Service\Carrier\Allegro\Taxonomy\AllegroSettingsTaxonomy;
use App\Core\Utility\Money;
use Doctrine\ORM\EntityManagerInterface;

class AllegroOrdersShipmentsService implements AllegroOrdersShipmentsInterface
{
    const RESOURCE = 'shipment-management';

    public function __construct(
        private readonly AllegroApiHandler           $allegroApiHandler,
        private readonly AllegroOrdersOrderInterface $allegroOrdersOrderInterface,
        private readonly EntityManagerInterface $entityManager,
    ) {}

    public function getAvailableDeliveryMethods($integration): array
    {
        return $this->allegroApiHandler->get(self::RESOURCE, 'delivery-services',$integration,['accept' => 'application/vnd.allegro.public.v1+json']);
    }


    public function createShipment($data, $integration): array {
        $orderId = $data['externalOrderId'];
        $order = $this->allegroOrdersOrderInterface->getOrder($orderId, $integration);
        $credentialsId = $this->getCredentialsId($order['delivery']['method']['id'], $integration);
        $mainOrder = $this->entityManager->getRepository(Order::class)->findOneBy(['external_order_id' => $orderId]);
        $weight = 1.45;
        if(in_array($mainOrder->getOrderDelivery()->getDeliveryMethod(), [
            'Allegro Kurier DPD',
            'Allegro Kurier DPD pobranie',
            'Allegro One Box, DPD',
            'Allegro One Punkt, DPD',
        ])) {
            $weight = 10;
        }
        $labelData = [
            'input' => [
                'deliveryMethodId' => $order['delivery']['method']['id'],
                'credentialsId' => $credentialsId['credentialsId'],
                'sender' => [
                    'name' => 'Swiat Supli',
                    'company' => 'Swiat Supli',
                    'street' => 'Stołeczna 2/lok 102',
                    'streetNumber' => '30',
                    'postalCode' => '15-879',
                    'city' => 'Białystok',
                    'countryCode' => 'PL',
                    'email' => '<EMAIL>',
                    'phone' => '***********',
                ],
                'receiver' => [
                    'name' => $this->trimString($order['delivery']['address']['firstName'] . ' ' . $order['delivery']['address']['lastName']),
                    'company' => $this->trimString($order['buyer']['companyName']),
                    'street' => $order['delivery']['pickupPoint']['address']['street'] ?? $order['delivery']['address']['street'],
                    'streetNumber' => '',
                    'city' => $order['delivery']['pickupPoint']['address']['city'] ?? $order['delivery']['address']['city'],
                    'postalCode' => $order['delivery']['address']['zipCode'],
                    'countryCode' => $order['delivery']['address']['countryCode'],
                    'email' => $order['buyer']['email'],
                    'phone' => $order['delivery']['address']['phoneNumber'],
                    'point' => $order['delivery']['pickupPoint']['id'] ?? NULL,
                ],
                'referenceNumber' => $mainOrder->getOrderId(),
                'description' => 'Suplementy',
                'packages' => [
                    [
                        'type' => 'PACKAGE',
                        'length' => [
                            'value' => 16,
                            'unit' => 'CENTIMETER'
                        ],
                        'width' => [
                            'value' => 12,
                            'unit' => 'CENTIMETER'
                        ],
                        'height' => [
                            'value' => 12,
                            'unit' => 'CENTIMETER'
                        ],
                        'weight' => [
                            'value' => $weight,
                            'unit' => 'KILOGRAMS'
                        ]
                    ]
                ],
                'labelFormat' => 'ZPL'
            ]
        ];

        if ($mainOrder->getPaymentMethodCod() && !isset($data['additionalSettings']['disable_cod'])) {

            $codPayment = $data['additionalSettings']['manual_cod_price'] ?? (new Money($mainOrder->getTotalOrderValue()))->add((new Money($mainOrder->getOrderDelivery()->getDeliveryPrice())))->toFloat();

            $labelData['input']['cashOnDelivery'] = [
                'amount' => (string)$this->formatAmount($codPayment),
                'currency' => $mainOrder->getCurrency()
            ];
            if(
                'Allegro Kurier DHL Słowacja pobranie' !== $mainOrder->getOrderDelivery()->getDeliveryMethod() &&
                'Allegro Kurier DHL Czechy pobranie' !== $mainOrder->getOrderDelivery()->getDeliveryMethod() &&
                'Allegro Wysyłka z Polski do Czech - Odbiór w Punkcie Packeta pobranie' !== $mainOrder->getOrderDelivery()->getDeliveryMethod() &&
                'Allegro Wysyłka z Polski do Słowacji - Odbiór w Punkcie Packeta' !== $mainOrder->getOrderDelivery()->getDeliveryMethod()
            ){
                $labelData['input']['cashOnDelivery']['iban'] = '****************************';
                $labelData['input']['cashOnDelivery']['owner'] = 'Paweł Lisowski';
            }
        }
        if ('PL' !== $order['delivery']['address']['countryCode']) {
            $deliveryMethod = $mainOrder->getOrderDelivery()->getDeliveryMethod();
            $isSpecial = $this->shouldApplyPlnInsurance($deliveryMethod);
            $labelData['input']['insurance'] = $this->getInsuranceData(
                $order['summary']['totalToPay']['amount'],
                $order['summary']['totalToPay']['currency'],
                $isSpecial
            );
        }

        return $this->allegroApiHandler->post(self::RESOURCE, 'shipments/create-commands', $integration, ['Content-Type' => 'application/vnd.allegro.public.v1+json','accept' => 'application/vnd.allegro.public.v1+json'] ,$labelData);
    }

    public function getShipmentLabel($shipmentId, $integration): array | string {
        $labelData = [
            'shipmentIds' => [
                $shipmentId['shipmentId']
            ],
        ];
       return $this->allegroApiHandler->post(self::RESOURCE, 'label', $integration,  ['accept' => 'application/octet-stream', 'Content-Type' => 'application/vnd.allegro.public.v1+json'], $labelData);
    }

    public function getShipmentId($orderId, $integration): array {
        return $this->allegroApiHandler->get(self::RESOURCE, 'shipments/create-commands/' . $orderId,  $integration,  ['accept' => 'application/vnd.allegro.public.v1+json']);
    }

    public function getCredentialsId($id, $integration): array {
        $credentialsId = NULL;
        $deliveryOptions = $this->getAvailableDeliveryMethods($integration);
        if (isset($deliveryOptions['services'])) {
            foreach ($deliveryOptions['services'] as $service) {
                if (isset($service['id']['deliveryMethodId']) && $service['id']['deliveryMethodId'] === $id) {
                    if (isset($service['id']['credentialsId'])) {
                        $credentialsId = $service['id']['credentialsId'];
                    }
                    break;
                }
            }
        }
        return ['credentialsId' => $credentialsId, 'deliveryMethodId' => $id];
    }

    private function shouldApplyPlnInsurance($deliveryMethod): bool {
        $specialDeliveryMethods = [
            'Allegro Kurier DHL Słowacja pobranie',
            'Allegro Kurier DHL Słowacja',
            'Allegro Kurier DHL Czechy pobranie',
            'Allegro Kurier DHL Czechy',
        ];

        return in_array($deliveryMethod, $specialDeliveryMethods, true);
    }

    private function getInsuranceData($amount, $currency, $isSpecial): array
    {
        if ($isSpecial) {
            $multiplier = $this->getCurrencyMultiplierToPln($currency);
            return [
                'amount' => $this->formatAmount($amount * $multiplier),
                'currency' => 'PLN'
            ];
        }

        return [
            'amount' => $this->formatAmount($amount),
            'currency' => $currency
        ];
    }

    private function getCurrencyMultiplierToPln(string $currency): float
    {
        $multipliers = [
            'CZK' => 0.25,
            'EUR' => 5,
        ];

        return $multipliers[$currency] ?? 1.0;
    }

    private function formatAmount($amount): string
    {
        return number_format((float)$amount, 2, '.', '');
    }

    private function trimString($string, $maxLength = 29): string
    {
        if (NULL === $string) {
            return '';
        }

        if (strlen($string) > $maxLength) {
            return substr($string, 0, $maxLength - 3);
        }
        return $string;
    }

}