<?php

namespace App\Core\Service\Allegro\Orders\Order;

use App\Core\Entity\Integration;
use App\Core\Service\Allegro\AllegroApiHandler;

class AllegroOrdersOrderService implements AllegroOrdersOrderInterface
{
    CONST RESOURCE = 'order';
    public function __construct(
        private readonly AllegroApiHandler $allegroApiHandler,
    ) {}

    public function getOrder($orderId, Integration $integration): array {
        $endpoint = "checkout-forms/{$orderId}";
        return $this->allegroApiHandler->get(self::RESOURCE,$endpoint,$integration,['Accept' => 'application/vnd.allegro.public.v1+json']);
    }
}