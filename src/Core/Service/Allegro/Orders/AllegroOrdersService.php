<?php

namespace App\Core\Service\Allegro\Orders;

use App\Core\Service\Allegro\Orders\Order\AllegroOrdersOrderInterface;
use App\Core\Service\Allegro\Orders\Shipments\AllegroOrdersShipmentsInterface;
use App\Core\Service\Allegro\Orders\AllegroOrdersInterface;


class AllegroOrdersService implements AllegroOrdersInterface
{

    public function __construct(
        private readonly AllegroOrdersShipmentsInterface $allegroOrdersShipmentsInterface,
        private readonly AllegroOrdersOrderInterface     $allegroOrderInterface,
    ) {}

    public function order(): AllegroOrdersOrderInterface {
        return $this->allegroOrderInterface;
    }

    public function shipment(): AllegroOrdersShipmentsInterface {
        return $this->allegroOrdersShipmentsInterface;
    }
}