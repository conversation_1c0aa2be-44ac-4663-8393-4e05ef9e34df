<?php

namespace App\Core\Service\Allegro\Auth;

use App\Core\Service\Allegro\Model\AllegroDeviceTokenModel;
use App\Core\Service\Allegro\Model\AllegroAccessTokenModel;
use App\Core\Entity\Integration;
use App\Core\Service\Settings;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use DateTime;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class AllegroAuthService implements AllegroAuthInterface
{
    private string $clientId;
    private string $clientSecret;
    private ?string $baseRegisterUrl;
    private ?Integration $integration = null;

    public function __construct(
        private readonly HttpClientInterface $client,
        private readonly SerializerInterface $serializer,
        private readonly Settings            $settings, private readonly EntityManagerInterface $entityManager
    ) {
        $this->baseRegisterUrl = $this->settings->get("ALLEGRO_URL_TOKEN");
    }

    public function setIntegration($integration): void {
        $this->integration = $integration;
        $this->clientId = $integration->getSettingsByKey('ALLEGRO_CLIENT_ID');
        $this->clientSecret = $integration->getSettingsByKey('ALLEGRO_SECRET_ID');
    }

    public function getDeviceCode(): ?AllegroDeviceTokenModel {
        if (!$this->integration) {
            throw new \Exception("Integration is not set.");
        }
        $response = $this->client->request('POST', $this->baseRegisterUrl . '/device', [
            'headers' => [
                'Authorization' => 'Basic ' . base64_encode($this->clientId . ':' . $this->clientSecret),
                'Content-Type' => 'application/x-www-form-urlencoded'
            ],
            'query' => [
                'client_id' => $this->clientId
            ]
        ]);
        if (NULL !== ($responseContent = $response->getContent())) {
            $token = $this->serializer->deserialize($responseContent, AllegroDeviceTokenModel::class, 'json');
            $token->setCreatedAt();
            $this->integration->addOrUpdateSetting('DEVICE_TOKEN', $token->__toString());
            $this->entityManager->persist($this->integration);
            $this->entityManager->flush();
            return $token;
        }
        return NULL;
    }

    public function getAccessToken(): ?AllegroAccessTokenModel {
        if (!$this->integration) {
            throw new \Exception("Integration is not set.");
        }
        $deviceToken = $this->integration->getSettingsByKey('DEVICE_TOKEN');
        $allegroDeviceToken = $this->serializer->deserialize($deviceToken, AllegroDeviceTokenModel::class, 'json');
        $response = $this->client->request('POST', $this->baseRegisterUrl . '/token', [
            'headers' => [
                'Authorization' => 'Basic ' . base64_encode($this->clientId . ':' . $this->clientSecret),
                'Content-Type' => 'application/x-www-form-urlencoded'
            ],
            'query' => [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:device_code',
                'device_code' => $allegroDeviceToken->getDeviceCode()
            ]
        ]);

        if (NULL !== ($responseContent = $response->getContent())) {
            $token = $this->serializer->deserialize($responseContent, AllegroAccessTokenModel::class, 'json');
            $token->setCreatedAt();
            $this->integration->addOrUpdateSetting('ACCESS_TOKEN', $token->__toString());
            $this->entityManager->persist($this->integration);
            $this->entityManager->flush();
            return $token;
        }
        return NULL;
    }

    public function getExpiryDeviceCode(): DateTime {
        if (!$this->integration) {
            throw new \Exception("Integration is not set.");
        }
        $token = $this->integration->getSettingsByKey('DEVICE_TOKEN');
        $token = $this->serializer->deserialize($token, AllegroDeviceTokenModel::class, 'json');

        return $token->getExpiry();
    }

    public function getIsExpiredDeviceCode(): bool {
        if (!$this->integration) {
            throw new \Exception("Integration is not set.");
        }
        $token = $this->integration->getSettingsByKey('DEVICE_TOKEN');
        $token = $this->serializer->deserialize($token, AllegroDeviceTokenModel::class, 'json');

        return $token->isExpired();
    }

    public function getExpiryAccessToken(): DateTime {
        if (!$this->integration) {
            throw new \Exception("Integration is not set.");
        }
        $token = $this->integration->getSettingsByKey('ACCESS_TOKEN');
        $token = $this->serializer->deserialize($token, AllegroAccessTokenModel::class, 'json');

        return $token->getExpiry();
    }

    public function getIsExpiredAccessToken(): bool {
        if (!$this->integration) {
            throw new \Exception("Integration is not set.");
        }
        $token = $this->integration->getSettingsByKey('ACCESS_TOKEN');
        $token = $this->serializer->deserialize($token, AllegroAccessTokenModel::class, 'json');

        return $token->isExpired();
    }

    public function refreshToken(): ?AllegroAccessTokenModel {
        if (!$this->integration) {
            throw new \Exception("Integration is not set.");
        }
        $accessToken = $this->integration->getSettingsByKey('ACCESS_TOKEN');
        $allegroAccessToken = $this->serializer->deserialize($accessToken, AllegroAccessTokenModel::class, 'json');
        $response = $this->client->request('POST', $this->baseRegisterUrl  . '/token', [
            'headers' => [
                'Authorization' => 'Basic ' . base64_encode($this->clientId . ':' . $this->clientSecret),
                'Content-Type' => 'application/x-www-form-urlencoded'
            ],
            'query' => [
                'grant_type' => 'refresh_token',
                'refresh_token' => $allegroAccessToken->getRefreshToken()
            ]
        ]);

        if (NULL !== ($responseContent = $response->getContent())) {
            $token = $this->serializer->deserialize($responseContent, AllegroAccessTokenModel::class, 'json');
            $token->setCreatedAt();
            $this->integration->addOrUpdateSetting('ACCESS_TOKEN', $token->__toString());
            $this->entityManager->persist($this->integration);
            $this->entityManager->flush();
            return $token;
        }
        return NULL;
    }
}
