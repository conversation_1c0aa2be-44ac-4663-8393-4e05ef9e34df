<?php

namespace App\Core\Service\Allegro\Auth;

use App\Core\Entity\Integration;
use App\Core\Service\Allegro\Model\AllegroDeviceTokenModel;
use App\Core\Service\Allegro\Model\AllegroAccessTokenModel;
use DateTime;

interface AllegroAuthInterface
{
    public function getDeviceCode(): ?AllegroDeviceTokenModel;

    public function getExpiryDeviceCode(): DateTime;

    public function getIsExpiredDeviceCode(): bool;

    public function getAccessToken(): ?AllegroAccessTokenModel;

    public function getExpiryAccessToken(): DateTime;

    public function getIsExpiredAccessToken(): bool;

    public function refreshToken();

    public function setIntegration(Integration $integration): void;
}
