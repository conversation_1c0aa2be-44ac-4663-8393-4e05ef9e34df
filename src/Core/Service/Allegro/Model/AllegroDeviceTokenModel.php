<?php

namespace App\Core\Service\Allegro\Model;

use App\Core\Taxonomy\DateTaxonomy;
use DateTime;
use DateTimeImmutable;

class AllegroDeviceTokenModel
{
    const NAME = 'allegro_device_token';
    private string $deviceCode;
    private string $expiresIn;
    private string $userCode;
    private int $interval;
    private string $verificationUri;
    private string $verificationUriComplete;
    private DateTime $createdAt;

    public function getDeviceCode(): string {
        return $this->deviceCode;
    }

    public function setDeviceCode(string $deviceCode): void {
        $this->deviceCode = $deviceCode;
    }

    public function getExpiresIn(): string {
        return $this->expiresIn;
    }

    public function setExpiresIn(string $expiresIn): void {
        $this->expiresIn = $expiresIn;
    }

    public function getExpiry(): DateTimeImmutable {
        return new DateTimeImmutable('@' . (time() + $this->expiresIn));
    }

    public function isExpired(): bool {
        return new DateTime() > $this->getCreatedAt();
    }

    public function getUserCode(): string {
        return $this->userCode;
    }

    public function setUserCode(string $userCode): void {
        $this->userCode = $userCode;
    }

    public function getInterval(): int {
        return $this->interval;
    }

    public function setInterval(int $interval): void {
        $this->interval = $interval;
    }

    public function getVerificationUri(): string {
        return $this->verificationUri;
    }

    public function setVerificationUri(string $verificationUri): void {
        $this->verificationUri = $verificationUri;
    }

    public function getVerificationUriComplete(): string {
        return $this->verificationUriComplete;
    }

    public function setVerificationUriComplete(string $verificationUriComplete): void {
        $this->verificationUriComplete = $verificationUriComplete;
    }


    public function setCreatedAt(string $dateTime = NULL): void {
        if (NULL === $dateTime) {
            $this->createdAt = new DateTime();
            return;
        }
        $this->createdAt = new DateTime($dateTime);
    }

    public function getCreatedAt(): DateTime {
        return $this->createdAt;
    }

    public function __toString(): string {
        $values = [
            'device_code' => $this->deviceCode,
            'expires_in' => $this->expiresIn,
            'user_code' => $this->userCode,
            'interval' => $this->interval,
            'verification_uri' => $this->verificationUri,
            'verification_uri_complete' => $this->verificationUriComplete,
            'created_at' => $this->createdAt->format(DateTaxonomy::DATE_FORMAT),
        ];

        return json_encode($values);
    }


}