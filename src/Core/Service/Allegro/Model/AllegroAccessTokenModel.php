<?php

namespace App\Core\Service\Allegro\Model;

use App\Core\Taxonomy\DateTaxonomy;
use DateTime;
use DateTimeImmutable;
use DateTimeInterface;
use Symfony\Component\Validator\Constraints\Date;

class AllegroAccessTokenModel
{
    CONST NAME = 'allegro_access_token';
    private ?string $accessToken;
    private ?string $refreshToken;
    private ?int $expiresIn;
    private ?string $scope;
    private ?string $jti;
    private DateTime $createdAt;

    public function getAccessToken(): ?string {
        return $this->accessToken;
    }

    public function setAccessToken(?string $accessToken): void {
        $this->accessToken = $accessToken;
    }

    public function getRefreshToken(): ?string {
        return $this->refreshToken;
    }

    public function setRefreshToken(?string $refreshToken): void {
        $this->refreshToken = $refreshToken;
    }

    public function getExpiresIn(): ?int {
        return $this->expiresIn;
    }

    public function setExpiresIn(?int $expiresIn): void {
        $this->expiresIn = $expiresIn;
    }

    public function getScope(): ?string {
        return $this->scope;
    }

    public function setScope(?string $scope): void {
        $this->scope = $scope;
    }

    public function getJti(): ?string {
        return $this->jti;
    }

    public function setJti(?string $jti): void {
        $this->jti = $jti;
    }



    public function getExpiry(): DateTime {
        return new DateTime($this->createdAt->format(DateTaxonomy::DATE_FORMAT) . ' + ' . $this->expiresIn . ' seconds');
    }

    public function isExpired(?DateTimeInterface $now = null): bool {
        $now = $now ?: new DateTimeImmutable();
        $then = $this->getExpiry();
        $is = $now > $then;
        return $now > $this->getExpiry();
    }

    public function setCreatedAt(string $dateTime = NULL): void {
        if (NULL === $dateTime) {
            $this->createdAt = new DateTime();
            return;
        }
        $this->createdAt = new DateTime($dateTime);
    }

    public function getCreatedAt(): DateTime {
        return $this->createdAt;
    }

    public function __toString(): string {
        $values = [
            'access_token' => $this->accessToken,
            'refresh_token' => $this->refreshToken,
            'expires_in' => $this->expiresIn,
            'scope' => $this->scope,
            'jti' => $this->jti,
            'created_at' => $this->createdAt->format(DateTaxonomy::DATE_FORMAT),
        ];

        return json_encode($values);
    }
}