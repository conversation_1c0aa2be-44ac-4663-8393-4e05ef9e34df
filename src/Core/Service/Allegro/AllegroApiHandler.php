<?php

namespace App\Core\Service\Allegro;

use App\Core\Entity\Integration;
use App\Core\Entity\IntegrationSettings;
use App\Core\Service\Allegro\Model\AllegroAccessTokenModel;
use App\Core\Service\Settings;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

final class AllegroApiHandler
{
    private ?string $baseUrl;
    public function __construct(
        private HttpClientInterface          $client,
        private readonly SerializerInterface $serializer,
        private readonly Settings            $settings, private readonly EntityManagerInterface $entityManager
    ) {
        $this->client = HttpClient::create();
        $this->baseUrl = $this->settings->get('ALLEGRO_URL_API');
    }

    /**
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws \Exception
     */
    private function makeRequest(string $method, string $resource, string $endpoint, Integration $integration, array $headers = [], array $body = []): array | string
    {
        try {
            $this->entityManager->refresh($integration);
            $accessToken = $this->serializer->deserialize(
                $integration->getSettingsByKey('ACCESS_TOKEN'),
                AllegroAccessTokenModel::class,
                'json'
            )->getAccessToken();

            $headers = array_merge($headers, [
                'Authorization' => 'Bearer ' . $accessToken,
                ]);

            $url = $this->baseUrl . '/' . $resource . '/' . $endpoint;

            $response = $this->client->request($method, $url, [
                'headers' => $headers,
                'json' => $body
            ]);
            $responseCode = $response->getStatusCode();
            $contentType = $response->getHeaders()['content-type'][0] ?? '';

            if ($responseCode === 200) {
                if (stripos($contentType, 'application/octet-stream') !== false) {
                    $binaryContent = $response->getContent();
                    return $binaryContent;
                } else {
                    $responseContent = json_decode($response->getContent(), TRUE);
                    return $responseContent;
                }
            } else {
                // Handle unexpected status codes
                throw new \Exception("Unexpected response code: $responseCode");
            }
        } catch (ClientExceptionInterface $e) {
            $response = $e->getResponse();
            $content = $response->getContent(false);

            throw new \Exception($content);
        }
    }

    public function get(string $resource, string $endpoint,$integration , array $headers = []): array
    {
        return $this->makeRequest('GET', $resource,  $endpoint, $integration, $headers);
    }

    public function post(string $resource, string $endpoint, $integration, array $headers = [], array $body = []): array | string
    {
        return $this->makeRequest('POST', $resource, $endpoint,$integration, $headers, $body);
    }
}