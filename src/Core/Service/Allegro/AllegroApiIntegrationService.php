<?php

namespace App\Core\Service\Allegro;

use App\Core\Service\Allegro\Auth\AllegroAuthInterface;
use App\Core\Service\Allegro\Orders\AllegroOrdersInterface;

class AllegroApiIntegrationService
{
    public function __construct(
        private readonly AllegroOrdersInterface          $allegroOrdersInterface,
        private readonly AllegroAuthInterface            $allegroAuthServiceInterface,
    ) {}

    public function orders(): AllegroOrdersInterface
    {
        return $this->allegroOrdersInterface;
    }
    public function auth(): AllegroAuthInterface
    {
        return $this->allegroAuthServiceInterface;
    }
}