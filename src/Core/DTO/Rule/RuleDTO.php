<?php

namespace App\Core\DTO\Rule;

use App\Core\Validator\Constraints as CoreConstraints;
class RuleDTO {

    public string $name = 'NoName';

    #[CoreConstraints\ValidEventValue]
    public string $event;

    #[CoreConstraints\ValidConditionValue]
    public array $conditions;

    #[CoreConstraints\ValidActionValue]
    public array $actions;

    public int $weight = 0;

    public function getEvent(): string {
        return $this->event;
    }

    public function getConditions(): array {
        return $this->conditions;
    }
    public function getActions(): array {
        return $this->actions;
    }

    public function getName(): string {
        return $this->name;
    }

    public function getWeight(): int {
        return $this->weight;
    }

}