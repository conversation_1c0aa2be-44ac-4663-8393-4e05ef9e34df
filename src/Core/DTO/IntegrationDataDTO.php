<?php

namespace App\Core\DTO;

use App\Core\Entity\IntegrationData;

final class IntegrationDataDTO {
    public string $id;
    public string $name;
    public int $integration;
    public string $fetchStatus;
    public string $cancelStatus;
    public string $orderFetchInterval;
    public string $fetchProducts;
    public string $orderStatusSendPaid;
    public string $orderStatusSynchronize;
    public array $integrationStatusMappings;
    public string $apiUrl;
    public string $apiToken;
    public string $apiSecret;

    public function __construct(IntegrationData $integrationData) {
        $this->id = $integrationData->getId()->toRfc4122();
        $this->name = $integrationData->getIntegration()->getName();
        $this->fetchStatus = $integrationData->getFetchStatus()->getId()->toRfc4122();
        $this->cancelStatus = $integrationData->getCancelStatus()->getId()->toRfc4122();
        $this->orderFetchInterval = $integrationData->getOrderFetchInterval()->value;
        $this->fetchProducts = $integrationData->getFetchProducts()->value;
        $this->orderStatusSendPaid = $integrationData->getOrderStatusSendPaid()->value;
        $this->orderStatusSynchronize = $integrationData->getOrderStatusSynchronize()->value;
        $this->integrationStatusMappings = $integrationData->getIntegrationStatusMappings()->toArray();
        $this->apiUrl = $integrationData->getApiUrl();
        $this->apiToken = $integrationData->getApiToken();
        $this->apiSecret = $integrationData->getApiSecret();
    }
}