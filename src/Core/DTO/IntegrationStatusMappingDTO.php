<?php

namespace App\Core\DTO;

class IntegrationStatusMappingDTO {
    public string $id;
    public string $integrationData;
    public ?string $orderStatus;
    public int $integrationStatusId;
    public string $integrationStatusName;
    public bool $paid;
    public bool $shipped;
    public bool $delivered;

    public function __construct(
        string $id,
        string $integrationData,
        ?string $orderStatus,
        int $integrationStatusId,
        string $integrationStatusName,
        bool $paid,
        bool $shipped,
        bool $delivered
    ) {
        $this->id = $id;
        $this->integrationData = $integrationData;
        $this->orderStatus = $orderStatus;
        $this->integrationStatusId = $integrationStatusId;
        $this->integrationStatusName = $integrationStatusName;
        $this->paid = $paid;
        $this->shipped = $shipped;
        $this->delivered = $delivered;
    }

}