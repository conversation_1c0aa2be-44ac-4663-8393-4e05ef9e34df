<?php

namespace App\Core\EventListener\Basket;

use App\Core\Entity\BasketOrderProduct;
use App\Core\Taxonomy\BasketOrderProductTaxonomy;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsEntityListener;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;

#[AsEntityListener(event: Events::preUpdate, method: 'preUpdate', entity: BasketOrderProduct::class)]
class BasketOrderProductQuantityCompletedChange {

    public function preUpdate(BasketOrderProduct $basketOrderProduct, PreUpdateEventArgs $event): void {
        if ($event->hasChangedField('quantity_completed')) {
            $object = $event->getObject();
            if ($object->getQuantityCompleted() === $object->getQuantityRequired()) {
                $object->setStatusId(BasketOrderProductTaxonomy::STATUS_COMPLETE_COLLECTION);
            } else {
                $object->setStatusId(BasketOrderProductTaxonomy::STATUS_INCOMPLETE_COLLECTION);
            }
        }

        if ($event->hasChangedField('quantity_packed')) {
            $object = $event->getObject();
            if ($object->getQuantityPacked() === $object->getQuantityRequired()) {
                $object->setStatusId(BasketOrderProductTaxonomy::STATUS_COMPLETE_PACKING);
            } else {
                $object->setStatusId(BasketOrderProductTaxonomy::STATUS_INCOMPLETE_PACKING);
            }
        }
    }
}