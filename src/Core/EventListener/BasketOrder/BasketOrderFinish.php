<?php

namespace App\Core\EventListener\BasketOrder;

use App\Core\Entity\OrderStatus;
use App\Core\EventDispatcher\BasketOrderEvent;
use App\Core\Message\CreateInvoiceMessage;
use App\Core\Message\SendShipmentMailMessage;
use App\Core\Service\Carrier\CarrierService;
use App\Core\Service\PrintNode\PrintNodePostService;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\HandlerFailedException;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsEventListener(event: 'order.basket.finish_packing', method: 'onBasketOrderFinish')]
readonly class BasketOrderFinish {
    public function __construct(
        private CarrierService         $carrierService,
        private PrintNodePostService   $printNodePostService,
        private EntityManagerInterface $entityManager,
        private MessageBusInterface    $bus
    ) {
    }

    //@CARRIER - shit happens -  ten event zmieni się na wydruk labela
    public function onBasketOrderFinish(BasketOrderEvent $basketOrderEvent): void {
        $sent = false;
        $data = $basketOrderEvent->getData();
        $order = $data['basketOrder']->getOrder();
        $orderDeliveryShipmentData = $this->carrierService->createShipment($order, $data['settings']);
        sleep(1);

       $this->carrierService->setTrackingIdAndPrintLabels($order, $orderDeliveryShipmentData);

        for ($i = 0; $i < count($orderDeliveryShipmentData); $i++) {
            $this->entityManager->persist($orderDeliveryShipmentData[$i]);
        }
        $this->entityManager->persist($order);
        $this->entityManager->flush();

        try {
            $this->bus->dispatch(new SendShipmentMailMessage($order->getId(), $orderDeliveryShipmentData[0]->getId()));
            if ('Odbiór osobisty' === trim($orderDeliveryShipmentData[0]->getCarrierType())) {
                $order->setInternalStatusId($this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::PERSONAL_PICKUP]));
                $this->entityManager->persist($order);
            } else {
//                $this->bus->dispatch(new CreateInvoiceMessage($order->getId(), $orderDeliveryShipmentData[0]->getId()));
                $sent = true;
            }

        } catch (HandlerFailedException $e) {
        }

        if ($sent && $orderDeliveryShipmentData[0]->isLabel()) {
            $order->setInternalStatusId($this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_SENT]));
            $this->entityManager->persist($order);
        }
        $this->entityManager->flush();
    }

}

