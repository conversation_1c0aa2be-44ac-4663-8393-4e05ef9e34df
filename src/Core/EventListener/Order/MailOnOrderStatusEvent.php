<?php

namespace App\Core\EventListener\Order;

use App\Core\Entity\Order;
use App\Core\Entity\OrderEmailOnStatus;
use App\Core\Message\EmailOnStatusMessage;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsEntityListener;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\OnFlushEventArgs;
use Doctrine\ORM\Event\PostPersistEventArgs;
use Doctrine\ORM\Events;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsEntityListener(event: Events::postPersist, method: 'postPersist', entity: Order::class)]
#[AsDoctrineListener(event: Events::onFlush)]
readonly class MailOnOrderStatusEvent {

    public function __construct(private readonly MessageBusInterface $bus, private readonly EntityManagerInterface $entityManager) {
    }

    public function onFlush(onFlushEventArgs $event): void
    {
        $em = $event->getObjectManager();
        $uow = $em->getUnitOfWork();
        foreach ($uow->getScheduledEntityUpdates() as $entity) {
            if ($entity instanceof Order) {
                $uow->computeChangeSets();
                $changeSet = $uow->getEntityChangeSet($entity);
                if (isset($changeSet['internal_status_id'])) {
                    $newStatus = $changeSet['internal_status_id'][1];
                    if (null === $newStatus) {
                        return;
                    }
                    foreach ($this->entityManager->getRepository(OrderEmailOnStatus::class)->findBy(['status' => $newStatus->getId()]) as $mail) {
                        $message = new EmailOnStatusMessage($entity->getId(), $newStatus->getId(), $mail->getId());
                        $this->bus->dispatch($message);
                    }
                }
            }
        }
    }

    public function postPersist(Order $order, PostPersistEventArgs $event): void {
        if ($order->getInternalStatusId()->getName() === OrderTaxonomy::STATUS_NEW) {

        }
    }
}
