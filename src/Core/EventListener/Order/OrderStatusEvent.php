<?php

namespace App\Core\EventListener\Order;

use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Entity\User;
use App\Core\Rules\RuleTrigger;
use App\Core\Service\Fetcher\Api\PrestashopApiService;
use App\Core\Service\Order\OrderStatusHandler;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsEntityListener;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\OnFlushEventArgs;
use Doctrine\ORM\Event\PostFlushEventArgs;
use Doctrine\ORM\Event\PostPersistEventArgs;
use Doctrine\ORM\Events;
use Symfony\Bundle\SecurityBundle\Security;

#[AsEntityListener(event: Events::postPersist, method: 'postPersist', entity: Order::class)]
#[AsDoctrineListener(event: Events::onFlush)]
#[AsDoctrineListener(event: Events::postFlush)]
class OrderStatusEvent {

    private array $orderEntityStatusChange = [];

    public function __construct(
        private PrestashopApiService       $prestashopApiService,
        private EntityManagerInterface     $entityManager,
        private Security                   $security,
        private OrderStatusHandler $orderStatusHandler,
        private RuleTrigger $ruleTrigger
    ) {}

    public function onFlush(onFlushEventArgs $event): void {
        $em = $event->getObjectManager();
        $uow = $em->getUnitOfWork();
        $insertions = $uow->getScheduledEntityInsertions();
        $updates = $uow->getScheduledEntityUpdates();
        $allEntities = array_merge($insertions, $updates);
        foreach ($allEntities as $entity) {
            if ($entity instanceof Order) {
                $uow->computeChangeSets();
                $changeSet = $uow->getEntityChangeSet($entity);
                if (isset($changeSet['internal_status_id'])) {
                    $this->orderEntityStatusChange[] = $entity;;
                    $integrationEntity = $this->entityManager->getRepository(Integration::class)->find($entity->getOrderSourceId());
                    if('prestashop' === $integrationEntity->getType()) {
                        $this->prestashopApiService->setConnectionData($integrationEntity->getIntegrationData()->getApiUrl(), $integrationEntity->getIntegrationData()->getApiToken());
                    }
                    $oldStatus = $changeSet['internal_status_id'][0];
                    $newStatus = $changeSet['internal_status_id'][1];
                    $user = $this->security->getUser() ?? $this->entityManager->getRepository(User::class)->findOneBy(['username' => 'system']);
                    $log = null;
                    if (NULL === $oldStatus ) {
                        $oldStatus = $this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_NEW]);
                    }
                    if (null === $newStatus || null === $oldStatus) {
                        return;
                    }
                    switch ($newStatus->getName()) {
                        case OrderTaxonomy::STATUS_VERIFIED:
                            $log = $this->orderStatusHandler->handleVerifiedStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
                            break;
                        case OrderTaxonomy::STATUS_NEW:
                            $log = $this->orderStatusHandler->handleStatusNew($entity, $user, $oldStatus, $newStatus, $integrationEntity);
                            break;
//                        case OrderTaxonomy::STATUS_MISSING_TO_COMPLETE:
//                            $log = $this->handleMissingToCompleteStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
//                        case OrderTaxonomy::STATUS_COMPLETING:
//                            $log = $this->handleCompletingStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
//                        case OrderTaxonomy::STATUS_IN_BASKET:
//                            $log = $this->handleInBasketStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
//                        case OrderTaxonomy::STATUS_PACKING:
//                            $log = $this->handlePackingStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
//                        case OrderTaxonomy::STATUS_TO_SEND:
//                            $log = $this->handleToSendStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
                        case OrderTaxonomy::STATUS_SENT:
                            $log = $this->orderStatusHandler->handleSentStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
                            break;
                        case OrderTaxonomy::STATUS_LABEL_ERROR:
                            $log = $this->orderStatusHandler->handleLabelErrorStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
                            break;
                        case OrderTaxonomy::STATUS_INVOICE_ERROR:
                            $log = $this->orderStatusHandler->handleInvoiceErrorStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
                            break;
//                        case OrderTaxonomy::STATUS_EMAIL_ERROR:
//                            $log = $this->handleEmailErrorStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
//                        case OrderTaxonomy::STATUS_CLIENT_CONTACT:
//                            $log = $this->handleClientContactStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
//                        case OrderTaxonomy::INCOMPLETE_TO_CHECK:
//                            $log = $this->handleIncompleteToCheckStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
//                        case OrderTaxonomy::PERSONAL_PICKUP:
//                            $log = $this->handlePersonalPickupStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
                        case OrderTaxonomy::STATUS_CANCELLED;
                            $log = $this->orderStatusHandler->handlePersonalCancelledStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
                            break;
//                        case OrderTaxonomy::STATUS_AFTER_CREATE_ZK;
//                            $log = $this->handleAfterCreateZKStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
//                        case OrderTaxonomy::STATUS_AFTER_SET_CHECK;
//                            $log = $this->handleAfterSetCheckStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
                        case OrderTaxonomy::STATUS_ZK_ERROR;
                            $log = $this->orderStatusHandler->handleZKErrorStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
                            break;
//                        case OrderTaxonomy::STATUS_AFTER_CONTACT_WITH_CLIENT;
//                            $log = $this->handleAfterContactWithClientStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
//                        case OrderTaxonomy::STATUS_REMOVED_FROM_BASKET;
//                            $log = $this->handleRemovedFromBasketStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
                        case OrderTaxonomy::STATUS_UNPAID;
                            $log = $this->orderStatusHandler->handleUnpaidStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
                            break;
                        case OrderTaxonomy::STATUS_UNPAID_3_DAYS;
                            $log = $this->orderStatusHandler->handleUnpaid3DaysStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
                            break;
//                        case OrderTaxonomy::STATUS_COLLECTION_RECEIVED;
//                            $log = $this->handleCollectionReceivedStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
//                            break;
                        default:
                            $log = $this->orderStatusHandler->defaultHandleStatus($entity, $user, $oldStatus, $newStatus, $integrationEntity);
                    }

                    $em->persist($log);
                    $uow->computeChangeSet($em->getClassMetadata(get_class($log)), $log);
                }
            }
        }
    }

    public function postPersist(Order $order, PostPersistEventArgs $event): void {
        $integration = $this->entityManager->getRepository(Integration::class)->find($order->getOrderSourceId());
        if ($order->getInternalStatusId()->getName() === $integration->getIntegrationData()->getFetchStatus()->getName()) {
            $user = $this->entityManager->getRepository(User::class)->findOneBy(['username' => 'system']);
            $this->orderStatusHandler->handleNewStatus($order, $user, $order->getInternalStatusId(), $integration->getIntegrationData()->getFetchStatus());
        }
    }

    public function postFlush(PostFlushEventArgs $event): void {
        if (!empty($this->orderEntityStatusChange)) {
            foreach ($this->orderEntityStatusChange as $key => $order) {
                unset ($this->orderEntityStatusChange[$key]);
                $this->ruleTrigger->triggerEvent('order.status.change', ['order' => $order]);
            }
            $this->orderEntityStatusChange = [];
        }
    }
}
