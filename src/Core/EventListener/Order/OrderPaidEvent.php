<?php

namespace App\Core\EventListener\Order;

use App\Core\Entity\Order;
use App\Core\Message\CreateInvoiceMessage;
use App\Core\Rules\RuleTrigger;
use App\Core\Utility\Money;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\OnFlushEventArgs;
use Doctrine\ORM\Event\PostFlushEventArgs;
use Doctrine\ORM\Events;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsDoctrineListener(event: Events::onFlush)]
#[AsDoctrineListener(event: Events::postFlush)]
class OrderPaidEvent {
    
    private array $ordersToUpdate = [];
    
    private bool $postFlushExecuted = false;
    
    public function __construct(
        private readonly RuleTrigger $ruleTrigger

    ) {}
    
    public function onFlush(OnFlushEventArgs $event): void {
        $em = $event->getObjectManager();
        $uow = $em->getUnitOfWork();
        foreach ($uow->getScheduledEntityUpdates() as $entity) {
            if ($entity instanceof Order) {
                $uow->computeChangeSets();
                $changeSet = $uow->getEntityChangeSet($entity);
                $req1 = isset($changeSet['payment_done']) && ((int) $changeSet['payment_done'][1] === $entity->getFullPriceWithDiscount() + $entity->getOrderDelivery()->getDeliveryPrice());
                $paymentDone = new Money($entity->getPaymentDone());
                $fullPrice = new Money($entity->getFullPriceWithDiscount());
                $deliveryPrice = new Money($entity->getOrderDelivery()->getDeliveryPrice());
                $requiredPrice = $fullPrice->add($deliveryPrice);
                $req2 = $paymentDone->equal($requiredPrice);

                if ($req1 && $req2) {
                    if (!$entity->checkIfFSExists()) {
                        $this->ordersToUpdate[] = $entity->getId();
                    }
                }
            }
        }
    }
    
    public function postFlush(PostFlushEventArgs $event): void {
        if ($this->postFlushExecuted) {
            return;
        }
        $this->postFlushExecuted = true;
        if (empty($this->ordersToUpdate)) {
            return;
        }
        $em = $event->getObjectManager();
        foreach ($this->ordersToUpdate as $orderId) {
            $order = $em->getRepository(Order::class)->find($orderId);
            if ($order->isPaid()) {
                $this->ruleTrigger->triggerEvent('order.is.paid', ['order' => $order]);
            }
        }
        $this->ordersToUpdate = [];
    }
}