<?php

namespace App\Core\EventListener\Order;

use App\Core\Entity\Order;
use App\Core\Entity\OrderDelivery;
use App\Core\Entity\OrderDeliveryShipmentData;
use App\Core\Entity\OrderHistoryChangeReview;
use App\Core\Entity\OrderInvoice;
use App\Core\Entity\User;
use App\Core\Message\CreateInvoiceMessage;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\OnFlushEventArgs;
use Doctrine\ORM\Event\PostFlushEventArgs;
use Doctrine\ORM\Events;
use ReflectionClass;
use Symfony\Bundle\SecurityBundle\Security;

#[AsDoctrineListener(event: Events::onFlush)]
#[AsDoctrineListener(event: Events::postFlush)]
class OrderHistoryReview {

    private array $ordersToUpdate = [];

    public function __construct(private readonly Security $security) {}

    public function onFlush(OnFlushEventArgs $event): void {
        $em = $event->getObjectManager();
        $uow = $em->getUnitOfWork();
        foreach ($uow->getScheduledEntityUpdates() as $entity) {
            if ($entity instanceof Order) {
                $changeSet = $uow->getEntityChangeSet($entity);
                foreach ($changeSet as $field => $value) {
                    if ('internal_status_id' === $field) {
                        $this->ordersToUpdate[$entity->getId()->toRfc4122()][$field][] = [
                            'oldValue' => [
                                'id' => $value[0]->getId()->toRfc4122(),
                                'name' =>$value[0]->getName()],
                            'newValue' => [
                                'id' => $value[1]->getId()->toRfc4122(),
                                'name' =>$value[1]->getName()
                            ]
                        ];
                    } else {
                        $this->ordersToUpdate[$entity->getId()->toRfc4122()][$field][] = ['oldValue' => $value[0], 'newValue' => $value[1]];
                    }
                }
            }
            if ($entity instanceof OrderDelivery || $entity instanceof OrderInvoice) {
                $changeSet = $uow->getEntityChangeSet($entity);
                foreach ($changeSet as $field => $value) {
                    $this->ordersToUpdate[$entity->getOrder()->getId()->toRfc4122()][$field][] = ['oldValue' => $value[0], 'newValue' => $value[1]];
                }
            }
        }
        foreach ($uow->getScheduledEntityInsertions() as $entity) {
            if ($entity instanceof OrderDeliveryShipmentData) {
                $order = $entity->getOrderDeliveryId()->getOrder();
                $this->ordersToUpdate[$order->getId()->toRfc4122()]['create'][] = ['oldValue' => 'Create', 'newValue' => $entity];
            }
        }
    }

    public function postFlush(PostFlushEventArgs $event): void {
        if (empty($this->ordersToUpdate)) {
            return;
        }

        foreach ($this->ordersToUpdate as $orderId => $fields) {
            $order = $event->getObjectManager()->getRepository(Order::class)->find($orderId);
            $history = new OrderHistoryChangeReview();
            $history->setParent($order);
            $history->setData($fields);
            $history->setDateOfChange(new \DateTime('now', new \DateTimeZone('Europe/Warsaw')));
            $history->setUser($this->security->getUser() ?? $event->getObjectManager()->getRepository(User::class)->findOneBy(['username' => 'system']));
            $event->getObjectManager()->persist($history);

        }
        $this->ordersToUpdate = [];
        $event->getObjectManager()->flush();
    }
}