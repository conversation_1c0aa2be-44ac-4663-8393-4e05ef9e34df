<?php

namespace App\Core\EventListener\Exception;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\RateLimiter\RateLimiterFactory;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

#[AsEventListener(event: KernelEvents::EXCEPTION, method: 'onKernelException', priority: -127)]
readonly class KernelExceptionListener {

    public function __construct(
        private UrlGeneratorInterface $urlGenerator,
        private RateLimiterFactory $badRequestLimiter,
    ) {
    }
    public function onKernelException(ExceptionEvent $event): void {

        $ex = $event->getThrowable();
//        if ($ex instanceof NotFoundHttpException) {
//            $ip = $event->getRequest()->getClientIp();
//            $limiter = $this->badRequestLimiter->create('login_error_rate_limiter-' . $ip);
//            $limiter->consume();
//            $url = $this->urlGenerator->generate('web_error_page');
//            $event->setResponse(new RedirectResponse($url));
//
//            return;
//        }
//        $ex = $event->getThrowable();

        $message = json_validate($ex->getMessage()) ? json_decode($ex->getMessage()) : $ex->getMessage();

        $error = [
            'error' => TRUE,
            'message' => $message,
            'exception_class' => get_class($ex),
            'file' => $ex->getFile(),
            'line' => $ex->getLine(),
            'trace' => $ex->getTrace(),
        ];
        $event->setResponse(new JsonResponse($error, Response::HTTP_BAD_REQUEST));
    }
}