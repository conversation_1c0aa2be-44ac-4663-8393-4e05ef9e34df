<?php

namespace App\Core\EventListener\Security;

use App\Core\Entity\UserLoginHistory;
use App\Core\Taxonomy\DateTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Event\LoginSuccessEvent;

#[AsEventListener(event: LoginSuccessEvent::class, method: 'onLoginSuccess')]
final class LoginSuccess {

    public function __construct(private EntityManagerInterface $entityManager) {
    }
    public function onLoginSuccess(LoginSuccessEvent $event): void {
        if ('api' === $event->getFirewallName()) {
            return;
        }

        $loginHistory = new UserLoginHistory();
        try {
            $username = $event->getPassport()->getBadge(UserBadge::class)->getUserIdentifier();
            $loginHistory->setUsername($username);
        } catch (\Exception $e) {
            $loginHistory->setUsername('ERROR');
        }
        $date = new \DateTime('now');
        $date->format(DateTaxonomy::DATE_FORMAT);
        $loginHistory->setDateLogin(new \DateTime('now'));
        $loginHistory->setFirewall($event->getFirewallName());
        $loginHistory->setIpAddress($event->getRequest()->getClientIp());
        $loginHistory->setResult('success');
        $loginHistory->setPassword('');
        $loginHistory->setUserAgent($event->getRequest()->headers->get('User-Agent'));
        $this->entityManager->persist($loginHistory);
        $this->entityManager->flush();
    }
}