<?php

namespace App\Core\EventListener\Security;

use App\Core\Entity\UserLoginHistory;
use App\Core\Taxonomy\DateTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\PasswordCredentials;
use Symfony\Component\Security\Http\Event\LoginFailureEvent;

#[AsEventListener(event: LoginFailureEvent::class, method: 'onLoginFailure')]
final class LoginFailed {

    public function __construct(private readonly EntityManagerInterface $entityManager) {
    }

    public function onLoginFailure(LoginFailureEvent $event): void {
        $loginHistory = new UserLoginHistory();
        try {
            $loginHistory->setUsername($event->getPassport()?->getBadge(UserBadge::class)?->getUserIdentifier() ?? 'ERROR');
        } catch (\Exception $e) {
            $loginHistory->setUsername('ERROR');
        }

        $date = new \DateTime('now');
        $date->format(DateTaxonomy::DATE_FORMAT);
        $loginHistory->setDateLogin(new \DateTime('now'));
        $loginHistory->setFirewall($event->getFirewallName());
        $loginHistory->setIpAddress($event->getRequest()->getClientIp());
        $loginHistory->setResult('failure');
        $loginHistory->setPassword($event->getPassport()?->getBadge(PasswordCredentials::class)?->getPassword() ?? 'ERROR');
        $loginHistory->setUserAgent($event->getRequest()?->headers->get('User-Agent'));
        $this->entityManager->persist($loginHistory);
        $this->entityManager->flush();
    }
}