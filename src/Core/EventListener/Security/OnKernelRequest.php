<?php

namespace App\Core\EventListener\Security;

use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\RateLimiter\RateLimiterFactory;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

#[AsEventListener(event: 'kernel.request', method: 'onKernelRequest', priority: 10000)]
readonly class OnKernelRequest {
    public function __construct(private RateLimiterFactory $badRequestLimiter, private UrlGeneratorInterface $generator) {}

    public function onKernelRequest(RequestEvent $event): void {

//        $ip = $event->getRequest()->getClientIp();
//        $limiter = $this->badRequestLimiter->create('login_error_rate_limiter-' . $ip);
//        $limit = $limiter->consume(0);
//        if ($limit->getRemainingTokens() === 0) {
//            $pathInfo = $event->getRequest()->getPathInfo();
//            if ('/web/error' === $pathInfo) {
//                return;
//            } else {
//                $url = $this->generator->generate('web_error_page');
//                $event->setResponse(new RedirectResponse($url));
//            }
//
//        }
    }
}