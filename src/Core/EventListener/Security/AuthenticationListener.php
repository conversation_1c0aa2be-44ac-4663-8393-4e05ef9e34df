<?php

namespace App\Core\EventListener\Security;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

#[AsEventListener(event: KernelEvents::REQUEST, method: 'onKernelRequest', priority: 33)]
class AuthenticationListener
{
    public function __construct(
        private Security $security,
        private UrlGeneratorInterface $urlGenerator
    ) {}

    public function onKernelRequest(RequestEvent $event): void
    {
//        if (!$event->isMainRequest()) {
//            return;
//        }
//        $request = $event->getRequest();
//        $route = $request->attributes->get('_route');
//
//        // Skip authentication check for login/logout routes and dev routes
//        if (in_array($route, ['app_login', 'app_logout', 'app_homepage']) ||
//            str_starts_with($request->getPathInfo(), '/_')) {
//            return;
//        }
//
//
//        // If user is not authenticated, redirect to login
//        if (!$this->security->getUser()) {
//            $loginUrl = $this->urlGenerator->generate('app_login');
//            $event->setResponse(new RedirectResponse($loginUrl));
//        }
    }
}
