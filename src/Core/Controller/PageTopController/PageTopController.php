<?php

namespace App\Core\Controller\PageTopController;

use App\Core\Template\EventSystem\Event\EventStorage;
use App\Core\Template\EventSystem\Event\PageTopEvent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;

class PageTopController extends AbstractController {

    public function __construct(private readonly EventDispatcherInterface $eventDispatcher) {}

    public function renderPageTop(): Response {
        $pageHeaderEvent = new PageTopEvent();
        $this->eventDispatcher->dispatch($pageHeaderEvent, EventStorage::PAGE_TOP);

        return $this->render('pagetop.html.twig', $pageHeaderEvent->getContent());
    }
}
