<?php

namespace App\Core\Controller\Stylesheet;

use App\Core\Template\EventSystem\Event\EventStorage;
use App\Core\Template\EventSystem\Event\StylesheetEvent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;

class StylesheetController extends AbstractController {

    public function __construct(private readonly EventDispatcherInterface $eventDispatcher) {}

    public function stylesheet(): Response {
        $stylesheetEvent = new StylesheetEvent();
        $this->eventDispatcher->dispatch($stylesheetEvent, EventStorage::PAGE_STYLESHEET);

        return $this->render('stylesheet.html.twig', ['stylesheets' => $stylesheetEvent->getContent()]);
    }
}