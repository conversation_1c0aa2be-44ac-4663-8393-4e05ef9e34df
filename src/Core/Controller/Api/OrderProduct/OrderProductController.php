<?php

namespace App\Core\Controller\Api\OrderProduct;

use App\Core\Entity\BasketOrderProduct;
use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Entity\OrderProduct;
use App\Core\Service\Fetcher\Order\PrestashopOrderFetcherService;
use App\Core\Service\Fetcher\Product\PrestashopProductFetcherService;
use App\Core\Service\OrderStatusService;
use App\Core\Service\ProductSetResolver;
use App\Core\Service\View\Order\OrderProductImageService;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

#[Route(path: '/api/products')]
class OrderProductController extends AbstractController {

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly ProductSetResolver $productSetResolver,
        private readonly OrderStatusService $orderStatusService,
        private readonly OrderProductImageService $orderProductImageService,
        private readonly DenormalizerInterface $denormalizer
    ){}

    #[Route(path: '/set/resolve')]
    public function productSetResolver(): JsonResponse {
        $orders = $this->entityManager->getRepository(Order::class)->findBy(['internal_status_id' => $this->orderStatusService->getStatus(OrderTaxonomy::STATUS_NEW)]);
        foreach ($orders as $order) {
            $products = $order->getProducts();
            foreach ($products as $product) {
                $this->productSetResolver->checkIfAProductIsASet($product);
            }
        }

        return $this->json('success', Response::HTTP_OK);
    }

    #[Route(path: '/{id}/coverImageSku')]
    public function getCoverImageSku(OrderProduct $orderProduct): JsonResponse {
        return $this->json($this->orderProductImageService->getCoverImage($orderProduct), Response::HTTP_OK);
    }

    #[Route(path: '/{id}/editProduct', methods: ['POST'])]
    public function editProduct(OrderProduct $orderProduct, Request $request): JsonResponse
    {
        $basketOrderProduct = $this->entityManager->getRepository(BasketOrderProduct::class)->findOneBy(['order_product_id' => $orderProduct]);
        $requestData = $request->toArray();
        if ($basketOrderProduct && isset($requestData['quantity']) && $requestData['quantity'] !== $orderProduct->getQuantity()) {
            $basketOrderProduct->setQuantityRequired($requestData['quantity']);
            $this->entityManager->persist($basketOrderProduct);
        }
        $prodEnt = $this->denormalizer->denormalize(
            $requestData,
            OrderProduct::class,
            null,
            [
                AbstractNormalizer::OBJECT_TO_POPULATE => $orderProduct,
            ]);

        $this->entityManager->persist($prodEnt);
        $this->entityManager->flush();
        $this->recountOrderPrice($prodEnt);

        return $this->json(['product' => $prodEnt]);
    }

    #[Route(path: '/{id}/removeProduct', methods: ['POST'])]
    public function removeProduct(OrderProduct $orderProduct): JsonResponse
    {
        $this->entityManager->remove($orderProduct);
        $this->entityManager->flush();
        $this->recountOrderPrice($orderProduct);

        return $this->json(['success' => TRUE], Response::HTTP_OK);
    }

    private function recountOrderPrice(OrderProduct $orderProduct): void {
        $orderEntity = $orderProduct->getOrderId();
        $orderEntity->setFullPrice($orderEntity->getFullPriceCountByProducts() + $orderEntity->getOrderDelivery()->getDeliveryPrice());
        $this->entityManager->persist($orderEntity);
        $this->entityManager->flush();
    }

    #[Route(path: '/getWeight')]
    public function getWeight(PrestashopProductFetcherService $prestashopProductFetcherService) {
        $weighed = [];
        $integration = $this->entityManager->getRepository(Integration::class)->find(1);
        foreach ($this->entityManager->getRepository(OrderProduct::class)->findBy(['weight' => 0]) as $product) {
            if (isset($weighed[$product->getOrderProductId()])) {
                $product->setWeight($weighed[$product->getOrderProductId()]);
            } else {
                $data = $prestashopProductFetcherService->getProductById($product->getProductId(), $integration->getIntegrationData()->getApiUrl(), $integration->getIntegrationData()->getApiToken());
                $product->setWeight((int) (round(floatval($data['data']['weight'] * 100))));
                $weighed[$product->getOrderProductId()] = $product->getWeight();
            }
            $this->entityManager->persist($product);
        }

        $this->entityManager->flush();

        return $this->json($weighed, Response::HTTP_OK);
    }
}