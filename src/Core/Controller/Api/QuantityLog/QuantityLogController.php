<?php

namespace App\Core\Controller\Api\QuantityLog;

use App\Core\Entity\Order;
use App\Core\Entity\QuantityLog;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/quantitylog')]
class QuantityLogController extends AbstractController {

    public function __construct(private readonly EntityManagerInterface $entityManager) {}

    #[Route('/{id}/view')]
    public function getQuantityLog(Order $order) {
        $returnArray = [];
        foreach ($this->entityManager->getRepository(QuantityLog::class)->findBy(['id_order' => $order]) as $ql) {
            $returnArray[] = [
                'shelf' => $ql->getIdEsq()->getShelf()->getShelfNo(),
                'ean' => $ql->getIdEsq()->getEan()->getEan(),
                'quantity_available' => $ql->getQuantityAvailable(),
                'quantity_required' => $ql->getQuantityRequired(),
                'product' => $ql->getIdProduct()->getName() . '(SKU: ' . $ql->getIdProduct()->getSku() . ')',
                'date_created' => $ql->getDateChecked()->format('Y-m-d H:i:s'),
            ];
        }

        return $this->json($returnArray, Response::HTTP_OK);
    }

}