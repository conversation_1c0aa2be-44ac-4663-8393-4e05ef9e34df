<?php

namespace App\Core\Controller\Api\Communicator;

use App\Core\Service\Communicator\CommunicatorService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/communicator')]
class CommunicatorController extends AbstractController
{
    public function __construct(
        private readonly CommunicatorService $communicatorService
    ) {
    }

    #[Route('/slack', methods: ['POST'])]
    public function sendSlackMessage(Request $request): JsonResponse {
        $data = $request->toArray();
        $channel = $data['channel'] ?? null;
        $text = $data['text'] ?? null;

        if(!$channel || !$text){
            return $this->json(['message' => 'Channel and text are required'], 400);
        }

        $message = $this->communicatorService->sendSlackMessage($channel, $text);
        return $this->json(['message' => $message], $message['code']);
    }

    #[Route('/telegram', methods: ['POST'])]
    public function sendTelegramMessage(Request $request): JsonResponse {
        $data = $request->toArray();
        $channel = $data['channel'] ?? null;
        $text = $data['text'] ?? null;
        if(!$channel || !$text){
            return $this->json(['message' => 'ChatId and text are required'], 400);
        }
        $message = $this->communicatorService->sendTelegramMessage($channel, $text);
        return $this->json(['message' => $message], $message['code']);
    }
}