<?php

namespace App\Core\Controller\Api\EmailEvent;

use App\Core\Entity\EmailAccount;
use App\Core\Entity\EmailTemplate;
use App\Core\Entity\OrderEmailOnStatus;
use App\Core\Entity\OrderStatus;
use App\Core\Service\EmailEvent\EmailEventService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Uid\Uuid;

#[Route('/api/email-event')]
class EmailEventController extends AbstractController
{

    public function __construct(private readonly EmailEventService $emailEventService) {}

    #[Route('/create', methods: ['POST'])]
    public function create(Request $request): JsonResponse {
        return $this->json(['message' => 'Mail template created successfully', 'status' => Response::HTTP_OK, 'data' => $this->emailEventService->createMailTemplate($request->toArray())]);
    }

    #[Route('/{id}/edit', methods: ['POST'])]
    public function edit(EmailTemplate $template, Request $request): JsonResponse {
        return $this->json(['message' => 'Mail template edited successfully', 'status' => Response::HTTP_OK, 'data' => $this->emailEventService->editMailTemplate($template, $request->toArray())]);
    }

    #[Route('/{id}/delete', methods: ['POST'])]
    public function delete(EmailTemplate $template): void {
        $this->emailEventService->deleteMailTemplate($template);
        $this->json(['message' => 'Mail template deleted successfully', 'status' => Response::HTTP_OK, 'data' => []]);
    }

    #[Route('/findAll', methods: ['GET'])]
    public function findAll(): JsonResponse {
        return $this->json(['message' => '', 'status' => Response::HTTP_OK, 'data' => $this->emailEventService->findAllMailTemplate()]);
    }

    #[Route('/bind-mail-to-status', methods: ['POST'])]
    public function bindMailToStatusCreate(Request $request, EntityManagerInterface $entityManager, MessageBusInterface $bus): JsonResponse {
        $data = $request->toArray();
        if (isset($data['templateId']) && Uuid::isValid($data['templateId'])) {
            $template = $entityManager->getRepository(EmailTemplate::class)->find($data['templateId']);
        }
        if (isset($data['orderStatusId']) && Uuid::isValid($data['orderStatusId'])) {
            $status = $entityManager->getRepository(OrderStatus::class)->find($data['orderStatusId']);
        }
        if (isset($data['emailFromId']) && Uuid::isValid($data['emailFromId'])) {
            $emailAccount = $entityManager->getRepository(EmailAccount::class)->find($data['emailFromId']);
        }
        if ((isset($template) && $template instanceof EmailTemplate) && isset($status) && $status instanceof OrderStatus) {
            return $this->json(['message' => 'Mail template bound to status successfully', 'status' => Response::HTTP_OK, 'data' => $this->emailEventService->bindMailToTemplate($template, $status, $emailAccount ?? NULL)]);
        }

        return $this->json(['message' => 'Mail template bound to status failed', 'status' => Response::HTTP_OK, 'data' => []]);
    }

    #[Route('/bind-mail-to-status/list', methods: ['GET'])]
    public function bindMailToStatusList(EntityManagerInterface $entityManager): JsonResponse {
        return $this->json(['message' => '', 'status' => Response::HTTP_OK, 'data' => [$this->emailEventService->findAllBindings()]]);
    }

    #[Route('/bind-mail-to-status/{id}/edit', methods: ['POST'])]
    public function editOrderEmailOnStatus(OrderEmailOnStatus $oeos, Request $request): JsonResponse {
        return $this->json(['message' => 'Mail template edited successfully', 'status' => Response::HTTP_OK, 'data' => $this->emailEventService->editOrderEmailOnStatus($oeos, $request->toArray())]);
    }

    #[Route('/bind-mail-to-status/{id}/delete', methods: ['POST'])]
    public function deleteOrderEmailOnStatus(OrderEmailOnStatus $oeos): void {
        $this->emailEventService->deleteOrderEmailOnStatus($oeos);
        $this->json(['message' => 'Mail template deleted successfully', 'status' => Response::HTTP_OK, 'data' => []]);
    }

    #[Route('/get-available-tags', methods: ['GET'])]
    public function getAvailableTags()
    {
        return $this->json($this->emailEventService->getAvailableTags());
    }

}