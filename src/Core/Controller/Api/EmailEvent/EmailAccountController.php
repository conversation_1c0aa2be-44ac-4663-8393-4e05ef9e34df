<?php

namespace App\Core\Controller\Api\EmailEvent;
use App\Core\Entity\EmailAccount;
use App\Core\Entity\EmailTemplate;
use App\Core\Service\EmailEvent\EmailAccountService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/email-account')]
class EmailAccountController extends AbstractController
{

    public function __construct(private readonly EmailAccountService $emailAccountService) {}

    #[Route('/create', methods: ['POST'])]
    public function create(Request $request, EntityManagerInterface $entityManager): JsonResponse {
        return $this->json(['message' => 'Email Account created successfully', 'status' => Response::HTTP_OK, 'data' => $this->emailAccountService->createEmailAccount($request->toArray())]);
    }

    #[Route('/{id}/edit', methods: ['POST'])]
    public function edit(EmailAccount $emailAccount, Request $request): JsonResponse {
        return $this->json(['message' => 'Email Account edited successfully', 'status' => Response::HTTP_OK, 'data' => $this->emailAccountService->editEmailAccount($emailAccount, $request->toArray())]);
    }

    #[Route('/{id}/delete', methods: ['POST'])]
    public function delete(EmailTemplate $emailAccount): void {
        $this->json(['message' => 'Email Account deleted successfully', 'status' => Response::HTTP_OK, 'data' => []]);
    }

    #[Route('/findAll', methods: ['GET'])]
    public function findAll(): JsonResponse {
        return $this->json(['message' => '', 'status' => Response::HTTP_OK, 'data' => $this->emailAccountService->findAllEmailAccounts()]);
    }
}