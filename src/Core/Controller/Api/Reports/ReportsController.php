<?php

namespace App\Core\Controller\Api\Reports;

use App\Core\Service\Reports\ReportsService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/reports')]
class ReportsController extends AbstractController {

    public function __construct(private readonly ReportsService $reportsService) {
    }

    #[Route('/getPackedIn24', methods: ['POST'])]
    public function getPackedIn24(Request $request): JsonResponse
    {
        $data = $request->toArray();

        if (!isset($data['dateFrom'], $data['dateTo'])) {
            return $this->json(['error' => 'Invalid Parameters: missing dateFrom or dateTo'], 400);
        }

        return $this->json($this->reportsService->getPackedIn24($data['dateFrom'], $data['dateTo']));
    }

}