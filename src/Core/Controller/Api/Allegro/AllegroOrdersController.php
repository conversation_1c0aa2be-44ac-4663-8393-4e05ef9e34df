<?php

namespace App\Core\Controller\Api\Allegro;

use App\Core\Entity\Integration;
use App\Core\Service\Allegro\AllegroApiIntegrationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/allegro/orders')]
class AllegroOrdersController extends AbstractController
{
    public function __construct(
        private readonly AllegroApiIntegrationService $allegroApiIntegrationService,
    ) {}

    #[Route('/shipment/delivery-methods/{id}', name: 'allegro_orders_delivery_methods')]
    public function getAvailableDeliveryMethods(Integration $integration): JsonResponse {
        return $this->json($this->allegroApiIntegrationService->orders()->shipment()->getAvailableDeliveryMethods($integration));
    }

    #[Route('/order/{orderId}', name: 'allegro_orders_order_detail')]
    public function getOrder($orderId): JsonResponse {
        return $this->json($this->allegroApiIntegrationService->orders()->order()->getOrder($orderId));
    }

    #[Route('/shipment/{orderId}/create-shipment', name: 'allegro_orders_create_label')]
    public function createLabel($orderId): JsonResponse {
        return $this->json($this->allegroApiIntegrationService->orders()->shipment()->createShipment($orderId));
    }

    #[Route('/shipment/{orderId}/get-label', name: 'allegro_orders_get_label')]
    public function getLabel($shipmentId): JsonResponse {
        return $this->json($this->allegroApiIntegrationService->orders()->shipment()->getShipmentLabel($shipmentId));
    }
    #[Route('/shipment/{orderId}/get-shipment-id', name: 'allegro_orders_get_shipment-id')]
    public function getShipmentId($orderId): JsonResponse {
        return $this->json($this->allegroApiIntegrationService->orders()->shipment()->getShipmentId($orderId));
    }
}