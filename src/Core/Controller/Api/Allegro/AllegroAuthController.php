<?php

namespace App\Core\Controller\Api\Allegro;
use App\Core\Entity\Integration;
use App\Core\Service\Allegro\AllegroApiIntegrationService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\JsonResponse;

#[Route('/api/allegro')]
class AllegroAuthController extends AbstractController
{
    public function __construct(
        private readonly AllegroApiIntegrationService $allegroApiIntegrationService, private readonly EntityManagerInterface $entityManager,
    ){}

    #[Route('/auth/device-code/{id}', name: 'allegro_auth_device_code')]
    public function getDeviceCode(Integration $integration): JsonResponse
    {
        $this->allegroApiIntegrationService->auth()->setIntegration($integration);
        $deviceCode = $this->allegroApiIntegrationService->auth()->getDeviceCode();
        return $this->json($deviceCode);
    }

    #[Route('/auth/device-code-expire/{id}', name: 'allegro_auth_device_code_expire')]
    public function getExpireDeviceCode(Integration $integration): JsonResponse
    {
        $this->allegroApiIntegrationService->auth()->setIntegration($integration);
        $deviceCode = $this->allegroApiIntegrationService->auth()->getExpiryDeviceCode();
        return $this->json($deviceCode);
    }

    #[Route('/auth/device-code-is-expired/{id}', name: 'allegro_auth_device_code_is_expired')]
    public function getIsExpiredDeviceCode(Integration $integration): JsonResponse
    {
        $this->allegroApiIntegrationService->auth()->setIntegration($integration);
        return $this->json($this->allegroApiIntegrationService->auth()->getIsExpiredDeviceCode());
    }

    #[Route('/auth/access-token/{id}', name: 'allegro_auth_access_token', methods: ['GET'])]
    public function getAccessToken(Integration $integration): JsonResponse
    {
        $this->allegroApiIntegrationService->auth()->setIntegration($integration);
        $token = $this->allegroApiIntegrationService->auth()->getAccessToken();
        return $this->json($token);
    }
    #[Route('/auth/access-token-expire/{id}', name: 'allegro_auth_access_token_expire')]
    public function getExpireAccessToken(Integration $integration): JsonResponse
    {
        $this->allegroApiIntegrationService->auth()->setIntegration($integration);
        $deviceCode = $this->allegroApiIntegrationService->auth()->getExpiryAccessToken();
        return $this->json($deviceCode);
    }

    #[Route('/auth/access-token-is-expired/{id}', name: 'allegro_auth_access_token_is_expired')]
    public function getIsExpiredAccessToken(Integration $integration): JsonResponse
    {
        $this->allegroApiIntegrationService->auth()->setIntegration($integration);
        return $this->json($this->allegroApiIntegrationService->auth()->getIsExpiredAccessToken());
    }

    #[Route('/auth/refresh-token/{id}', name: 'allegro_auth_refresh-token')]
    public function refreshToken(Integration $integration): JsonResponse
    {
        $this->allegroApiIntegrationService->auth()->setIntegration($integration);
        return $this->json($this->allegroApiIntegrationService->auth()->refreshToken());
    }

    #[Route('/auth/get-integrations/{id}', name: 'allegro_auth_get-integrations')]
    public function getIntegrations(): JsonResponse
    {
        $integrations = $this->entityManager->getRepository(Integration::class)->findBaselinkerWithAllegroSettings();
        $result = [];
        foreach ($integrations as $integration) {

            $this->entityManager->refresh($integration);
            $this->allegroApiIntegrationService->auth()->setIntegration($integration);
            $result[] = $this->allegroApiIntegrationService->auth()->getIsExpiredAccessToken();
        }

        return $this->json($result);
    }
}