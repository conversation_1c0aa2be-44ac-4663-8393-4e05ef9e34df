<?php

namespace App\Core\Controller\Api\BasketOrderProduct;

use App\Core\Entity\BasketOrderProduct;
use App\Core\Service\Basket\BasketOrderService;
use App\Core\Service\OrderStatusService;
use App\Core\Taxonomy\BasketOrderTaxonomy;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;

#[Route(path: '/api/basket/product')]
class BasketOrderProductController extends AbstractController {

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly BasketOrderService $basketOrderService,
        private readonly OrderStatusService $orderStatusService
    ) {}

    #[Route(path: '/{id}/addQuantityCompleted')]
    public function addQuantityCompleted(BasketOrderProduct $basketOrderProduct, Request $request): JsonResponse {
        $requestArray = $request->toArray();
        if (!isset($requestArray['quantityCompleted'])) {
            throw new \Exception('No quantity');
        }
        if ($basketOrderProduct->getQuantityCompleted() + (int) $requestArray['quantityCompleted'] > $basketOrderProduct->getQuantityRequired()) {
            throw new \Exception('Too many quantity');
        }
        $basketOrderProduct->setQuantityCompleted($basketOrderProduct->getQuantityCompleted() + (int) $requestArray['quantityCompleted']);
        $this->entityManager->persist($basketOrderProduct);
        $this->entityManager->flush();

        $this->basketOrderService->allowChangeStatus($basketOrderProduct->getBasketOrder(), BasketOrderTaxonomy::STATUS_COMPLETE_COLLECTION);

        return  $this->json($basketOrderProduct, Response::HTTP_OK, [], [AbstractObjectNormalizer::GROUPS => ['order_product_action']]);
    }

    #[Route(path: '/{id}/addQuantityPacked')]
    public function addQuantityPacked(BasketOrderProduct $basketOrderProduct, Request $request): JsonResponse {
        $requestArray = $request->toArray();
        if (!isset($requestArray['quantityPacked'])) {
            throw new \Exception('No quantity packed param!');
        }
        if ($basketOrderProduct->getQuantityPacked() + (int) $requestArray['quantityPacked'] > $basketOrderProduct->getQuantityRequired()) {
            throw new \Exception('Too many quantity');
        }
        $basketOrderProduct->setQuantityPacked($basketOrderProduct->getQuantityPacked() + (int) $requestArray['quantityPacked']);
        $this->entityManager->persist($basketOrderProduct);
        $this->entityManager->flush();

        if ($this->basketOrderService->allowChangeStatus($basketOrderProduct->getBasketOrder(), BasketOrderTaxonomy::STATUS_COMPLETE_PACKING)) {
            $this->basketOrderService->changeBasketOrderStatus($basketOrderProduct->getBasketOrder(), BasketOrderTaxonomy::STATUS_COMPLETE_PACKING);
            $order = $basketOrderProduct->getBasketOrder()->getOrder();
            $order->setInternalStatusId($this->orderStatusService->getStatus(OrderTaxonomy::STATUS_TO_SEND));
            $this->entityManager->persist($order);
            $this->entityManager->flush();
        }

        return  $this->json($basketOrderProduct, Response::HTTP_OK, [], [AbstractObjectNormalizer::GROUPS => ['order_product_action']]);
    }
}