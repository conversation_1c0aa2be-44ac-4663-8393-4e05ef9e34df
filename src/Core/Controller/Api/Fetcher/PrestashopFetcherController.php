<?php

namespace App\Core\Controller\Api\Fetcher;

use App\Core\Entity\Integration;
use App\Core\Service\Fetcher\Order\PrestashopOrderFetcherService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class PrestashopFetcherController extends AbstractController {

    public function __construct(
        private readonly PrestashopOrderFetcherService $prestashopOrderFetcherService
    ){}

   #[Route(path: '/api/fetcher/prestashopapi/getorders', name: 'prestashop_fetcher_get_orders')]
   public function getOrders(Request $request, EntityManagerInterface $entityManager): JsonResponse {
        if ($integration = $request->get('integration')) {
            $integrationEntity = $entityManager->getRepository(Integration::class)->find($integration);
            $orders = $this->prestashopOrderFetcherService->fetchAllOrders($integrationEntity);

            return $this->json($orders, Response::HTTP_OK);
        }
        return $this->json([], Response::HTTP_NOT_FOUND);
   }

    #[Route(path: '/api/fetcher/prestashopapi/getorderbyid', name: 'prestashop_fetcher_get_orders_by_id')]
   public function getOrder(Request $request, EntityManagerInterface $entityManager): JsonResponse {
       if (($integration = $request->get('integration')) && $orderId = $request->get('order_id')) {
           $integrationEntity = $entityManager->getRepository(Integration::class)->find($integration);
           $orders = $this->prestashopOrderFetcherService->fetchOrder($orderId, $integrationEntity);

           return $this->json($orders, Response::HTTP_OK);
       }
       return $this->json(['message' => 'Something went wrong, contact with administrator'], Response::HTTP_NOT_FOUND);
   }
}