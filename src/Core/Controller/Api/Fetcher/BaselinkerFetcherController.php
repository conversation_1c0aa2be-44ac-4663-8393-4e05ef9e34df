<?php

namespace App\Core\Controller\Api\Fetcher;

use App\Core\Entity\Integration;
use App\Core\Service\Fetcher\Order\BaselinkerOrderFetcherService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route(path: '/api/fetcher/baselinkerapi')]
class BaselinkerFetcherController extends AbstractController {

    public function __construct(
        private readonly BaselinkerOrderFetcherService $orderFetcherService, private readonly BaselinkerOrderFetcherService $baselinkerOrderFetcherService) {}

    #[Route(path: '/getorders/{id}', name: 'baselinker_fetcher_get_orders')]
    public function getOrders(Integration $integration): JsonResponse {
        $results = $this->orderFetcherService->fetchLastOrders($integration);

        return $this->json($results);
    }

    #[Route(path: '/getOrderSources', name: 'baselinker_fetcher_get_order_sources')]
    public function getOrderSources(): JsonResponse {
            return $this->json($this->orderFetcherService->getOrderSources());
        }

    #[Route(path: '/getordersnofetch', name: 'baselinker_fetcher_get_orders_no_fetch')]
    public function getOrdersNoFetch(): JsonResponse {
        return $this->json($this->orderFetcherService->getOrders());
    }

    #[Route(path: '/getstatuseslist', name: 'baselinker_fetcher_get_statuses_list')]
    public function getStatusesList(): JsonResponse {
        return $this->json($this->orderFetcherService->getStatusesList());
    }

    #[Route(path: '/getorderbyid', name: 'baselinker_fetcher_get_order_by_id')]
    public function getOrderById(Request $request, EntityManagerInterface $entityManager): JsonResponse {
        if (($integration = $request->get('integration')) && $orderId = $request->get('order_id')) {
            $integrationEntity = $entityManager->getRepository(Integration::class)->find($integration);
            $orders = $this->baselinkerOrderFetcherService->fetchOrder($orderId, $integrationEntity);

            return $this->json($orders, Response::HTTP_OK);
        }
        return $this->json(['message' => 'Something went wrong, contact with administrator'], Response::HTTP_NOT_FOUND);
    }
}