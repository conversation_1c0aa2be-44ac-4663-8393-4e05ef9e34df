<?php

namespace App\Core\Controller\Api\Carrier\Fedex;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Carrier\Fedex\FedexService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/carrier/fedex')]
class Fedex extends AbstractController {

    public function __construct(private FedexService $fedexService) {}

    #[Route('/{carrier}/calculateCost/{order}', methods: ['POST'])]
    public function calculateCost(Carrier $carrier, Order $order, Request $request): JsonResponse {
        $data = $request->toArray();
        $results = $this->fedexService->calculateShipment($order, $carrier, $data['additionalSettings']);
        $costs = [];
        foreach ($results['output']['rateReplyDetails'] as $typeCost) {
            $costs[$typeCost['serviceName']] = $typeCost['ratedShipmentDetails'][0]['totalNetChargeWithDutiesAndTaxes'];
        }
        return $this->json(['cost' => $costs[array_key_first($costs)]], Response::HTTP_OK);
    }
}