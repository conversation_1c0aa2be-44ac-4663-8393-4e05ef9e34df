<?php

namespace App\Core\Controller\Api\Carrier\GLS;


use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Carrier\CarrierService;
use App\Core\Service\Carrier\GLS\Auth\GLSAuthService;
use App\Core\Service\Carrier\GLS\GLSApiHandler;
use App\Core\Service\Carrier\GLS\GLSIntegrationRequestService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/gls')]
class GLSController extends AbstractController{

    public function __construct(
        private readonly GLSAuthService $glsAuthService,
        private readonly GLSApiHandler $glsApiHandler,
        private readonly CarrierService $carrierService,
        private readonly EntityManagerInterface $entityManager,
        private readonly GLSIntegrationRequestService $glsIntegrationRequestService,
    )
    {
    }

    #[Route('/authenticate', name: 'gls_authenticate')]
    public function authenticate(): JsonResponse
    {
        return $this->json($this->glsAuthService->authenticate());
    }

    #[Route('/check', name: 'gls_services_allowed')]
    public function check(): JsonResponse
    {
        return $this->json($this->glsApiHandler->request('adeServices_GetAllowed',[]));
    }



    #[Route('/createShipment', name: 'gls_create_shipment')]
    public function createShipment(Request $request)
    {
        $content = $request->toArray();
        $carrier = $this->entityManager->getRepository(Carrier::class)->find($content['carrier']);

        $data = $this->entityManager->getRepository(Order::class)->findOneby(['order_id' => '1-317496-34391']);
        return $this->json($this->carrierService->createShipment($data, ['quantity' => 3], $carrier));
    }

    #[Route('/checkPreparingId/{id}', name: 'gls_check_preparing_id')]
    public function checkPreparingId($id): JsonResponse {
        return $this->json($this->glsIntegrationRequestService->checkPreparingId($id));
    }

    #[Route('/getLabel', name: 'gls_get_labell')]
    public function getLabel(): JsonResponse {
        $order = $this->entityManager->getRepository(Order::class)->find('018fc3a6-0cfb-7935-8724-5ae27d7a28a3');
        return $this->json($this->glsIntegrationRequestService->getLabelFromPreparing(1315773));
    }

    #[Route('/getIdsToPickup', name: 'gls_get_ids_to_picup')]
    public function getIdsToPickup(): JsonResponse {
        $order = $this->entityManager->getRepository(Order::class)->find('018fc3a6-0cfb-7935-8724-5ae27d7a28a3');
        return $this->json($this->glsIntegrationRequestService->getIdsToPickup());
    }

    #[Route('/createPickup', name: 'gls_get_label')]
    public function createPickup(): JsonResponse {
        return $this->json($this->glsIntegrationRequestService->createPickup());
    }
}