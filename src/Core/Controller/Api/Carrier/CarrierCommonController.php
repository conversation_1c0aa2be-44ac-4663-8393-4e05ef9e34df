<?php

namespace App\Core\Controller\Api\Carrier;

use App\Core\Service\Carrier\CarrierService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class CarrierCommonController extends AbstractController {

    #[Route('/api/carrier/getAvailableCarriers', name: 'api_carrier_get_available_carriers', methods: ['GET'])]
    public function getAvailableCarriers(CarrierService $carrierService): JsonResponse {
        return $this->json(['availableCarriers' => $carrierService->getCarrierTypes()], Response::HTTP_OK);
    }
}