<?php


namespace App\Core\Controller\Api\Carrier\DPD;


use App\Core\Service\Carrier\DPD\DPDMetaClientServices;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;


#[Route('/api/dpd')]
class DPDMetaController extends AbstractController
{
    private DPDMetaClientServices $dpdService;

    public function __construct(DPDMetaClientServices $dpdService)
    {
        $this->dpdService = $dpdService;
    }

    #[Route('/create-shipment', methods: ['POST'])]
    public function createShipment(Request $request): JsonResponse
    {
        $shipmentDetails = json_decode($request->getContent(), true);
        $token = $this->dpdService->authenticate();

        if (!$token) {
            return $this->json(['error' => 'Authentication failed'], 500);
        }

        $response = $this->dpdService->createShipment($shipmentDetails, $token);

        return $this->json($response);
    }
}