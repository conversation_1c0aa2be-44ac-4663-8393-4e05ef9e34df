<?php

namespace App\Core\Controller\Api\Carrier\DPD;

use App\Core\Service\Carrier\DPD\DPDIntegrationRequestService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/dpd')]
class DPDShipmentsController extends AbstractController
{
    public function __construct(
        private readonly DPDIntegrationRequestService $dpdIntegrationRequestService
    ){}

    #[Route('/test', methods: ['POST'])]
    public function generatePackageNumbers(Request $request): JsonResponse
    {
        $shipmentDetails = json_decode($request->getContent(), true);

        try {
            $response = $this->dpdIntegrationRequestService->generatePackageNumbers($shipmentDetails);
            return $this->json($response);
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], 500);
        }
    }

    #[Route('/order-courier', methods: ['POST'])]
    public function orderCourier(Request $request): JsonResponse
    {
        $orderDetails = json_decode($request->getContent(), true);

        try {
            $response = $this->dpdIntegrationRequestService->orderCourier($orderDetails);
            return $this->json($response);
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], 500);
        }
    }
}