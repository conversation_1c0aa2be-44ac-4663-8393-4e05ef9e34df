<?php

namespace App\Core\Controller\Api\Carrier\SelfPickup;
use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Carrier\CarrierService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

class SelfPickupController extends AbstractController {

    public function __construct(
        private CarrierService $carrierService
    ) {

    }

    #[Route('/api/carrier/createShipment/{id}', name: 'api_carrier_self_pickup', methods: ['GET'])]
    public function getSelfPickupPoints(Order $order, Request $request): JsonResponse {
        $content = $request->toArray();
        $carrier = $this->entityManager->getRepository(Carrier::class)->find($content['carrier']);

       return $this->json($this->carrierService->createShipment($order, [], $carrier));
    }
    #[Route('/api/carrier/createLabel/{id}', name: 'api_carrier_self_pickupp', methods: ['GET'])]
    public function getLabel(Order $order): JsonResponse {
        return $this->json($this->carrierService->downloadShipmentLabelByIdShipment($order, $order->getOrderDelivery()->getOrderDeliveryShipmentData()->last()->getShipmentId()));
    }
    #[Route('/api/carrier/checkStatus/{id}', name: 'api_carrier_self_pickuppp', methods: ['GET'])]
    public function checkShipment(Order $order): JsonResponse {
        return $this->json($this->carrierService->checkStatusOfShipment($order, $order->getOrderDelivery()->getOrderDeliveryShipmentData()->last()->getShipmentId()));
    }

}