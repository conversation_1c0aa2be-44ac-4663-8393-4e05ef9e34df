<?php

namespace App\Core\Controller\Api\Carrier\DHL;


use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Carrier\CarrierService;
use App\Core\Service\Carrier\DHL\DHL24IntegrationRequestService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route(['/api/dhl', '/dhl'])]
class DHLRequestController extends AbstractController {

    public function __construct(
        private readonly DHL24IntegrationRequestService $dhl24IntegrationRequestService, private readonly EntityManagerInterface $entityManager,
        private readonly CarrierService $carrierService,
    ) {
    }

    #[Route('/get-price')]
    public function getPrice(Request $request): Response {
        $requestData = $request->toArray();
        $result = $this->dhl24IntegrationRequestService->getPrice($requestData);
        return new Response(print_r($result, true));
    }

    #[Route('/create-shipment/{id}')]
    public function createShipment(Order $order, Request $request): JsonResponse {
        $content = $request->toArray();
        $carrier = $this->entityManager->getRepository(Carrier::class)->find($content['carrier']);

        return $this->json($this->carrierService->createShipment($order, [], $carrier));
    }

    #[Route('/create-label/{id}')]
    public function createLabel(Order $order): JsonResponse {
        return $this->json($this->carrierService->downloadShipmentLabelByIdShipment($order,$order->getOrderDelivery()->getOrderDeliveryShipmentData()->last()->getShipmentId()));
    }
    //    #[Route('/create-label')]
//    public function createLabel(Request $request,LoggerInterface $logger): Response
//    {
//        $labelDetails = $request->toArray();
//        $shipmentDetails = new GetShipmentTaxonomy();
//        $shipmentDetails->setReceiver($labelDetails['receiver'] ?? []);
//        $shipmentDetails->setContent($labelDetails['content'] ?? "");
//        $shipmentDetails->setPackage($labelDetails['package'] ?? []);
//        $errors = $this->validator->validate($shipmentDetails);
//
//        if (count($errors) > 0) {
//            $errorMessages = [];
//            foreach ($errors as $error) {
//                $fieldName = $error->getPropertyPath();
//                $errorMessages[] = $fieldName . ': ' . $error->getMessage();
//            }
//            throw new \Exception("Validation failed: " . implode(", ", $errorMessages));
//        }
//        try {
//            $result = $this->dhl24IntegrationRequestService->createLabel($shipmentDetails->getDefaultData());
//
//            if (is_object($result) && isset($result->createShipmentResult->label->labelType) && $result->createShipmentResult->label->labelType === "ZBLP") {
////            if (is_object($result) && isset($result->createShipmentResult->label->labelType) && $result->createShipmentResult->label->labelType === "BLP") {
//                return $this->json($result);
//                return PdfConverterService::downloadPdfFromBase64($result->createShipmentResult->label->labelContent, 'label.pdf');
//            } elseif (is_string($result)) {
//                throw new \Exception($result);
//            } else {
//                throw new \Exception("Unexpected result type");
//            }
//        } catch (\Exception $e) {
//            throw new \Exception("Error processing request: " . $e->getMessage());
//        }
//    }


}
