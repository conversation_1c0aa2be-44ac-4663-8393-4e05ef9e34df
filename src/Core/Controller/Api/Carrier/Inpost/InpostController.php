<?php


namespace App\Core\Controller\Api\Carrier\Inpost;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Carrier\CarrierFactory;
use App\Core\Service\Carrier\CarrierService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route(['/api/inpost', '/inpost'])]
class InpostController extends AbstractController{

    public function __construct(
        private readonly CarrierService $carrierService,
    ) {
    }


    #[Route('/create-shipment/{id}')]
    public function createShipment(Order $order, Request $request): JsonResponse {
        $content = $request->toArray();
        $carrier = $this->entityManager->getRepository(Carrier::class)->find($content['carrier']);
        return $this->json($this->carrierService->createShipment($order, ['parcelSettings' => 'C'], $carrier));
    }

    #[Route('/create-label/{id}')]
    public function createLabel(Order $order): JsonResponse {
        return $this->json($this->carrierService->downloadShipmentLabelByIdShipment($order,$order->getOrderDelivery()->getOrderDeliveryShipmentData()->last()->getShipmentId()));
    }
}