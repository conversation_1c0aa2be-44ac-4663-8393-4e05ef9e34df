<?php

namespace App\Core\Controller\Api\Carrier;

use App\Core\Entity\Carrier;
use App\Core\Service\Carrier\CarrierManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/carriers')]
class CarrierController extends AbstractController {

    #[Route('/add', methods: ['POST'])]
    public function addCarrier(Request $request, CarrierManager $carrierManager): JsonResponse {
        $data = $request->toArray();

        return $this->json(['carrier' => $carrierManager->createCarrier($data)]);
    }
    
    #[Route('/{id}/edit', methods: ['POST'])]
    public function editCarrier(Carrier $carrier, Request $request, CarrierManager $carrierManager): JsonResponse {
        $data = $request->toArray();
        $carrierType = $carrierManager->getCarrierHandler($carrier->getType());
        if ([] !== ($errors = $carrierManager->validateCarrier($carrierType, $data))) {
            return $this->json(['errors' => $errors]);
        }
        
        return $this->json(['carrier' => $carrierManager->editCarrier($carrier, $carrierManager->sanitizeCarrier($carrierType, $data))]);
    }

    #[Route('/{id}/edit', methods: ['GET'])]
    public function getCarrierView(Carrier $carrier, Request $request, CarrierManager $carrierManager): JsonResponse {
        $handler = $carrierManager->getCarrierHandler($carrier->getType());
        $fields = $handler->getRequiredFields();
        foreach($fields as &$field) {
            $field['value'] = $carrier->getSettingsByKey($field['name']);
        }
        return $this->json(['carrier' => $fields]);
    }

    #[Route('/getAvailableTypes', methods: ['GET'])]
    public function getCarriers(CarrierManager $carrierManager): JsonResponse {
        return $this->json(['carriers' => $carrierManager->getAvailableCarrierTypes()]);
    }
    
    #[Route('/getAllCarriers', methods: ['GET'])]
    public function getAllCarriers(CarrierManager $carrierManager): JsonResponse {
        return $this->json(['carriers' => $carrierManager->getAllCarriers()]);
    }
    
    
    #[Route('/getAllCarriersByType', methods: ['POST'])]
    public function getCarriersByType(Request $request, CarrierManager $carrierManager): JsonResponse {
        $data = $request->toArray();
        
        return $this->json(['carriers' => $carrierManager->getAllCarriersByType($data)]);
        
    }

    public function getCarrierPickupHours() {

    }
}