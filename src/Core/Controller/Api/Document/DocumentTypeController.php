<?php

namespace App\Core\Controller\Api\Document;

use App\Core\Entity\DocumentType;
use App\Core\Service\Document\DocumentTypeService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use <PERSON>ymfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/documents/type')]
class DocumentTypeController extends AbstractController{
    
    public function __construct(
        private readonly DocumentTypeService $documentTypeService,
    ){}

    #[Route('/list', name: 'document_type_list', methods: ['GET'])]
    public function getDocumentTypes(): JsonResponse {
        return $this->json(
            [
            'data' => $this->documentTypeService->list(),
            'message' => 'Document Types found'
            ],
            200,
            [],
            [
                'ignored_attributes' => ['RelatedDocuments']
            ]
        );
    }

    #[Route('/add', name: 'document_type_add', methods: ['POST'])]
    public function createDocumentType(Request $request): JsonResponse{
        $data = json_decode($request->getContent(), true);
        if (!isset($data['name'], $data['prefix'])) {
            return $this->json(['error' => 'Name and Prefix is required'], 400);
        }
        $result = $this->documentTypeService->add($data);
        return $this->json(['message' => $result['message']], $result['code']);
    }

    #[Route('/edit/{id}', name: 'document_type_edit', methods: ['PUT'])]
    public function edit($id , Request $request): JsonResponse {
        $data = json_decode($request->getContent(), true);
        $status = $this->documentTypeService->get($id);
        $message = $this->documentTypeService->edit($status, $data);
        return $this->json(
            [
                'message' => $message['message']
            ], $message['code']
        );

    }

    #[Route('/delete/{id}', name: 'document_type_delete', methods: ['DELETE'])]
    public function delete(DocumentType $id): JsonResponse {
        $status = $this->documentTypeService->get($id);
        $message = $this->documentTypeService->delete($status);
        return $this->json(
            [
                'message' => $message['message']
            ], $message['code']
        );

    }

    #[Route('/get/{id}', name: 'document_type_get', methods: ['GET'])]
    public function get($id): JsonResponse {
        $type = $this->documentTypeService->get($id);
        return $this->json(
            [
                'message' => 'Document Type found',
                'data' => $type
            ],
            200,
            [],
            [
                'ignored_attributes' => ['RelatedDocuments']
            ]
        );
    }


    
    
    

}