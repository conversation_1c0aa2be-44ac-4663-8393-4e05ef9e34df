<?php

namespace App\Core\Controller\Api\Document;

use App\Core\Entity\Document;
use App\Core\Entity\DocumentItem;
use App\Core\Service\Document\DocumentItemService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/documents/item')]
class DocumentItemController extends AbstractController {
    public function __construct(
        private DocumentItemService $documentItemService,
    ) {
    }

    #[Route('/add/{documentId}', name: 'document_item_add', methods: ['POST'])]
    public function add(Document $documentId, Request $request) {
        $data = json_decode($request->getContent(), true);

        return $this->json($this->documentItemService->add($documentId, $data));
    }

    #[Route('/list/{documentId}', name: 'document_item_list', methods: ['GET'])]
    public function list(Document $documentId) {

        return $this->json(
            [
                'data' => $this->documentItemService->list($documentId),
                'message' => 'Document Items Found'
            ]
        );
    }

    #[Route('/get/{documentItemId}', name: 'document_item_get', methods: ['GET'])]
    public function get($documentItemId) {

        return $this->json([
            'data' => $this->documentItemService->get($documentItemId),
            'message' => 'Document Id Found'
        ]);
    }

    #[Route('/edit/{documentItemId}', name: 'document_item_edit', methods: ['PUT'])]
    public function edit(DocumentItem $documentItemId, Request $request) {

        $data = json_decode($request->getContent(), true);

        if (empty($data)) {
            return $this->json(['status' => 'error', 'message' => 'No data provided']);
        }

        return $this->json([
            'data' => $this->documentItemService->edit($documentItemId,$data),
            'message' => 'Document Id Found'
        ]);
    }

    #[Route('/delete/{documentItemId}', name: 'document_item_delete', methods: ['DELETE'])]
    public function delete(Document $documentId, DocumentItem $documentItemId) {
        try {
            return $this->json($this->documentItemService->delete($documentId, $documentItemId));
        } catch (\Exception $e) {
            return $this->json(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    #[Route('/assign/{documentItemId')]
    public function assign(DocumentItem $documentItemId) {
        return $this->json(
            [
                'data' => $this->documentItemService->assign($documentItemId),
                'message' => 'Document Item Assigned'
            ]
        );
    }

    #[Route('/assignEan/{documentItemId}', methods: ['POST'])]
    public function assignEan(DocumentItem $documentItemId, Request $request) {
        $data = json_decode($request->getContent(), true);
        $response = $this->documentItemService->assignEans($documentItemId, $data);
        if('success' === $response['status']){
            return $this->json(
                [
                    'data' => $this->documentItemService->assignEans($documentItemId ,$data),
                    'message' => 'Document Item Assigned'
                ]
            );
        }

        return $this->json([
            'status' => 'error',
            'message' => 'Error assigning EANs to product'
        ]);

    }
}