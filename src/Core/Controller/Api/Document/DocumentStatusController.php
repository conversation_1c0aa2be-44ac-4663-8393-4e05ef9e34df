<?php

namespace App\Core\Controller\Api\Document;

use App\Core\Entity\DocumentStatus;
use App\Core\Service\Document\DocumentStatusService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/documents/status')]
class DocumentStatusController extends AbstractController{


    public function __construct(
        private readonly DocumentStatusService $documentStatusService,
    ){}

    #[Route('/list', name: 'document_status_list', methods: ['GET'])]
    public function list(): JsonResponse {
        return $this->json(
            [
                'data' => $this->documentStatusService->list(),
                'message' => 'Document statuses found'
            ],
            200,
            [],
            [
                'ignored_attributes' => ['RelatedDocuments']
            ]
        );
    }

    #[Route('/add', name: 'document_status_create', methods: ['POST'])]
    public function add(Request $request): JsonResponse{
        $data = json_decode($request->getContent(), true);
        if(!isset($data['name'])){
            return $this->json(
                [
                    'error' => 'Name is required'
                ], 400);
        }
        $result = $this->documentStatusService->add($data['name']);
        return $this->json(
            [
                'message' => $result['message']
            ], $result['code']);

    }


    #[Route('/edit/{id}', name: 'document_status_edit', methods: ['PUT'])]
    public function edit(DocumentStatus $id , Request $request): JsonResponse {
        $data = json_decode($request->getContent(), true);
        $message = $this->documentStatusService->edit($id, $data);
        return $this->json(
            [
                'message' => $message['message']
            ], $message['code']
        );

    }

    #[Route('/delete/{id}', name: 'document_status_delete', methods: ['DELETE'])]
    public function delete(DocumentStatus $id): JsonResponse {
        $message = $this->documentStatusService->delete($id);
        return $this->json(
            [
                'message' => $message['message']
            ], $message['code']
        );

    }

    #[Route('/get/{id}', name: 'document_status_get', methods: ['GET'])]
    public function get(DocumentStatus $id): JsonResponse {
        return $this->json(
            [
                'data' => $id,
                'message' => 'Document status found'
            ],
            200,
            [],
            [
                'ignored_attributes' => ['RelatedDocuments']
            ]
        );
    }
}