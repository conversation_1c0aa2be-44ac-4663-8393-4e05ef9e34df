<?php

namespace App\Core\Controller\Api\Document;

use App\Core\Entity\Document;
use App\Core\Entity\DocumentSellerCompany;
use App\Core\Entity\DocumentStatus;
use App\Core\Service\Document\DocumentItemService;
use App\Core\Service\Document\DocumentService;
use App\Core\Service\Subiekt\SubiektApiService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/documents/document')]
class DocumentController extends AbstractController {

    public function __construct(
        private readonly DocumentService     $documentService,
        private readonly DocumentItemService $documentItemService, private readonly SubiektApiService $subiektApiService,
        private readonly SerializerInterface $serializer, private readonly EntityManagerInterface $entityManager,
    ) {
    }

    #[Route('/add', name: 'document_generate', methods: ['POST'], priority: 2)]
    public function add(Request $request): JsonResponse {
        $data = json_decode($request->getContent(), true);
        if (!$data['type']) {
            return $this->json(['error' => 'Type is required'], 400);
        }

        if(!$data['sellerCompany']) {
            return $this->json(['error' => 'Seller company is required'], 400);
        }
        $sellerCompany = $this->entityManager->getRepository(DocumentSellerCompany::class)->find($data['sellerCompany']);
        if(!$sellerCompany) {
            return $this->json(['error' => 'Seller company not found'], 404);
        }

        if ($this->documentService->add($data, $sellerCompany)) {
            return $this->json(['status' => 'created'], 201);
        }
        return $this->json([
            'status' => "error",
            'message' => 'Error creating document',
        ], 500);
    }

    #[Route('/list', name: 'document_list', methods: ['GET'], priority: 2)]
    public function list(): JsonResponse {
        return $this->json(
            [
                'data' => $this->documentService->list(),
                'message' => 'Documents found'
            ], Response::HTTP_OK,
            [],
            [
                AbstractNormalizer::IGNORED_ATTRIBUTES => ['type', 'status']
            ]
        );
    }

    #[Route('/subiekt/GetProductByChar', name: 'document_subiekt_get_products_by_char', methods: ['POST'], priority: 2)]
    public function subiektGetProductByChar(Request $request) {
        $char = $request->getContent(false);
        $chardeco = json_decode($char, true);
        return $this->json($this->documentItemService->getProducts($chardeco['chars']));
    }


    #[Route('/subiekt/generate/{document}', name: 'document_subiekt_generate', methods: ['POST'])]
    public function generateDocument(Document $document): JsonResponse {
        return $this->json([
            'data' => $this->subiektApiService->generateDocument($document),
            'message' => 'Document generated'
        ]);
    }

    #[Route('/get/{id}', name: 'document_get', methods: ['GET'])]
    public function getDocument(string $id): JsonResponse {
        $document = $this->documentService->get($id);
        return $this->json(
            [
                'data' => $this->serializer->normalize(
                    $document,
                    null,
                    [
                        AbstractNormalizer::CALLBACKS => [
                            'eans' => fn($value) => $value ?? [],
                        ],
                        AbstractNormalizer::IGNORED_ATTRIBUTES => ['type', 'status']
                    ]
                ),
                'message' => $document ? 'Document found' : 'Document not found'
            ],
            $document ? Response::HTTP_OK : Response::HTTP_NOT_FOUND
        );
    }

    #[Route('/edit/{id}', name: 'document_edit', methods: ['PUT'], priority: 1)]
    public function edit($id, Request $request): JsonResponse {
        $data = json_decode($request->getContent(), true);
        if (!$data['type']) {
            return $this->json(['status' => 'error', 'message' => 'Type is required'], 400);
        }
        if ($this->documentService->edit($id, $data)) {
            return $this->json(['status' => 'edited'], 200);
        }
        return $this->json(['status' => 'error', 'message' => 'Error editing document'], 500);

    }

    #[Route('/delete/{id}', name: 'document_delete', methods: ['DELETE'], priority: 1)]
    public function delete(Document $id): JsonResponse {
        if ($this->documentService->delete($id)) {
            return $this->json(['status' => 'deleted'], 200);
        }
        return $this->json(['error' => 'Error deleting document'], 500);
    }
}