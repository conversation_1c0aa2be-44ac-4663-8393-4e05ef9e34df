<?php

namespace App\Core\Controller\Api\Document;

use App\Core\Service\Document\DocumentSellerCompanyService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/documents/seller-company')]
class DocumentSellerCompanyController extends AbstractController {
    public function __construct(
        private readonly DocumentSellerCompanyService $documentSellerCompanyService,
    ) {}

    #[Route('/add', methods: ['POST'])]
    public function add(Request $request): JsonResponse{
        $data = json_decode($request->getContent(), true);
        $message = $this->documentSellerCompanyService->add($data);
        return $this->json(
            [
                'message' => $message['message']
            ], $message['code']
        );
    }

    #[Route('/edit/{id}', methods: ['PUT'])]
    public function edit($id, Request $request): JsonResponse{
        $data = json_decode($request->getContent(), true);
        $message = $this->documentSellerCompanyService->edit($id, $data);
        return $this->json(
            [
                'message' => $message['message']
            ], $message['code']
        );
    }

    #[Route('/delete/{id}', methods: ['DELETE'])]
    public function delete($id): JsonResponse{
        $message = $this->documentSellerCompanyService->delete($id);
        return $this->json(
            [
                'message' => $message['message']
            ], $message['code']
        );
    }

   #[Route('/get/{id}', methods: ['GET'])]
    public function get($id): JsonResponse{
        $message = $this->documentSellerCompanyService->get($id);
       return $this->json(
           [
               'message' => $message['message'],
               'data' => $message['data']
           ], $message['code']
       );
    }

    #[Route('/list', methods: ['GET'])]
    public function list(): JsonResponse{
        $message = $this->documentSellerCompanyService->list();
        return $this->json(
            [
                'message' => $message['message'],
                'data' => $message['data']
            ], $message['code']
        );
    }
}