<?php

namespace App\Core\Controller\Api\Document\Company;

use App\Core\Entity\Document;
use App\Core\Entity\DocumentSellerCompany;
use App\Core\Service\Document\Company\CompanyDetectorService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;


#[Route('/api/documents/company')]
class DocumentCompanyController extends AbstractController
{
    public function __construct(private readonly SerializerInterface $serializer, private readonly EntityManagerInterface $entityManager) {}

    #[Route('/checkCompany', name: 'api_document_company_check', methods: ['POST'])]
    public function checkCompany(Request $request, CompanyDetectorService $detectorService): JsonResponse
    {
        $file = $request->files->get('file');

        if (!$file || strtolower($file->getClientOriginalExtension()) !== 'xml') {
            return $this->json(['error' => 'Invalid file type'], 400);
        }

        try {
            if(is_file($file->getPathname())) {
                $decodedFile = $this->serializer->decode(file_get_contents($file->getPathname()), 'xml');
                $company = $detectorService->detectCompany($decodedFile);

                return $this->json(['success' => true, 'data' => $company]);
            } else {
                throw new \Exception('A problem with provided file!');
            }


        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], 400);
        }
    }

    #[Route('/createDocument', name: 'api_document_company_upload', methods: ['POST'])]
    public function createDocument(Request $request, CompanyDetectorService $detectorService): JsonResponse
    {
        $file = $request->files->get('file');
        $requestedCompany = $request->get('company');
        if (!$requestedCompany) {
            return $this->json(['error' => 'No company!'], 400);
        }
        if (!$file || strtolower($file->getClientOriginalExtension()) !== 'xml') {
            return $this->json(['error' => 'Invalid file type'], 400);
        }
        $requestedCompany = $this->entityManager->getRepository(DocumentSellerCompany::class)->find($requestedCompany);
        try {
            if(is_file($file->getPathname())) {
                $decodedFile = $this->serializer->decode(file_get_contents($file->getPathname()), 'xml');
                $companyFromFile = $detectorService->detectCompany($decodedFile);
                if ($companyFromFile !== $requestedCompany) {
                    return $this->json(['error' => 'Bad Company Selected!'], 400);
                }
                $handledFile = $detectorService->handleFile($decodedFile);
                return $this->json(['success' => true, 'data' => $handledFile]);
            } else {
                return $this->json(['error' => 'A problem with provided file!'], 400);
            }

        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], 400);
        }
    }
}