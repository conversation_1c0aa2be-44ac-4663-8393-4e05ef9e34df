<?php

namespace App\Core\Controller\Api\BasketOrder;

use App\Core\Entity\Basket;
use App\Core\Entity\BasketOrder;
use App\Core\Entity\OrderStatus;
use App\Core\EventDispatcher\BasketOrderEvent;
use App\Core\Service\Basket\BasketOrderService;
use App\Core\Service\Basket\BasketService;
use App\Core\Service\Carrier\CarrierService;
use App\Core\Service\LoggerService;
use App\Core\Taxonomy\BasketOrderTaxonomy;
use App\Core\Taxonomy\BasketTaxonomy;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route(['/api/basket/order', '/basket/order'])]
class BasketOrderController extends AbstractController {

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly BasketService $basketService,
        private readonly BasketOrderService $basketOrderService,
        private readonly CarrierService $carrierService,
        private readonly EventDispatcherInterface $eventDispatcher,
        private readonly LoggerService $loggerService
    ) {}

    #[Route('/new/{id}')]
    public function new(Basket $basket, Request $request) {
        $basketOrderAssignCountLimit = BasketOrderTaxonomy::MAX_ORDER_IN_BASKET_LIMIT;
        $requestArray = $request->toArray();
        if ($this->entityManager->getRepository(BasketOrder::class)->count(['basket_id' => $basket->getId()]) >= BasketOrderTaxonomy::MAX_ORDER_IN_BASKET_LIMIT) {
            return $this->json(['message' => 'Max order in basket limit reached'], Response::HTTP_BAD_REQUEST);
        }
        if (array_key_exists('orderLimit', $requestArray) && is_int($requestArray['orderLimit'])) {
            $basketOrderAssignCountLimit = $requestArray['orderLimit'];
        }
        $this->basketOrderService->assignOrdersToBasket($basket, $basketOrderAssignCountLimit, $this->getUser());

        return $this->json(['basket' => $basket], Response::HTTP_OK);
    }

    #[Route('/view/{id}/order')]
    public function view(BasketOrder $basketOrder) {
        return $this->json($basketOrder, Response::HTTP_OK);
    }

    #[Route('/delete/{id}/fromBasket')]
    public function removeFromBasket(BasketOrder $basketOrder) {
        try {
            $basket = $basketOrder->getBasketId();
            $basketOrderCount = $this->entityManager->getRepository(BasketOrder::class)->count(['basket_id' => $basket]);
            if (0 === $basketOrderCount) {
                $basket->setUser(null);
                $basket->setStatusId(BasketTaxonomy::STATUS_EMPTY);
                $this->entityManager->persist($basket);
                $this->loggerService->basketChangeStatus($basket, $this->getUser(), BasketTaxonomy::STATUS_EMPTY);
            }
            $order = $basketOrder->getOrder();
            $order->setInternalStatusId($this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_REMOVED_FROM_BASKET]));
            $this->entityManager->persist($order);
            $this->entityManager->remove($basketOrder);
            $this->entityManager->flush();

            return $this->json(['Order removed'], Response::HTTP_OK);
        } catch (\Exception $exception) {
            return $this->json('Something went wrong: ' . $exception->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }
    #[Route('/view/all')]
    public function viewAll() {
        $baskets = $this->entityManager->getRepository(Basket::class)->findAll();

        return $this->json($baskets, Response::HTTP_OK);
    }

    #[Route('/{id}/finish')]
    public function finishPacking(BasketOrder $basketOrder, Request $request): JsonResponse {
        if (BasketOrderTaxonomy::STATUS_COMPLETE_PACKING !== $basketOrder->getStatusId()) {
            throw new \Exception(sprintf('You can not finish order %s with status %s', $basketOrder->getId()->toRfc4122(), $basketOrder->getStatusId()));
        }
        $basket = $basketOrder->getBasketId();
        $basketOrder->setBasketId(null);
        $this->entityManager->persist($basketOrder);
        $this->entityManager->flush();
        $this->basketService->finishPacking($basket, $this->getUser());

        $content = $request->toArray();
        $orderBasketEvent = new BasketOrderEvent();
        $orderBasketEvent->setData(['basketOrder' => $basketOrder, 'settings' => $content['settings'] ?? []]);
        $this->eventDispatcher->dispatch($orderBasketEvent,  'order.basket.finish_packing');

        return $this->json($basketOrder, Response::HTTP_OK);
    }

    #[Route('/{id}/getParcelSettings')]
    public function getOrderParcelSettings(BasketOrder $basketOrder): JsonResponse {
        $order = $basketOrder->getOrder();

        return $this->json(['parcelSettings' => $this->carrierService->getParcelsSettings($order)]);
    }

}