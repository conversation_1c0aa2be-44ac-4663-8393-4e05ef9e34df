<?php

namespace App\Core\Controller\Api;

use App\Core\Entity\Order;
use App\Core\Message\BaselinkerZKRegenerate;
use App\Core\Rules\RuleTrigger;
use App\Core\Service\Fetcher\Order\BaselinkerOrderFetcherService;
use Doctrine\ORM\EntityManagerInterface;
use SCA\Rules\Entity\Rule;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\Encoder\CsvEncoder;
use Symfony\Component\Serializer\SerializerInterface;

class ApiHelloController extends AbstractController {

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly SerializerInterface $serializer,
        private readonly MessageBusInterface $messageBus,
    ) {
    }

    #[Route(path: '/api/hello', name: 'hello_controller')]
    public function hello() {
    }


    #[Route(path: '/api/test', name: 'test_hehe_name_controller')]
    public function test(MenuService $menuService): JsonResponse {
        $links = $menuService->getMenuLinks();

        return $this->json(['links' => $links]);
    }

    #[Route(path: '/api/getZkFromBaselinkerByCSVFile', name: 'test_name_controller')]
    public function getZkFromBaselinkerByCSVFile(Request $request, BaselinkerOrderFetcherService $baselinkerOrderFetcherService) {
        try {
            $file = $request->files->get('file');
            $content = file_get_contents($file);
            $decodedArray = $this->serializer->decode($content, 'csv', [CsvEncoder::DELIMITER_KEY => ';']);
            $count = 0;
            foreach ($decodedArray as $order) {
                $message = new BaselinkerZKRegenerate($order['zk_number'], (int) $order['original_number']);
                $this->messageBus->dispatch($message);
                $count++;
            }

            return $this->json(['message' => 'Settings updated successfully, messages created: ' . $count]);
        } catch (\Exception $exception) {
            return $this->json(['message' => $exception->getMessage()]);
        }
    }

    #[Route(path: '/api/autoaction', name: 'api_autoaction')]
    public function autoaction(RuleTrigger $ruleTrigger) {
        $order = $this->entityManager->getRepository(Order::class)->findOneBy(['order_id' => '1-109664-1']);
        $ruleTrigger->triggerEvent('order.fetched.after.save',['order' => $order]);
        $r = new Rule($event->getName(), ['order.status.condition' => ['operator' => '===', 'value' => '0195101b-187c-7708-8f92-95b35030fdd5']], ['set.status.action' => ['value' => '0195101b-187c-7708-8f92-95b3521f7f0e']], $context);
        array_push($rules, $r);
    }
}