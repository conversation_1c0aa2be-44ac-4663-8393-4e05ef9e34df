<?php

namespace App\Core\Controller\Api\Order;

use App\Core\Entity\Carrier;
use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Entity\OrderDeliveryShipmentData;
use App\Core\Entity\OrderStatus;
use App\Core\EventDispatcher\OrderStatusChangeEvent;
use App\Core\Message\ChangePrestashopStatusMessenger;
use App\Core\Message\OrderRenewMessage;
use App\Core\Rules\RuleTrigger;
use App\Core\Scheduler\Message\SingleOrderCheckStatusInPrestaShopMessage;
use App\Core\Service\Carrier\CarrierService;
use App\Core\Service\Order\OrderFilterService;
use App\Core\Service\ProductSetResolver;
use App\Core\Service\View\Order\OrderViewService;
use App\Core\Taxonomy\OrderStatusTaxonomy;
use App\Core\Utility\Money;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Uid\Uuid;

#[Route('/api/orders')]
class OrderView extends AbstractController {
    
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly DenormalizerInterface $denormalizer,
        private readonly CarrierService $carrierService,
        private readonly OrderFilterService $orderFilterService,
        private readonly MessageBusInterface $messageBus,
        private readonly RuleTrigger $ruleTrigger,
    ) {}
    
    #[Route(path: ['/view/all'], name: 'orders_view')]
    public function ordersView(Request $request, OrderViewService $orderViewService): JsonResponse {
        $integrationType = $request->get('integration');
        if (null === $integrationType) {
            return $this->json(['Integration not found']);
        }
        $orders = $orderViewService->viewOrders($integrationType);
        
        return $this->json($orders, Response::HTTP_OK);
    }
    
    #[Route(path: '/view/{id}/status', methods: ['GET'])]
    public function viewOrdersByStatus(OrderStatus $orderStatus): JsonResponse {
        $orders = $this->entityManager->getRepository(Order::class)->findBy(['internal_status_id' => $orderStatus]);
        
        return $this->json(['orders' => $orders], Response::HTTP_OK);
    }
    
    #[Route(path: '/view/filter', methods: ['GET'])]
    public function filterOrder(Request $request): JsonResponse {
        $requestedFilters = $request->query->all();
        $orderBy = $requestedFilters['orderBy'] ?? null;
        $orderByOrder = isset($requestedFilters['orderByOrder']) && in_array($requestedFilters['orderByOrder'], [
            'ASC',
            'DESC'
        ]) ? strtoupper($requestedFilters['orderByOrder']) : 'ASC';
        $limit = $requestedFilters['limit'] ?? 50;
        $page = $requestedFilters['page'] ?? 1;
        $offset = $page ? $limit * ($page - 1) : null;
        $orWhere = $requestedFilters['orWhere'] ?? false;
        $acceptedFilters = $this->orderFilterService->getAcceptedFilters($requestedFilters);
        $result = $this->orderFilterService->filterOrders($acceptedFilters, $offset, $limit, $orderBy, $orderByOrder, $orWhere);
        
        return $this->json([
            'firstPage' => 1,
            'currentPage' => $page,
            'lastPage' => ceil($result['totalOrders'] / $limit),
            'totalOrders' => $result['totalOrders'],
            'orders' => $result['orders'],
        ], Response::HTTP_OK);
    }
    
    #[Route(path: '/view/filterlist', methods: ['GET'])]
    public function filterList(): JsonResponse {
        return $this->json($this->orderFilterService->getAllFilters(), Response::HTTP_OK);
    }
    
    #[Route(path: '/view/filterbyproductskuean', methods: ['GET'])]
    public function filterOrdersByEanSku(Request $request): JsonResponse {
        $requestedFilters = $request->query->all();
        $orders = $this->entityManager->getRepository(Order::class)->findByData([
            'ean' => $requestedFilters['ean'] ?? null,
            'sku' => $requestedFilters['sku'] ?? null
        ]);
        
        return $this->json($orders, Response::HTTP_OK);
    }
    
    #[Route(path: '/view/filterbytrackingid', methods: ['GET'])]
    public function filterOrderByTrackingId(Request $request): JsonResponse {
        $requestedFilters = $request->query->all();
        if (!isset($requestedFilters['tracking_id'])) {
            return $this->json(['message' => 'No tracking_id provided'], Response::HTTP_BAD_REQUEST);
        }
        $orderDeliveryShipmentData = $this->entityManager->getRepository(OrderDeliveryShipmentData::class)->findOneBy(['tracking_id' => $requestedFilters['tracking_id']]);
        if ($orderDeliveryShipmentData) {
            return $this->json($orderDeliveryShipmentData->getOrderDeliveryId()->getOrder(), Response::HTTP_OK);
        }
        
        return $this->json(['message' => 'Tracking id ' . $requestedFilters['tracking_id'] . ' not found!'], Response::HTTP_NOT_FOUND);
    }
    
    #[Route(path: '/{id}/view', methods: ['GET'])]
    public function orderView(Order $order): JsonResponse {
        $productImages = [];
        foreach ($order->getProducts() as $product) {
            $productImages[$product->getSku()]['coverImage'] = $product->getCoverImageUrl();
        }
        
        return $this->json([
                'order' => $order,
                'images' => $productImages,
            ], Response::HTTP_OK);
    }
    
    #[Route(path: '/{id}/createShipment')]
    public function createShipment(Order $order, Request $request, CarrierService $carrierService): JsonResponse {
        try {
            $content = $request->toArray();
            $carrier = $this->entityManager->getRepository(Carrier::class)->find($content['carrier']);
            $orderDeliveryShipmentData = $carrierService->createShipment($order, $content['settings'] ?? [], $carrier);
            if (!empty($orderDeliveryShipmentData)) {
                $this->ruleTrigger->triggerEvent('order.shipment.create.success', ['order' => $order]);
            }
            else {
                $this->ruleTrigger->triggerEvent('order.shipment.create.failure', ['order' => $order]);
            }
            $this->entityManager->persist($order);
            $this->entityManager->flush();


        } catch (\Exception $e) {
            $this->entityManager->persist($order);
            $this->entityManager->flush();
            $this->ruleTrigger->triggerEvent('order.shipment.create.failure', ['order' => $order]);
            return $this->json(['message' => $e->getMessage()], Response::HTTP_BAD_REQUEST);
        }
        
        return $this->json(['message' => 'OK'], Response::HTTP_OK);
    }
    
    #[Route(path: '/{id}/modifyPrice', methods: ['POST'])]
    public function modifyPrice(Order $order, Request $request): JsonResponse {
        $content = $request->toArray();
        if (isset($content['price'])) {
            $newPaymentDone = new Money($content['price']);
            if ($newPaymentDone->getAmount() === 0) {
                $order->setPaid(false);
            }
            $fullPriceWithDiscount = new Money($order->getFullPriceWithDiscount());
            $deliveryPrice = new Money($order->getOrderDelivery()->getDeliveryPrice());
            $currentPrice = $fullPriceWithDiscount->add($deliveryPrice);

            if ($newPaymentDone->getAmount() < 0 || $newPaymentDone->getAmount() > $currentPrice->getAmount()) {
                throw new \Exception('Wartość nonsensowna! Kwota musi być >= 0 lub <= wartości zamówienia');
            }
            if ($newPaymentDone->equal($currentPrice)) {
                $order->setPaid(true);
                $integration = $this->entityManager->getRepository(Integration::class)->find($order->getOrderSourceId());
                $message = new ChangePrestashopStatusMessenger($order->getId(), $integration->getId(), OrderStatusTaxonomy::PRESTASHOP_PAYMENT_ACCEPTED);
                $this->messageBus->dispatch($message);
            }
            $order->setPaymentDone($newPaymentDone->getAmount());
            $this->entityManager->persist($order);
            $this->entityManager->flush();
            if ($order->isPaid()) {
                $this->ruleTrigger->triggerEvent('order.is.paid', ['order' => $order]);
            }


        }
        
        return $this->json($order, Response::HTTP_OK);
    }
    
    #[Route(path: '/get-many-by-uuid', methods: ['POST'])]
    public function bulkReturnOrders(Request $request): JsonResponse {
        $content = $request->toArray();
        $orderToReturn = [];
        foreach ($content['orders'] as $order) {
            if (Uuid::isValid($order)) {
                $orderToReturn[$order] = $this->entityManager->getRepository(Order::class)->find($order);
            }
            else {
                $orderToReturn[$order] = "Invalid Order UUID!";
            }
        }
        
        return $this->json($orderToReturn, Response::HTTP_OK);
    }
    
    #[Route(path: '/many-change-status', methods: ['POST'])]
    public function bulkStatusChange(Request $request) {
        $content = $request->toArray();
        if (Uuid::isValid($content['status'])) {
            $status = $this->entityManager->getRepository(OrderStatus::class)->find($content['status']);
            $orderToReturn = [];
            foreach ($content['orders'] as $order) {
                if (Uuid::isValid($order)) {
                    $orderEntity = $this->entityManager->getRepository(Order::class)->find($order);
                    $orderEntity->setInternalStatusId($status);
                    $this->entityManager->persist($orderEntity);
                    $orderToReturn[$order] = $orderEntity;
                }
                else {
                    $orderToReturn[$order] = "Invalid Order UUID!";
                }
            }
            $this->entityManager->flush();
            
            return $this->json(['message' => 'Success!'], Response::HTTP_OK);
        }
        throw new \Exception('Invalid status UUID!');
    }
    
    #[Route(path: '/{id}/getOrderLabels')]
    public function getOrderLabels(Order $order, SerializerInterface $serializer): JsonResponse {
        $orderData = $order->getOrderDelivery()->getOrderDeliveryShipmentData();
        $arrayOfData = [];
        foreach ($orderData as $od) {
            $normalized = $serializer->normalize($od, null, [
                    AbstractNormalizer::IGNORED_ATTRIBUTES => ['orderDeliveryId']
                ]);
            $normalized['shipmentStatus'] = $statusFromWebhook['status'] ?? '';
            $normalized['description'] = $statusFromWebhook['description'] ?? '';
            $arrayOfData[] = $normalized;
        }
        
        return $this->json(['orderDeliveryShipmentData' => $arrayOfData]);
    }
    
    #[Route(path: '/{id}/createLabel')]
    public function createLabel(Order $order, Request $request): JsonResponse {
        try {
            $requestArray = $request->toArray();
            $idShipment = $requestArray['id_shipment'];
            $orderDeliveryShipmentData = $order->getOrderDelivery()->getOrderDeliveryShipmentDataByShipmentId($idShipment);
            $response = $this->carrierService->setTrackingIdAndPrintLabels($order, $orderDeliveryShipmentData);
            $this->ruleTrigger->triggerEvent('order.label.create.success', ['order' => $order]);
            return $this->json($response);
        } catch (\Exception $exception) {
            $this->ruleTrigger->triggerEvent('order.label.create.failure', ['order' => $order]);
            $this->entityManager->persist($order);
            $this->entityManager->flush();
            throw $exception;
        }
    }
    
    #[Route('/{id}/getParcelSettings')]
    public function getOrderParcelSettings(Order $order): JsonResponse {
        return $this->json(['parcelSettings' => $this->carrierService->getParcelsSettings($order)]);
    }
    
    #[Route('/{id}/checkProductSetForOrder')]
    public function checkProductSetForOrder(Order $order, ProductSetResolver $productSetResolver): JsonResponse {
        $productSetResolver->checkProductSetForOrder($order);
        
        return $this->json($order);
    }
    
    #[Route(path: '/{id}/edit', methods: ['POST'])]
    public function orderEdit(Order $order, Request $request, Security $security): JsonResponse {
        $requestData = $request->toArray();
        if ($requestData['order']) {
            $entity = $this->denormalizer->denormalize($requestData['order'], Order::class, null, [
                AbstractNormalizer::OBJECT_TO_POPULATE => $order,
                AbstractObjectNormalizer::DEEP_OBJECT_TO_POPULATE => true
            ]);
            $this->entityManager->persist($entity);
            $this->entityManager->flush();
            $orderStatusChangeEvent = new OrderStatusChangeEvent();
            $orderStatusChangeEvent->setOrder($entity);
            $orderStatusChangeEvent->setOrderStatus($entity->getInternalStatusId());
            
            return $this->json(['order' => $entity], Response::HTTP_OK);
        }
        
        return $this->json(['message' => 'Something went wrong'], Response::HTTP_BAD_REQUEST);
    }

    #[Route(path: '/order-summary', methods: ['GET'])]
    public function orderSummary() {
        return $this->json(['message' => 'Success!', 'data' => $this->entityManager->getRepository(Order::class)->orderSummary()], Response::HTTP_OK);
    }

    #[Route(path: '/regeneratePrices', methods: ['POST'])]
    public function regeneratePrices() {
        $count = 0;
        foreach ($this->entityManager->getRepository(Order::class)->findAll() as $order) {
            $message = new OrderRenewMessage($order->getId());
            $this->messageBus->dispatch($message);
            $count++;
        }

        return $this->json(['count' => $count], Response::HTTP_OK);
    }

    #[Route(path: '/{id}/checkOrderStatusInIntegration', methods: ['POST'])]
    public function checkOrderStatusInIntegration(Order $order) {
        $message = new SingleOrderCheckStatusInPrestaShopMessage($this->entityManager->getRepository(Integration::class)->find($order->getOrderSourceId()), $order);
        $this->messageBus->dispatch($message);

        return $this->json(['message' => 'OK'], Response::HTTP_OK);
    }
}