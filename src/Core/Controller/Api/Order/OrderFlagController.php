<?php

namespace App\Core\Controller\Api\Order;

use App\Core\Entity\OrderFlag;
use App\Core\Repository\OrderFlagRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/order/flag', name: 'api_order_flag')]
class OrderFlagController extends AbstractController {

    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly EntityManagerInterface $entityManager,) {}

    #[Route('/create', name: 'api_order_flag_create', methods: ['POST'])]
    public function create(Request $request): JsonResponse {

        $orderFlagEntity = $this->serializer->denormalize($request->toArray(), OrderFlag::class);
        $this->entityManager->persist($orderFlagEntity);
        $this->entityManager->flush();

        return $this->json(['message' => 'Flag created successfully', 'status' => Response::HTTP_CREATED, 'data' => $orderFlagEntity]);
    }

    #[Route('/{id}/edit', name: 'api_order_flag_edit', methods: ['POST'])]
    public function edit(OrderFlag $flag, Request$request): JsonResponse {
        $orderFlagEntity = $this->serializer->denormalize($request->toArray(), OrderFlag::class, null, [AbstractNormalizer::OBJECT_TO_POPULATE => $flag]);
        $this->entityManager->persist($orderFlagEntity);
        $this->entityManager->flush();

        return $this->json(['message' => 'Flag edited successfully', 'status' => Response::HTTP_CREATED, 'data' => $orderFlagEntity]);
    }

    #[Route('/{id}/delete', name: 'api_order_flag_delete', methods: ['POST'])]
    public function delete(OrderFlag $flag): JsonResponse {
        $this->entityManager->remove($flag);
        $this->entityManager->flush();

        return $this->json(['message' => 'Flag deleted successfully', 'status' => Response::HTTP_CREATED, 'data' => []]);
    }

    #[Route('/findAll', name: 'api_order_flag_find_all', methods: ['GET'])]
    public function findAll(): JsonResponse {
        return $this->json(['message' => 'Flags', 'status' => Response::HTTP_OK, 'data' => $this->entityManager->getRepository(OrderFlag::class)->findAll()]);
    }
}