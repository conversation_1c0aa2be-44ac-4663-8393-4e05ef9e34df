<?php

namespace App\Core\Controller\Api\Address;


use App\Core\Service\Address\AddressSplitService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

class AddressController extends AbstractController {

    public function __construct(
        private readonly AddressSplitService $addressSplitService,
    ) {
    }

    #[Route('/api/address/split', methods: ['POST'])]
    public function splitAddress(Request $request) {
        $inputAddress = $request->toArray();
        $split = $this->addressSplitService->split($inputAddress['address']);
        return $this->json($split);
    }
}