<?php

namespace App\Core\Controller\Api\Status;

use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Entity\OrderStatusTab;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/status')]
class StatusController extends AbstractController {

    public function __construct(private readonly EntityManagerInterface $entityManager){}
    #[Route('/all', name: 'order_status_all')]
    public function all(): JsonResponse
    {
        $statuses = $this->entityManager->getRepository(OrderStatus::class)->findAll();
        foreach ($statuses as &$status) {
            $status = [
                'status' => $status,
                'orders' => $this->entityManager->getRepository(Order::class)->count(['internal_status_id' => $status])
            ];
        }

        return $this->json($statuses, Response::HTTP_OK);
    }

    #[Route('/create', name: 'order_status_create')]
    public function create(Request $request): JsonResponse {
        $data = $request->toArray();
        $newStatus = new OrderStatus();

        $newStatus->setName($data['name']);
        $newStatus->setFullName($data['fullName']);
        $newStatus->setShortName($data['shortName']);
        $newStatus->setColor($data['color']);
        if ($data['tabId']) {
            try {
                if (NULL !== ($tab = $this->entityManager->getRepository(OrderStatusTab::class)->findOneBy(['id' => $data['tabId']]))) {
                    $newStatus->setTab($tab);
                }
            } catch (\Exception $e) {}
        }

        $this->entityManager->persist($newStatus);
        $this->entityManager->flush();

        return $this->json(['message' => 'Successfully created order status tab', 'data' => $newStatus, 'status' => Response::HTTP_OK], Response::HTTP_CREATED);
    }

    #[Route('/{id}/edit', name: 'order_status_edit')]
    public function edit(OrderStatus $orderStatus, Request $request): JsonResponse {
        $data = $request->toArray();
        $orderStatus->setName($data['name']);
        $orderStatus->setFullName($data['fullName']);
        $orderStatus->setShortName($data['shortName']);
        $orderStatus->setColor($data['color']);
        if (isset($data['tabId'])) {
            $orderStatus->setTab($this->entityManager->getRepository(OrderStatusTab::class)->find($data['tabId']));
        }
        $this->entityManager->flush();

        return $this->json(['message' => 'Successfully edited order status', 'data' => $orderStatus, 'status' => Response::HTTP_OK], Response::HTTP_OK);
    }

    #[Route('/{id}/delete', name: 'order_status_delete')]
    public function delete(OrderStatus $orderStatus): JsonResponse {

        if ($this->entityManager->getRepository(Order::class)->findBy(['internal_status_id' => $orderStatus->getId()])) {
            return $this->json(['message' => 'You can not remove status when there are orders with this status', 'status' => Response::HTTP_BAD_REQUEST], Response::HTTP_BAD_REQUEST);
        }
        $this->entityManager->remove($orderStatus);
        $this->entityManager->flush();

        return $this->json(['message' => 'Successfully deleted order status', 'status' => Response::HTTP_OK], Response::HTTP_OK);
    }

    #[Route('/tab/create', name: 'order_status_tab_create')]
    public function createTab(Request $request): JsonResponse {
        $content = $request->toArray();
        $orderStatusTab = new OrderStatusTab();
        $orderStatusTab->setName($content['name']);
        $this->entityManager->persist($orderStatusTab);
        $this->entityManager->flush();

        return $this->json(['message' => 'Successfully created order status tab', 'data' => $orderStatusTab, 'status' => Response::HTTP_OK], Response::HTTP_CREATED);
    }

    #[Route('/tab/{id}/edit', name: 'order_status_tab_edit')]
    public function editTab(OrderStatusTab $orderStatusTab, Request $request): JsonResponse {
        $content = $request->toArray();
        $orderStatusTab->setName($content['name']);
        $this->entityManager->persist($orderStatusTab);
        $this->entityManager->flush();

        return $this->json(['message' => 'Successfully created order status tab', 'data' => $orderStatusTab, 'status' => Response::HTTP_OK], Response::HTTP_CREATED);
    }

    #[Route('/tab/{id}/delete', name: 'order_status_tab_edit')]
    public function deleteTab(OrderStatusTab $orderStatusTab, Request $request): JsonResponse {
        if (!$orderStatusTab->getOrderStatuses()->isEmpty()) {
            return $this->json(['message' => 'You can not remove tab when there are statuses with this tab', 'status' => Response::HTTP_BAD_REQUEST], Response::HTTP_BAD_REQUEST);
        }
        $this->entityManager->remove($orderStatusTab);
        $this->entityManager->flush();

        return $this->json(['message' => 'Successfully removed order status tab', 'status' => Response::HTTP_OK], Response::HTTP_CREATED);
    }

    #[Route('/tab/getAllTabs', name: 'order_status_tab_show_all')]
    public function getAllTabs(): JsonResponse {
        return $this->json(['message' => '', 'tabs' => $this->entityManager->getRepository(OrderStatusTab::class)->findAll()], Response::HTTP_OK);
    }
}