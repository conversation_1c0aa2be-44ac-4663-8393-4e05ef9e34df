<?php

namespace App\Core\Controller\Api\Logs;
use App\Core\Entity\Basket;
use App\Core\Entity\Log;
use App\Core\Entity\Order;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/logs')]
class LogsController extends AbstractController {

    public function __construct(private readonly EntityManagerInterface $entityManager) {

    }
    #[Route('/order/{id}', name: 'api_logs_order')]
    public function getLogsForOrder(Order $order): JsonResponse {
        $logs = $this->entityManager->getRepository(Log::class)->getLogsSortedByAction($order);

        return $this->json(['logs' => $logs], Response::HTTP_OK);
    }

    #[Route('/basket/{id}', name: 'api_logs_basket')]
    public function getLogsForBasket(Basket $basket): JsonResponse {
        $logs = $this->entityManager->getRepository(Log::class)->getLogsSortedByAction($basket);

        return $this->json(['logs' => $logs], Response::HTTP_OK);
    }
}