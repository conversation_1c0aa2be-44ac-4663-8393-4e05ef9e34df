<?php

namespace App\Core\Controller\Api\Rules;

use App\Core\Rules\RuleService;
use SCA\Rules\Entity\Rule;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/rules')]
class RuleController extends AbstractController{

    public function __construct(private readonly RuleService $ruleService) {}

    #[Route('/create', name: 'api_rules_create', methods: ['POST'])]
    public function create(Request $request): JsonResponse {
        $data = $request->toArray();

        return $this->json(['message' => 'OK', 'data' => $this->ruleService->createRule($data)], Response::HTTP_CREATED);
    }

    #[Route('/{id}/read', name: 'api_rules_read', methods: ['GET'])]
    public function read(Rule $rule): JsonResponse {
        return $this->json(['message' => 'OK', 'data' => $rule], Response::HTTP_CREATED);
    }

    #[Route('/{id}/update', name: 'api_rules_update', methods: ['POST'])]
    public function update(Rule $rule, Request $request): JsonResponse {
        $data = $request->toArray();

        return $this->json(['message' => 'OK', 'data' => $this->ruleService->editRule($rule, $data)], Response::HTTP_CREATED);
    }

    #[Route('/{id}/delete', name: 'api_rules_delete', methods: ['DELETE'])]
    public function delete(Rule $rule): JsonResponse {
        return $this->json(['message' => 'OK', 'data' => $this->ruleService->deleteRule($rule)], Response::HTTP_CREATED);
    }

    #[Route('/readAll', name: 'api_rules_read_all', methods: ['GET'])]
    public function readAll(): JsonResponse {
        return $this->json(['message' => 'OK', 'data' => $this->ruleService->readAll()], Response::HTTP_OK);
    }
    #[Route('/getAvailableEvents', name: 'api_rules_get_available_events', methods: ['GET'])]
    public function getEvents(): JsonResponse {

        $events = [];
        foreach ($this->ruleService->ruleEngine->getEvents() as $event) {
            $events[] = [
                'name' => $event->getName(),
                'label' => $event->getLabel(),
            ];
        }
        return $this->json(['message' => 'OK', 'data' => $events], Response::HTTP_OK);
    }

    #[Route('/getAvailableConditions', name: 'api_rules_get_available_conditions', methods: ['GET'])]
    public function getConditions(): JsonResponse {

        $conditions = [];

        foreach ($this->ruleService->ruleEngine->getConditions() as $condition) {
            $conditions[] = [
                'name' => $condition->getName(),
                'label' => $condition->getLabel(),
                'allowedValues' => $condition->getSettings(),
            ];
        }

        $operators = [
            [
                'name' => 'operator',
                'type' => 'select',
                'label' => 'Warunek',
                'options' => [
                    [
                        'value' => '===',
                        'label' => 'równe',
                    ],
                    [
                        'value' => '!==',
                        'label' => 'inne',
                    ],
                    [
                        'value' => '>',
                        'label' => 'większe'
                    ],
                    [
                        'value' => '>=',
                        'label' => 'większe, lub równe'
                    ],
                    [
                        'value' => '<',
                        'label' => 'mniejsze',
                    ],
                    [
                        'value' => '<=',
                        'label' => 'mniejsze, lub równe'
                    ]
                ]
            ]
        ];
        return $this->json(['message' => 'OK', 'data' => ['condition' => $conditions, 'operators' => $operators]], Response::HTTP_OK);
    }

    #[Route('/getAvailableActions', name: 'api_rules_get_available_actions', methods: ['GET'])]
    public function getActions(): JsonResponse {
        $actions = [];

        foreach ($this->ruleService->ruleEngine->getActions() as $action) {
            $actions[] = [
                'name' => $action->getName(),
                'label' => $action->getLabel(),
                'allowedValues' => $action->getAllowedValues(),
            ];
        }
        return $this->json(['message' => 'OK', 'data' => $actions], Response::HTTP_OK);
    }
}