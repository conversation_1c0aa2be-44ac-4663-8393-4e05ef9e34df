<?php

namespace App\Core\Controller\Api\Basket;

use App\Core\Entity\Basket;
use App\Core\Entity\BasketOrder;
use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Service\Basket\BasketOrderService;
use App\Core\Service\Basket\BasketService;
use App\Core\Taxonomy\BasketTaxonomy;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route(['/api/basket', '/basket'])]
class BasketController extends AbstractController {

    public function __construct(private readonly EntityManagerInterface $entityManager) {}

    #[Route('/new')]
    public function new(): JsonResponse {
        return $this->json(['Creating a basket via API is deprecated, use fixtures instead!'], Response::HTTP_OK);
    }

    #[Route('/view/{id}/basket')]
    public function view(Basket $basket): JsonResponse {
        $basketOrders = $this->entityManager->getRepository(BasketOrder::class)->findBy(['basket_id' => $basket->getId()]);
        $return = [
            'basket' => $basket,
            'basket_orders' => $basketOrders,
        ];

        return $this->json($return, Response::HTTP_OK);
    }

    #[Route('/view/all')]
    public function viewAll(): JsonResponse {
        $baskets = $this->entityManager->getRepository(Basket::class)->findAll('asd');

        return $this->json($baskets, Response::HTTP_OK);
    }

    #[Route('/assign/{id}')]
    public function assignBasket(Basket $basket): JsonResponse {
        $basket->setUser($this->getUser());
        $this->entityManager->persist($basket);
        $this->entityManager->flush();

        return $this->json($basket, Response::HTTP_OK);
    }

    #[Route('/{id}/removeAllOrders')]
    public function clearBasket(Basket $basket): JsonResponse {
        try {
            $basketOrders = $this->entityManager->getRepository(BasketOrder::class)->findBy(['basket_id' => $basket->getId()]);
            $removedOrders = 0;
            foreach ($basketOrders as $basketOrder) {
                $removedOrders++;
                $order = $basketOrder->getOrder();
                $order->setInternalStatusId($this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_REMOVED_FROM_BASKET]));
                $this->entityManager->persist($order);
                $this->entityManager->remove($basketOrder);
            }
            $basket->setStatusId(BasketTaxonomy::STATUS_EMPTY);
            $basket->setUser(null);
            $this->entityManager->flush();

            return $this->json(['removedOrders' => $removedOrders], Response::HTTP_OK);
        } catch (\Exception $exception) {
            return $this->json(['error' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/unassign/{id}')]
    public function unassignBasket(Basket $basket): JsonResponse {
        $basket->setUser(null);
        $this->entityManager->persist($basket);
        $this->entityManager->flush();

        return $this->json($basket, Response::HTTP_OK);
    }

    #[Route('/view/packing')]
    public function viewReadyToPackingBaskets(): JsonResponse {
        $baskets = $this->entityManager->getRepository(Basket::class)->findBy(['status_id' => BasketTaxonomy::STATUS_PACKING]);

        return $this->json($baskets, Response::HTTP_OK);
    }

    #[Route('/view/collecting')]
    public function viewCollectingBaskets(): JsonResponse {
        $baskets = $this->entityManager->getRepository(Basket::class)->findBy(['status_id' => [BasketTaxonomy::STATUS_EMPTY, BasketTaxonomy::STATUS_COMPLETING]]);

        return $this->json($baskets, Response::HTTP_OK);
    }

    #[Route('/view/{id}/packing')]
    public function viewPackingBasket(Basket $basket): JsonResponse {
        if (BasketTaxonomy::STATUS_PACKING !== $basket->getStatusId()) {
            throw new \Exception('This basket is not ready to be packed');
        }
        $basketOrders = $this->entityManager->getRepository(BasketOrder::class)->findBy(['basket_id' => $basket->getId()]);
        if (empty($basketOrders)) {
            $basket->setStatusId(BasketTaxonomy::STATUS_EMPTY);
            $basket->setUser(null);
        }
        $basket->setUser($this->getUser());
        $this->entityManager->persist($basket);
        $this->entityManager->flush();
        $return = [
            'basket' => $basket,
            'basket_orders' => $basketOrders
        ];

        return $this->json($return, Response::HTTP_OK);
    }

    #[Route('/{id}/finishCollecting')]
    public function finishCollecting(Basket $basket, BasketService $basketService): JsonResponse {
        $basketService->finishCollecting($basket, $this->getUser());

        return $this->json($basket, Response::HTTP_OK);
    }

    #[Route('/{id}/assignOrder/{order}')]
    public function assignOrderToBasket(Basket $basket, Order $order,  BasketOrderService $basketOrderService): JsonResponse {
        $basketOrders = $this->entityManager->getRepository(BasketOrder::class)->findBy(['order' => $order]);
        if (!empty($basketOrders)) {
            return $this->json(['message' => 'Can not assign order to basket! Order was assigned before'], Response::HTTP_BAD_REQUEST);
        }
        if ($basket->isPacking()) {
            return $this->json(['message' => 'Can not assign order to basket! Basket is in status ' . BasketTaxonomy::STATUS_PACKING], Response::HTTP_BAD_REQUEST);
        }

        $basketOrderService->assignOrderToBasket($order, $basket, $this->getUser());

        return $this->json($basket, Response::HTTP_OK);
    }

    #[Route('/{id}/reassignOrder/{order}')]
    public function reassignOrderToBasket(Basket $basket, Order $order,  BasketOrderService $basketOrderService): JsonResponse {
        $basketOrders = $this->entityManager->getRepository(BasketOrder::class)->findBy(['order' => $order]);
        if (empty($basketOrders)) {
            return $this->json(['message' => 'Can not reassign - use assign instead'], Response::HTTP_BAD_REQUEST);
        }
        if ($basket->isPacking()) {
            return $this->json(['message' => 'Can not assign order to basket! Basket is in status ' . BasketTaxonomy::STATUS_PACKING], Response::HTTP_BAD_REQUEST);
        }

        $basketOrderService->reassignOrderToBasket($order, $basket, $this->getUser());

        return $this->json($basket, Response::HTTP_OK);
    }

}