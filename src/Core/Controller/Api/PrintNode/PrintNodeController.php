<?php

namespace App\Core\Controller\Api\PrintNode;


use App\Core\Service\PrintNode\Enum\PrintNodeContentTypeEnum;
use App\Core\Service\PrintNode\PrintNodeGetService;
use App\Core\Service\PrintNode\PrintNodePostService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/printnode')]
class PrintNodeController extends AbstractController {

    public function __construct(
        private readonly PrintNodeGetService  $printNodeGetService,
        private readonly PrintNodePostService $printNodePostService,

    ) {}

    #[Route('/whoami', name: 'api_print-node_whoami', methods: ['GET'])]
    public function whoAmI(): JsonResponse {
        return $this->json($this->printNodeGetService->getWhoAmI());
    }

    #[Route('/printers', name: 'api_print-node_printers', methods: ['GET'])]
    public function printers(): JsonResponse {
        return $this->json($this->printNodeGetService->getPrinters());
    }

    #[Route('/printers/print', name: 'api_print-node_printers_print', methods: ['POST'])]
    public function createPrintJob(Request $request): JsonResponse {
        $data = $request->toArray();

        if (!isset($data['contentType'], $data['content'])) {
            return $this->json(['error' => 'Missing required fields'], Response::HTTP_BAD_REQUEST);
        }
        if (!PrintNodeContentTypeEnum::tryFrom($data['contentType'])) {
            return $this->json('Invalid content type', Response::HTTP_BAD_REQUEST);
        }

        try {
            $result = $this->printNodePostService->print(
                $data['printerId'],
                $data['contentType'],
                $data['content'],
                $data['title'] ?? ''
            );

            return $this->json($result);
        } catch (\Exception $exception) {
            return $this->json(['error' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/printers/{id}', name: 'api_print-node_printer', methods: ['GET'])]
    public function printer($id): JsonResponse {
        return $this->json($this->printNodeGetService->getPrinter($id));
    }

    #[Route('/printers/{id}/state', name: 'api_print-node_printers_state', methods: ['GET'])]
    public function printerState($id): JsonResponse {
        return $this->json($this->printNodeGetService->getPrinterState($id));
    }

    #[Route('/getPrinterByUser', name: 'api_print-node_printers_user', methods: ['GET'])]
    public function getPrinterByUser(): JsonResponse {
        return $this->json($this->printNodeGetService->getPrinterByUser());
    }
}
