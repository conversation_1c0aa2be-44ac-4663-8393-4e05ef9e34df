<?php

namespace App\Core\Controller\Api\PdfCreator;

use App\Core\Entity\Order;
use App\Core\Service\PdfCreatorService\PdfReaderService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Attribute\Route;

class PdfCreatorReaderController extends AbstractController
{
    public function __construct(
        private readonly PdfReaderService $pdfReaderService,
        private readonly EntityManagerInterface $entityManager
    ){}

    #[Route('/api/pdf-creator', name: 'api_pdf_creator', methods: ['POST'])]
    public function getPdf(Request $request): BinaryFileResponse
    {
        $requestArray = $request->toArray();
        if (!isset($requestArray['order_id'])){
            throw $this->createNotFoundException('Order not found');
        }
        $order = $this->entityManager->getRepository(Order::class)->find($requestArray['order_id']);
        if(!$order){
            throw $this->createNotFoundException('Order not found');
        }
        $pdfFilePath = $this->pdfReaderService->getPdf($order);

        $response = new BinaryFileResponse($pdfFilePath);
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            str_replace(['/', '\\',' '], '-', $order->getOrderInvoice()->getInvoiceNumber()) . '.pdf'
        );

        return $response;
    }

}