<?php

namespace App\Core\Controller\Api\Integration;

use App\Core\Entity\Integration;
use App\Core\Integration\IntegrationService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/integration')]
class IntegrationController extends AbstractController {

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly IntegrationService $integrationService
    ) {}

    #[Route('/all')]
    public function viewAll(EntityManagerInterface $entityManager): JsonResponse {
        $integrations = $entityManager->getRepository(Integration::class)->findAll();

        return $this->json($integrations, Response::HTTP_OK);
    }

    #[Route('/getBy')]
    public function viewPrestashop(Request $request, EntityManagerInterface $entityManager): JsonResponse {
        $type = $request->get('type');
        if (NULL === $type) {
            return $this->json(['Type not found']);
        }
        $integrations = $entityManager->getRepository(Integration::class)->findBy(['type' => $type]);
        $response = [];
        if ('baselinker' === $type) {
            foreach ($integrations as $key => $integration) {
                if (NULL !== $response[$key]['name'] = $integration->getSettingsByKey('INTERNAL_NAME')) {
                    $response[$key]['id'] = $integration->getId();
                    $response[$key]['type'] = $integration->getType();
                    $response[$key]['name'] = $integration->getSettingsByKey('INTERNAL_NAME');
                }
            }
        } else {
            $response = $integrations;
        }

        return $this->json($response, Response::HTTP_OK);
    }

    #[Route('/getTypes')]
    public function getTypes(IntegrationService $integrationService): JsonResponse {
        return $this->json(['types' => $integrationService->getAllTypes()], Response::HTTP_OK);
    }

    #[Route('/getIntegrationSourceName')]
    public function getIntegrationSourceName(): JsonResponse {
        $integrations = $this->entityManager->getRepository(Integration::class)->findAll();
        $response = [];
        foreach ($integrations AS $key => $integration){
            if('baselinker' === $integration->getType()){
                if(NULL !== $integration->getSettingsByKey('INTERNAL_NAME')){
                    $response[$key]['id'] = $integration->getId();
                    $response[$key]['type'] = $integration->getType();
                    $response[$key]['name'] = $integration->getSettingsByKey('INTERNAL_NAME');
                }
            } else {
                $response[$key]['id'] = $integration->getId();
                $response[$key]['type'] = $integration->getType();
                $response[$key]['name'] = $integration->getName();
            }
        }
        return $this->json(['sources' => $response], Response::HTTP_OK);
    }

    #[Route('/create', name: 'api_integration_create', methods: ['POST'])]
    public function create(Request $request): JsonResponse {
        $data = $request->toArray();

        return $this->json(['message' => 'OK', 'data' => $this->integrationService->create($data)], Response::HTTP_CREATED);
    }

    #[Route('/{id}/read', name: 'api_integration_read', methods: ['GET'])]
    public function read(Integration $integration): JsonResponse {

        return $this->json(['message' => 'OK', 'data' => $integration], Response::HTTP_OK);
    }

    #[Route('/{id}/update', name: 'api_integration_update', methods: ['POST'])]
    public function update(Integration $integration, Request $request): JsonResponse {
        $data = $request->toArray();

        return $this->json(['message' => 'OK', 'data' => $this->integrationService->update($integration, $data)], Response::HTTP_CREATED);
    }

    #[Route('/{id}/delete', name: 'api_integration_delete', methods: ['DELETE'])]
    public function delete(Integration $integration): JsonResponse {
        return $this->json(['message' => 'OK', 'data' => $this->integrationService->delete($integration)], Response::HTTP_OK);
    }
}