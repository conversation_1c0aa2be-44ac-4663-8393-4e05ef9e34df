<?php

namespace App\Core\Controller\Api\Accountant;

use App\Core\Service\Accountant\AccountantManager;
use App\Core\Service\Accountant\Types\Account\AccountInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/accountant')]
class AccountantController extends AbstractController {
    #[Route('/add', methods: ['POST'])]
    public function addAccount(Request $request, AccountantManager $accountantManager): JsonResponse {
        $data = $request->toArray();

        return $this->json(['carrier' => $accountantManager->createAccount($data)]);
    }

    #[Route('/{id}/edit', methods: ['POST'])]
    public function editCarrier(AccountInterface $account, Request $request, AccountantManager $accountantManager): JsonResponse {
        $data = $request->toArray();
        $accountantManager->updateAccount($account, $data);

        return $this->json(['account' =>$account]);
    }

    #[Route('/{id}/delete', methods: ['POST'])]
    public function deleteAccount(AccountInterface $account, AccountantManager $accountantManager) {
        $accountantManager->deleteAccount($account);

        return $this->json(['account' => 'deleted']);
    }

    #[Route('/getAccountsByType', methods: ['GET'])]
    public function getAccountantAccountsByType(AccountantManager $accountantManager, Request $request): JsonResponse {
        return $this->json(['accounts' => $accountantManager->getAccountantAccountsByType($request->toArray()['type'])]);
    }

    #[Route('/getAllAccounts', methods: ['GET'])]
    public function getAccountantAccounts(AccountantManager $accountantManager): JsonResponse {
        return $this->json(['accounts' => $accountantManager->getAccountantAccounts()]);
    }

    #[Route('/getAccountTypes', methods: ['GET'])]
    public function getAccountTypes(AccountantManager $accountantManager): JsonResponse {
        return $this->json(['types' => $accountantManager->getAccountantTypes()]);
    }
}