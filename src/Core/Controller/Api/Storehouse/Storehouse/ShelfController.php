<?php

namespace App\Core\Controller\Api\Storehouse\Storehouse;

use App\Core\Entity\Ean;
use App\Core\Entity\EanShelfQuantity;
use App\Core\Entity\Rack;
use App\Core\Entity\Shelf;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/shelf')]

class ShelfController extends AbstractController {

    public function __construct(private readonly SerializerInterface $serializer, private readonly EntityManagerInterface $entityManager) {}

    #[Route('/create', methods: ['POST'])]
    public function create(Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['shelf'])) {
            if (NULL === ($rack = $this->entityManager->getRepository(Rack::class)->find($data['shelf']['rack']))) {
                return $this->json(['message' => 'Bad request! Rack does not exists!', Response::HTTP_BAD_REQUEST]);
            }
            $newShelf = $this->serializer->denormalize($data['shelf'], Shelf::class, 'array');
            $newShelf->setRack($rack);
            $this->entityManager->persist($newShelf);
            $this->entityManager->flush();

            return $this->json(['message' => 'Successfully created', 'shelf' => $newShelf], Response::HTTP_CREATED);
        }

        return $this->json(['message' => 'Bad request', Response::HTTP_BAD_REQUEST]);
    }

    #[Route('/update/{id}', methods: ['POST'])]
    public function update(Shelf $shelf, Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['shelf'])) {
            $updatedShelf = $this->serializer->denormalize($data['rack'], Rack::class, 'array', [AbstractNormalizer::OBJECT_TO_POPULATE => $shelf]);
            if (isset($data['shelf']['rack'])) {
                $rack = $this->entityManager->getRepository(Rack::class)->find($data['shelf']['rack']);
                if (NULL === $rack) {
                    return $this->json(['message' => 'Bad request! Rack does not exists!', Response::HTTP_BAD_REQUEST]);
                }

                $updatedShelf->setRackNo($rack);
            }
            $this->entityManager->persist($updatedShelf);
            $this->entityManager->flush();

            return $this->json(['message' => 'Successfully updated', 'shelf' => $updatedShelf, Response::HTTP_ACCEPTED]);
        }

        return $this->json(['message' => 'Bad request', Response::HTTP_BAD_REQUEST]);
    }

    #[Route('/remove/{id}', methods: ['POST'])]
    public function remove(Shelf $shelf): JsonResponse {
        $this->entityManager->remove($shelf);
        $this->entityManager->flush();

        return $this->json(['message' => 'Successfully removed'], Response::HTTP_ACCEPTED);
    }

    #[Route('/list', methods: ['GET'])]
    public function list(): JsonResponse {
        $return = [];
        foreach ($this->entityManager->getRepository(Shelf::class)->findAll() as $shelf) {
            $return[] = [
                'id' => $shelf->getId()->toRfc4122(),
                'shelfNo' => $shelf->getShelfNo(),
                'type' => $shelf->getType(),
                'ean13' => $shelf->getEan13(),
                'rack' => [
                    'id' => $shelf->getRack()->getId()->toRfc4122(),
                    'rackNo' => $shelf->getRack()->getRackNo()
                ],
                'storehouse' => [
                    'id' => $shelf->getRack()->getStorehouse()->getId()->toRfc4122()
                ],
            ];
        }
        return $this->json(['message' => 'success', 'data' => ['shelves' => $return]], Response::HTTP_OK);
    }

    #[Route('/scan', methods: ['POST'])]
    public function scan(Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['shelfNo'])) {
            if (NULL === ($shelf = $this->entityManager->getRepository(Shelf::class)->findOneBy(['shelf_no' => $data['shelfNo']]))) {
                return $this->json(['message' => 'Shelf not found!', Response::HTTP_BAD_REQUEST]);
            }
            $products = [];
            foreach ($this->entityManager->getRepository(EanShelfQuantity::class)->findBy(['shelf' => $shelf]) as $eanShelfQuantity) {
                $product = $eanShelfQuantity->getEan()->getProduct();
                $eans = [];
                foreach ($this->entityManager->getRepository(Ean::class)->findBy(['product' => $product]) as $ean) {
                    $eans[] = [
                        'id' => $ean->getId()->toRfc4122(),
                        'ean' => $ean->getEan(),
                    ];
                }
                $products[] = [
                    'id' => $product->getId()->toRfc4122(),
                    'name' => $product->getName(),
                    'sku' => $product->getSku(),
                    'eans' => $eans
                ];
            }

            return $this->json(['message' => 'Success', 'data' => ['products' => $products]]);
        }

        return $this->json(['message' => 'Bad request!', 'data' => []]);
    }
}