<?php

namespace App\Core\Controller\Api\Storehouse\Storehouse;

use App\Core\Entity\Storehouse;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/storehouse')]

class StorehouseController extends AbstractController {

    public function __construct(private readonly SerializerInterface $serializer, private readonly EntityManagerInterface $entityManager) {}

    #[Route('/create', methods: ['POST'])]
    public function create(Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['storehouse'])) {
            $newStorehouse = $this->serializer->denormalize($data['storehouse'], Storehouse::class, 'array');
            $this->entityManager->persist($newStorehouse);
            $this->entityManager->flush();

            return $this->json(['message' => 'Successfully created', 'storehouse' => $newStorehouse], Response::HTTP_CREATED);
        }

        return $this->json(['message' => 'Bad request', Response::HTTP_BAD_REQUEST]);
    }

    #[Route('/update/{id}', methods: ['POST'])]
    public function update(Storehouse $storehouse, Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['storehouse'])) {
            $updatedStorehouse = $this->serializer->denormalize($data['storehouse'], Storehouse::class, 'array', [AbstractNormalizer::OBJECT_TO_POPULATE => $storehouse]);
            $this->entityManager->persist($updatedStorehouse);
            $this->entityManager->flush();

            return $this->json(['message' => 'Successfully updated', 'storehouse' => $updatedStorehouse, Response::HTTP_ACCEPTED]);
        }

        return $this->json(['message' => 'Bad request', Response::HTTP_BAD_REQUEST]);
    }

    #[Route('/remove/{id}', methods: ['POST'])]
    public function remove(Storehouse $storehouse): JsonResponse {
        $this->entityManager->remove($storehouse);
        $this->entityManager->flush();
        return $this->json(['message' => 'Successfully removed'], Response::HTTP_ACCEPTED);
    }

    #[Route('/list', methods: ['GET'])]
    public function list(): JsonResponse {
        return $this->json(['storehouses' => $this->entityManager->getRepository(Storehouse::class)->findAll()], Response::HTTP_OK, [], [AbstractNormalizer::IGNORED_ATTRIBUTES => ['racks']]);
    }

}