<?php

namespace App\Core\Controller\Api\Storehouse\Storehouse;

use App\Core\Entity\Ean;
use App\Core\Entity\Rack;
use App\Core\Entity\Storehouse;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/rack')]

class RackController extends AbstractController {

    public function __construct(private readonly SerializerInterface $serializer, private readonly EntityManagerInterface $entityManager) {}

    #[Route('/create', methods: ['POST'])]
    public function create(Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['rack'])) {
            if (NULL === ($storehouse = $this->entityManager->getRepository(Storehouse::class)->find($data['rack']['storehouse']))) {
                return $this->json(['message' => 'Bad request! Storehouse does not exists!', Response::HTTP_BAD_REQUEST]);
            }
            $newRack = $this->serializer->denormalize($data['rack'], Rack::class, 'array');
            $newRack->setStorehouse($storehouse);
            $this->entityManager->persist($newRack);
            $this->entityManager->flush();

            return $this->json(['message' => 'Successfully created', 'rack' => $newRack], Response::HTTP_CREATED);
        }

        return $this->json(['message' => 'Bad request', Response::HTTP_BAD_REQUEST]);
    }

    #[Route('/update/{id}', methods: ['POST'])]
    public function update(Rack $rack, Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['rack'])) {
            $updatedRack = $this->serializer->denormalize($data['rack'], Rack::class, 'array', [AbstractNormalizer::OBJECT_TO_POPULATE => $rack]);
            if (isset($data['shelf']['storehouse'])) {
                $storehouse = $this->entityManager->getRepository(Storehouse::class)->find($data['shelf']['storehouse']);
                if (NULL === $storehouse) {
                    return $this->json(['message' => 'Bad request! Rack does not exists!', Response::HTTP_BAD_REQUEST]);
                }

                $updatedRack->setStorehouse($storehouse);
            }

            $this->entityManager->persist($updatedRack);
            $this->entityManager->flush();

            return $this->json(['message' => 'Successfully updated', 'rack' => $updatedRack, Response::HTTP_ACCEPTED]);
        }

        return $this->json(['message' => 'Bad request', Response::HTTP_BAD_REQUEST]);
    }

    #[Route('/remove/{id}', methods: ['POST'])]
    public function remove(Rack $rack): JsonResponse {
        $this->entityManager->remove($rack);
        $this->entityManager->flush();

        return $this->json(['message' => 'Successfully removed'], Response::HTTP_ACCEPTED);
    }

    #[Route('/list', methods: ['GET'])]
    public function list(): JsonResponse {
        return $this->json(['racks' => $this->entityManager->getRepository(Rack::class)->findAll()], Response::HTTP_OK, [], [AbstractNormalizer::IGNORED_ATTRIBUTES => ['storehouse', 'shelves']]);
    }

    #[Route('/scan', methods: ['POST'])]
    public function scan(Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['rackNo'])) {
            if (NULL === ($rack = $this->entityManager->getRepository(Rack::class)->findOneBy(['rack_no' => $data['rackNo']]))) {
                return $this->json(['message' => 'Rack not found!', Response::HTTP_BAD_REQUEST]);
            }

            $products = [];
            foreach ($rack->getShelves() as $shelf) {
                foreach ($shelf->getEanShelfQuantities() as $eanShelfQuantity) {
                    $products[] = $eanShelfQuantity->getEan()->getProduct();
                }
            }

            return $this->json(['message' => 'Success', 'data' => ['products' => $products]]);
        }

        return $this->json(['message' => 'Bad request!', 'data' => []]);
    }
}