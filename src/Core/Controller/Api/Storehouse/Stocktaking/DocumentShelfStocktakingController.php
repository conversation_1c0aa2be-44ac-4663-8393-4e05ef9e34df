<?php

namespace App\Core\Controller\Api\Storehouse\Stocktaking;

use App\Core\Entity\DocumentShelfStocktaking;
use App\Core\Entity\DocumentShelfStocktakingData;
use App\Core\Entity\DocumentStocktaking;
use App\Core\Entity\Ean;
use App\Core\Entity\EanShelfQuantity;
use App\Core\Entity\Product;
use App\Core\Entity\Shelf;
use App\Core\Service\Storehouse\Stocktaking\StocktakingService;
use App\Core\Service\Subiekt\Products\SubiektProductsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/stocktaking/document/shelf')]

class DocumentShelfStocktakingController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly StocktakingService $stocktakingService,
        private readonly SubiektProductsService $subiektProductsService

    ) {}

    #[Route('/scan')]
    public function scan(Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['scannedNo'])) {
            $data = $this->stocktakingService->returnScannedData($data['scannedNo']);

            return $this->json($data, Response::HTTP_OK);
        }
        return $this->json(['message' => 'No scan number provided!'], Response::HTTP_BAD_REQUEST);
    }

    #[Route('/create')]
    public function create(Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['shelfNo'])) {
            if (NULL === ($shelf = $this->entityManager->getRepository(Shelf::class)->findOneBy(['shelf_no' => $data['shelfNo']]))) {
                return $this->json(['message' => 'Shelf not found!', Response::HTTP_NOT_FOUND]);
            }
            if (NULL !== ($documentShelfStocktaking = $this->entityManager->getRepository(DocumentShelfStocktaking::class)->findOneBy(['shelf' => $shelf, 'finished' => FALSE]))) {
                $products = $this->stocktakingService->getProductFromStock($documentShelfStocktaking->getStock());
                $returnData = ['products' => $products, 'shelfDocument' => $documentShelfStocktaking->getId()];
                if (NULL !== $documentShelfStocktaking->getDocumentStocktaking()) {
                    $returnData['storehouseDocument'] = $documentShelfStocktaking->getDocumentStocktaking()->getId();
                }
                return $this->json($returnData, Response::HTTP_OK);
            }
            $products = $this->stocktakingService->getProductsFromShelfQuantities($shelf->getEanShelfQuantities());
            $documentShelfStocktaking = new DocumentShelfStocktaking();
            $documentShelfStocktaking->setShelf($shelf);

            foreach ($products as $product) {
                $shelfStockingData = new DocumentShelfStocktakingData();
                $shelfStockingData->setEan($product['ean']);
                $shelfStockingData->setQuantity(0);
                $shelfStockingData->setOldQuantity($product['quantity']);
                $documentShelfStocktaking->addStock($shelfStockingData);
            }
            if (NULL !== ($documentStocktaking = $this->entityManager->getRepository(DocumentStocktaking::class)->findOneBy(['storehouse' => $shelf->getRack()->getStorehouse(), 'finished' => FALSE]))) {
                $documentShelfStocktaking->setDocumentStocktaking($documentStocktaking);
            }
            $this->entityManager->persist($documentShelfStocktaking);
            $this->entityManager->flush();

            $returnData = ['products' => $products, 'shelfDocument' => $documentShelfStocktaking->getId()];
            if (NULL !== $documentShelfStocktaking->getDocumentStocktaking()) {
                $returnData['storehouseDocument'] = $documentShelfStocktaking->getDocumentStocktaking()->getId();
            }
            return $this->json($returnData, Response::HTTP_CREATED);
        }

        return $this->json(['message' => 'No shelf provided!'], Response::HTTP_BAD_REQUEST);
    }

    #[Route('/{id}/addQuantity')]
    public function addQuantity(DocumentShelfStocktaking $documentShelfStocktaking, Request $request): JsonResponse {
        if ($documentShelfStocktaking->isFinished()) {
            return $this->json(['message' => 'You can\'t add quantity to finished document!'], Response::HTTP_BAD_REQUEST);
        }
        $data = $request->toArray();
        if (!isset($data['products'])) {
            return $this->json(['message' => 'No product provided!'], Response::HTTP_BAD_REQUEST);
        }
        $products = [];
        $eansNotFound = [];
        foreach ($data['products'] as $eanToSearch => $quantity) {
            $stocktakingData = $documentShelfStocktaking->getStock()->filter(function($stock) use ($eanToSearch) {
                $eanFromStock = $stock->getEan()->getEan();
                return $eanFromStock === (string) $eanToSearch;
            })->first();
            if (!$stocktakingData) {

                if (NULL === ($ean = $this->stocktakingService->createProduct($eanToSearch, $documentShelfStocktaking->getShelf()))) {
                    $eansNotFound[] = $eanToSearch;
                    continue;
                }
                $stocktakingData = new DocumentShelfStocktakingData();
                $stocktakingData->setEan($ean);
                $stocktakingData->setQuantity($quantity);
                $documentShelfStocktaking->addStock($stocktakingData);
            }
            $stocktakingData->setQuantity($quantity);
            $this->entityManager->persist($stocktakingData);
            $products[] = [
                'name' => $stocktakingData->getEan()->getProduct()->getName(),
                'ean' => $stocktakingData->getEan()->getEan(),
                'quantity' => $stocktakingData->getQuantity(),
                'oldQuantity' => $stocktakingData->getOldQuantity()
            ];
        }
        $this->entityManager->flush();

        $returnData = ['products' => $products, 'shelfDocument' => $documentShelfStocktaking->getId()];
        if (!empty($eansNotFound)) {
            $returnData['eansNotFound'] = $eansNotFound;
        }
        if (NULL !== $documentShelfStocktaking->getDocumentStocktaking()) {
            $returnData['storehouseDocument'] = $documentShelfStocktaking->getDocumentStocktaking()->getId();
        }
        return $this->json($returnData, Response::HTTP_OK);
    }



    #[Route('/{id}/finish')]
    public function finish(DocumentShelfStocktaking $documentShelfStocktaking): JsonResponse {
        return $this->json(['message' => 'Success', 'result' => $this->stocktakingService->finishShelfDocument($documentShelfStocktaking)], Response::HTTP_OK);
    }
}