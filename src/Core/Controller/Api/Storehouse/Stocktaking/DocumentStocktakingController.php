<?php

namespace App\Core\Controller\Api\Storehouse\Stocktaking;
use App\Core\Entity\DocumentStocktaking;
use App\Core\Entity\Storehouse;
use App\Core\Service\Storehouse\Stocktaking\StocktakingService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;


#[Route('/api/stocktaking/document/storehouse')]
class DocumentStocktakingController extends AbstractController {

    public function __construct(private readonly EntityManagerInterface $entityManager, private readonly StocktakingService $stocktakingService) {}

    #[Route('/create')]
    public function create(Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['storehouse'])) {
            if (NULL !== $documentStocktaking = $this->entityManager->getRepository(DocumentStocktaking::class)->findOneBy(['storehouse' => $data['storehouse'], 'finished' => FALSE])) {
                return $this->json(['document' => $documentStocktaking], Response::HTTP_OK);
            }
            $documentStocktaking = new DocumentStocktaking();
            $documentStocktaking->setStorehouse($this->entityManager->getRepository(Storehouse::class)->find($data['storehouse']));
            $this->entityManager->persist($documentStocktaking);
            $this->entityManager->flush();

            return $this->json(['document' => $documentStocktaking], Response::HTTP_CREATED);
        }

        return $this->json(['message' => 'No storehouse provided!'], Response::HTTP_BAD_REQUEST);
    }

    #[Route('/{id}/finish')]
    public function finish(DocumentStocktaking $documentStocktaking): JsonResponse {
        return $this->json(['message' => 'Success', 'results' => $this->stocktakingService->finishMainDocument($documentStocktaking)], Response::HTTP_OK);
    }
}