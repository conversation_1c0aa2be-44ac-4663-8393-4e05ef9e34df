<?php

namespace App\Core\Controller\Api\Storehouse\Product;


use App\Core\Entity\Order;
use App\Core\Service\ReservationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/storehouse/reservation')]
class ReservationProductController extends AbstractController {
    public function __construct(
        private readonly ReservationService $reservationService,
    ) {}

    #[Route('/unReservedOrder/{order}', name: 'reservation_unreserved')]
    public function unReserveOrder(Order $order): JsonResponse {
        $this->reservationService->unReserveOrder($order,true);
        return $this->json(['message' => 'Order unreserved'], 200);
    }
}