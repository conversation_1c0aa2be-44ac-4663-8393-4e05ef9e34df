<?php

namespace App\Core\Controller\Api\Storehouse\Product;

use App\Core\Entity\Ean;
use App\Core\Entity\EanShelfQuantity;
use App\Core\Entity\Order;
use App\Core\Entity\Product;
use App\Core\Entity\Shelf;
use App\Core\Service\ShelfService;
use App\Core\Service\Storehouse\Stocktaking\StocktakingService;
use App\Core\Service\Subiekt\Products\SubiektProductsService;
use App\Core\Validator\Ean13Validator;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/storehouse/product')]
class ProductController extends AbstractController {

    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly EntityManagerInterface $entityManager,
        private readonly SubiektProductsService $subiektProductsService,
        private readonly StocktakingService $stocktakingService,
    ) {}


    #[Route('/checkfckinproducts')]
    public function checkfckinproducts() {
        $dir = $this->getParameter('kernel.project_dir');
        $fileToRead = 'migration2.json';
        $filePath = $dir  . '/' . $fileToRead;
        $this->stocktakingService->checkFckingProducts($filePath);

        return $this->json([]);
    }
    #[Route('/rearrangeProducts', methods: ['POST'])]
    public function rearrangeProducts(): JsonResponse {
        $duplicatedEansCounter = 0;
        $duplicatedProductsCounter = 0;
        $duplicates = $this->entityManager->getRepository(Product::class)->findDuplicatesBySku();
        foreach ($duplicates as $duplicate) {
            $products = $this->entityManager->getRepository(Product::class)->findBy(['sku' => $duplicate['sku']]);
            $mainProduct = array_shift($products);

            foreach ($products as $product) {
                $duplicatedProductsCounter++;
                $duplicatedEans = $this->entityManager->getRepository(Ean::class)->findBy(['product' => $product]);
                $mainEan = array_shift($duplicatedEans);
                foreach ($duplicatedEans as $duplicatedEan) {
                    $duplicatedEansCounter++;
                    foreach ($duplicatedEan->getEanShelfQuantities() as $esq) {
                        $mainEan->addEanShelfQuantity($esq);
                    }
                    $this->entityManager->remove($duplicatedEan);
                }
                $mainProduct->addEan($mainEan);
                $this->entityManager->remove($product);
            }
            $this->entityManager->persist($mainProduct);

        }
        $this->entityManager->flush();

        return $this->json(['duplicatedEans' => $duplicatedEansCounter, 'duplicationProductsCounter' => $duplicatedProductsCounter]);
    }

    #[Route('/migrateStock')]
    public function migrateStocks(): JsonResponse {
        $dir = $this->getParameter('kernel.project_dir');
        $fileToRead = 'migrationdata.json';
        $filePath = $dir  . '/' . $fileToRead;
        $count = 0;
        if (file_exists($filePath)) {
            $count = $this->stocktakingService->migrateStock($filePath);
        }

        return $this->json(['message' => 'Generated messages', 'count' => $count]);
    }

    #[Route('/loadShelves')]
    public function loadShelves(): JsonResponse {
        $dir = $this->getParameter('kernel.project_dir');
        $fileToRead = 'goodshelf.json';
        $filePath = $dir  . '/' . $fileToRead;
        $this->stocktakingService->loadShelves($filePath);

        return $this->json(['It\'s all ok']);
    }

    #[Route('/mapShelves')]
    public function mapShelves(): JsonResponse {
        $dir = $this->getParameter('kernel.project_dir');
        $fileToRead = 'shelf.json';
        $filePath = $dir  . '/' . $fileToRead;
        if (file_exists($filePath)) {
            $toAdd = $this->stocktakingService->mapShelves($filePath);

            return $this->json(['data' => $toAdd]);
        }

        return $this->json(['message' => 'File doesn\'t extists'], Response::HTTP_BAD_REQUEST);
    }

    #[Route('/create', methods: ['POST'])]
    public function create(Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['product'])) {
            $newProduct = $this->serializer->denormalize($data['product'], Product::class, 'array');
            $this->entityManager->persist($newProduct);
            $this->entityManager->flush();

            return $this->json(['message' => 'Successfully created', 'product' => $newProduct], Response::HTTP_CREATED);
        }

        return $this->json(['message' => 'Bad request', Response::HTTP_BAD_REQUEST]);
    }

    #[Route('/scan', methods: ['POST'])]
    public function scan(Request $request): JsonResponse {
        $content = $request->toArray();

        if (isset($content['search'])) {
            $searchString = trim($content['search']);
            if (Ean13Validator::isAValidEAN13($searchString)) {
                if (NULL == ($eanObject = $this->entityManager->getRepository(Ean::class)->findOneBy(['ean' => $searchString]))) {
                    $subiektProduct = $this->subiektProductsService->findProductByEan($searchString);

                    if (!isset($subiektProduct['data']['code'])) {
                        return $this->json(['message' => 'Ean not found in Subiekt!!!', 'data' => []], Response::HTTP_NOT_FOUND);
                    }
                    $eans = $this->subiektProductsService->getAllEans($subiektProduct['data']['code']);
                    $productObject = new Product();
                    $productObject->setName($subiektProduct['data']['name']);
                    $productObject->setSku($subiektProduct['data']['code']);
                    foreach ($eans['data']['eans'] as $eanCode) {
                        $ean = new Ean();
                        $ean->setEan($eanCode);
                        $productObject->addEan($ean);
                    }
                    $this->entityManager->persist($productObject);
                    $this->entityManager->flush();

                } else {
                    $productObject = $eanObject->getProduct();
                }

                return $this->json(['message' => 'Success', 'data' => ['product' => $this->createScanResponseByProduct($productObject)]], Response::HTTP_OK, [],
                    ['groups' => 'scan']);
            }
            $results = $this->entityManager->getRepository(Product::class)->findByName($searchString);

            return $this->json(['message' => 'Success', 'data' => ['products' => $results]], Response::HTTP_OK, [], ['groups' => 'scan']);
        }

        return $this->json(['message' => 'Search string not found!', 'data' => []], Response::HTTP_NOT_FOUND);
    }

    private function createScanResponseByProduct(Product $product): array
    {
        $newResponse = [
            'name' => $product->getName(),
            'sku' => $product->getSku(),
            'id' => $product->getId(),
            'eans' => []
        ];

        $productEans = $this->entityManager->getRepository(Ean::class)->findBy(['product' => $product->getId()]);

        $processedEans = [];

        foreach ($productEans as $ean) {
            $eanCode = $ean->getEan();

            if (in_array($eanCode, $processedEans)) {
                continue;
            }

            $allEans = $this->entityManager->getRepository(Ean::class)->findBy(['ean' => $eanCode]);

            foreach ($allEans as $currentEan) {
                $eanData = [
                    'ean' => $currentEan->getEan(),
                    'eanShelfQuantities' => []
                ];

                $eanShelfQuantities = $this->entityManager->getRepository(EanShelfQuantity::class)->findBy(['ean' => $currentEan]);
                foreach ($eanShelfQuantities as $eanShelfQuantity) {
                    $shelf = $eanShelfQuantity->getShelf();
                    $eanData['eanShelfQuantities'][] = [
                        'quantity' => $eanShelfQuantity->getQuantity(),
                        'virtual_quantity' => $eanShelfQuantity->getVirtualQuantity(),
                        'shelf' => [
                            'shelf_no' => $shelf->getShelfNo(),
                            'id' => $shelf->getId()
                        ],
                        'eanShelfId' => $eanShelfQuantity->getId()
                    ];
                }

                $newResponse['eans'][] = $eanData;
            }

            $processedEans[] = $eanCode;
        }

        return $newResponse;
    }

    #[Route('/{id}/addShelf', methods: ['POST'])]
    public function addShelfForProduct(Product $product, Request $request): JsonResponse {

        return $this->json(['message' => 'Deprecated', 'data' => []]);
        $requestArray = $request->toArray();
        if (isset($requestArray['ean']) && isset($requestArray['shelf_no'])) {
            if (NULL === ($shelf = $this->entityManager->getRepository(Shelf::class)->findOneBy(['shelf_no' => $requestArray['shelf_no']]))) {
                return $this->json(['message' => 'Shelf not found', 'data' => []], Response::HTTP_NOT_FOUND);
            }
            if (NULL !== ($ean = $this->entityManager->getRepository(Ean::class)->findOneBy(['ean' => $requestArray['ean']]))) {
                if ($ean->getShelf() === $shelf) {
                    return $this->json(['message' => 'Ean with shelf exists! Can\'t add it second time!', 'data' => ['ean' => $ean]], Response::HTTP_BAD_REQUEST);
                }

                $ean->setShelf($shelf);
                $this->entityManager->persist($ean);
                $this->entityManager->flush();

                return $this->json(['message' => 'Success', 'data' => ['product' => $product]], Response::HTTP_CREATED);
            }
            return $this->json(['message' => 'Ean not found!', 'data' => []], Response::HTTP_NOT_FOUND);
        }

        return $this->json(['message' => 'Bad data provided', 'data' => []], Response::HTTP_BAD_REQUEST);
    }

    #[Route('/{product}/{shelf}/addQuantity')]
    public function addQuantity(Product $product, Shelf $shelf, Request $request): JsonResponse {
        $requestArray = $request->toArray();
        if (isset($requestArray['ean']) && ((isset($requestArray['quantity']) && $requestArray['quantity'] > 0) || (isset($requestArray['virtualQuantity']) && $requestArray['virtualQuantity'] > 0))) {
            $postedEan = $requestArray['ean'];
            // TODO: Fix this
//            $ean = $product->getEans()->filter(function($eanObject) use ($postedEan) {
//                return $eanObject->getEan() === $postedEan;
//            })->first();
            
            $ean = $this->entityManager->getRepository(Ean::class)->findOneBy([
                'ean' => $postedEan,
                'product' => $product->getId()
            ]);

            if (FALSE === $ean) {
                return $this->json(['message' => 'Ean not found!', 'data' => []], Response::HTTP_NOT_FOUND);
            }
            if(isset($requestArray['eanShelfId'])){
                $eanShelfQuantity = $this->entityManager->getRepository(EanShelfQuantity::class)->find($requestArray['eanShelfId']);
            } else {
                $eanShelfQuantity = $ean->getEanShelfQuantities()->filter(function($eanShelfQuantity) use ($shelf) {
                    return $eanShelfQuantity->getShelf() === $shelf;
                })->first();
            }

            if (FALSE === $eanShelfQuantity) {
                $eanShelfQuantity = new EanShelfQuantity();
                $eanShelfQuantity->setShelf($shelf);
            }

            if (isset($requestArray['quantity'])) {
                $eanShelfQuantity->setQuantity($eanShelfQuantity->getQuantity() + $requestArray['quantity']);
            }

            if (isset($requestArray['virtualQuantity'])) {
                $eanShelfQuantity->setVirtualQuantity($eanShelfQuantity->getVirtualQuantity() + $requestArray['virtualQuantity']);
            }

            $ean->addEanShelfQuantity($eanShelfQuantity);
            $this->entityManager->persist($eanShelfQuantity);
            $this->entityManager->flush();

            return $this->json(['message' => 'Success', 'data' => []], Response::HTTP_CREATED);
        }

        return $this->json(['message' => 'Bad data provided', 'data' => []], Response::HTTP_BAD_REQUEST);
    }
}