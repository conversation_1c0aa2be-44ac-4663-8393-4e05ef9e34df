<?php

namespace App\Core\Controller\Api\Subiekt;

use App\Core\Entity\Order;
use App\Core\Message\SubiektAddCommentToInvoiceMessage;
use App\Core\Rules\RuleTrigger;
use App\Core\Service\OrderStatusService;
use App\Core\Service\Settings;
use App\Core\Service\Subiekt\Documents\SubiektDocumentsService;
use App\Core\Service\Subiekt\Orders\SubiektOrdersService;
use App\Core\Service\Subiekt\Products\SubiektProductsService;
use App\Core\Service\Subiekt\SubiektApiService;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;

#[Route('/api/subiekt')]
class SubiektApiController extends AbstractController {
    
    public function __construct(
        private readonly SubiektApiService $subiektApiService,
        private readonly SubiektOrdersService $subiektOrdersService,
        private readonly SubiektDocumentsService $subiektDocumentsService,
        private readonly EntityManagerInterface $entityManager,
        private readonly OrderStatusService $orderStatusService,
        private readonly MessageBusInterface $bus,
        private readonly SubiektProductsService $subiektProductsService,
        private readonly Settings $settings,
        private RuleTrigger $ruleTrigger
    ) {}
    
    #[Route('/setAuthParams', name: 'api_subiekt_auth', methods: ['POST'])]
    public function setAuthParams(Request $request): JsonResponse {
        $data = $request->toArray();
        if (isset($data['apiKey'])) {
            $this->settings->set('SUBIEKT_API_KEY', $data['apiKey']);
        }
        if (isset($data['apiRemoteUrl'])) {
            $this->settings->set('SUBIEKT_BASE_REMOTE_URL', $data['apiRemoteUrl']);
        }
        if (isset($data['apiLocalUrl'])) {
            $this->settings->set('SUBIEKT_BASE_LOCAL_URL', $data['apiLocalUrl']);
        }
        
        return $this->json([
            'apiKey' => $this->settings->get('SUBIEKT_API_KEY'),
            'apiRemoteUrl' => $this->settings->get('SUBIEKT_BASE_REMOTE_URL'),
            'apiLocalUrl' => $this->settings->get('SUBIEKT_BASE_LOCAL_URL'),
        ]);
    }
    
    #[Route('/product/addProduct', name: 'api_subiekt_product_add', methods: ['POST'])]
    public function addProduct(Request $request): Response {
        $data = $request->toArray();
        $product = $this->subiektProductsService->addProduct($data);
        
        return $this->json($product, $product['code']);
    }
    
    #[Route('/document/addCommentBulk', name: 'api_subiekt_order_update_comment', methods: ['GET'])]
    public function updateComment(): Response {
        $orders = $this->entityManager->getRepository(Order::class)->findBy(['internal_status_id' => $this->orderStatusService->getStatus(OrderTaxonomy::STATUS_SENT)], null, 5);
        $ordersDone = [];
        foreach ($orders as $order) {
            $message = new SubiektAddCommentToInvoiceMessage($order->getId());
            $this->bus->dispatch($message);
        }
        
        return $this->json($ordersDone);
    }
    
    #[Route('/document/addComment/{id}', name: 'api_subiekt_order_update_comment_by_id', methods: ['GET'])]
    public function updateCommentById(Order $order): Response {
        return $this->json($this->subiektDocumentsService->updateComment($order));
    }
    
    #[Route('/{category}/{action}', name: 'api_subiekt_hello', methods: ['POST'])]
    public function api(Request $request, string $category, string $action): Response {
        $data = json_decode($request->getContent(), true);
        if (empty($data)) {
            return $this->json([
                'error' => 'Request body cannot be empty and must contain data.'
            ], Response::HTTP_BAD_REQUEST);
        }
        $response = $this->subiektApiService->makeRequest($category, $action, $data);
        
        return $this->json($response);
    }
    
    /**
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    #[Route('/order/addOrder/{id}', name: 'api_subiekt_add_order', methods: ['GET'])]
    public function addOrder(Order $order): Response {
        try {
            if ($invoiceNo = $this->subiektOrdersService->addOrder($order)) {
                $orderInvoice = $order->getOrderInvoice();
                $orderInvoice->setInvoiceNumber($invoiceNo);
                $this->entityManager->persist($orderInvoice);
                $this->entityManager->persist($order);
                $this->entityManager->flush();
                $this->ruleTrigger->triggerEvent('order.zk.create.success', ['order' => $order]);

                return $this->json($order, Response::HTTP_CREATED);
            }
        } catch (\Exception | \Throwable $exception) {
            $this->ruleTrigger->triggerEvent('zk.create.error', ['order' => $order]);
            
            return $this->json(['order' => $order, 'error' => $exception->getMessage()], Response::HTTP_BAD_REQUEST);
        }

        
        return $this->json($order, Response::HTTP_BAD_REQUEST);
    }
    

    #[Route('/order/makeSaleDoc/{id}', name: 'api_subiekt_make_sale_doc', methods: ['GET'])]
    public function makeSaleDoc(Order $order): Response {
        $response = $this->subiektOrdersService->makeSaleDoc($order);
        
        return $this->json($response, $response['code']);
    }
    
    #[Route('/order/makeSaleDoc/{id}/email', name: 'api_subiekt_make_sale_doc_manual', methods: ['GET'])]
    public function makeSaleDocManual(Order $order): Response {
        $response = $this->subiektOrdersService->makeSaleDoc($order);
        
        return $this->json($response, $response['code']);
    }
    
    #[Route('/document/getPdf/{id}', name: 'api_subiekt_get_pdf', methods: ['GET'])]
    public function getPdf(Order $order): Response {
        return $this->json($this->subiektDocumentsService->getPdf($order));
    }
    
    #[Route('/order/getState/{id}', name: 'api_subiekt_get_state', methods: ['GET'])]
    public function getState(Order $order): Response {
        return $this->json($this->subiektOrdersService->checkSaleDoc($order));
    }
    
    #[Route('/order/get/{id}', name: 'api_subiekt_get_state_get', methods: ['GET'])]
    public function getOrder(Order $order): Response {
        return $this->json($this->subiektOrdersService->get($order));
    }
    
    #[Route('/document/get/{id}', name: 'api_subiekt_document_get', methods: ['GET'])]
    public function getDocument(Order $order): Response {
        return $this->json($this->subiektDocumentsService->get($order));
    }
    
    #[Route('/document/updatestatus/{id}', name: 'api_subiekt_document_update_status', methods: ['GET'])]
    public function documentUpdateStatus(Order $order): Response {
        return $this->json($this->subiektDocumentsService->updateZkReservation($order, true));
    }
    
    #[Route('/document/getstate/{id}', name: 'api_subiekt_document_get_state', methods: ['GET'])]
    public function getDocumentState(Order $order): Response {
        return $this->json($this->subiektDocumentsService->getState($order));
    }
    
    #[Route('/order/update/{id}', name: 'api_subiekt_order_update', methods: ['GET'])]
    public function updateOrder(Order $order): Response {
        return $this->json($this->subiektOrdersService->update($order));
    }
    
    #[Route('/product/get/{code}', name: 'api_subiekt_product_get', methods: ['GET'])]
    public function getProduct(string $code): Response {
        return $this->json($this->subiektProductsService->get($code));
    }
    
    #[Route('/product/assignEan/{code}', name: 'api_subiekt_product_assign_ean', methods: ['POST'])]
    public function assignEan(string $code): Response {
        return $this->json($this->subiektProductsService->assignEansByCode($code));
    }
    
    #[Route('/product/find-by-ean/{ean}', name: 'api_subiekt_product_assign_eans', methods: ['GET'])]
    public function findProductByEan($ean) {
        return $this->json($this->subiektProductsService->findProductByEan($ean));
    }
    
    #[Route('/product/getQuantityInWarehouse', name: 'api_subiekt_product_get_quantity_in_warehouse', methods: ['GET'])]
    public function getQuantityInWarehouse(Request $request) {
        $data = $request->toArray();
        if (empty($data['skus']) || empty($data['warehouses'])) {
            return $this->json([
                'error' => 'Request body must contain skus and warehouses.'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        return $this->json($this->subiektProductsService->getQuantityInWarehouse($data['skus'], $data['warehouses']));
    }
}