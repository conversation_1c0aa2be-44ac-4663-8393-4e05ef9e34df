<?php

namespace App\Core\Controller\Javascript;

use App\Core\Template\EventSystem\Event\EventStorage;
use App\Core\Template\EventSystem\Event\JavascriptEvent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;

class JavascriptController extends AbstractController {

    public function __construct(private readonly EventDispatcherInterface $eventDispatcher) {}

    public function javascript(): Response {
        $javascriptEvent = new JavascriptEvent();
        $this->eventDispatcher->dispatch($javascriptEvent, EventStorage::PAGE_JAVASCRIPT);

        return $this->render('javascript.html.twig', ['javascripts' => $javascriptEvent->getContent()]);
    }
}