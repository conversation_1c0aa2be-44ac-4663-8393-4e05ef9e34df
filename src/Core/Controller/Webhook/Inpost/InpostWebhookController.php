<?php

namespace App\Core\Controller\Webhook\Inpost;


use App\Core\Service\Carrier\Inpost\InpostWebhookService;
use App\Core\Taxonomy\InpostStatusTaxonomy;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class InpostWebhookController extends AbstractController
{
    public function __construct(
        private readonly InpostWebhookService $inpostWebhookService,
    )
    {}

    #[Route('/webhook/inpost', name: 'webhook_inpost')]
    public function handleWebhook(Request $request): Response
    {
        $payload = $request->toArray();

        if (empty($payload)) {
            http_response_code(400);
            die('Invalid payload');
        }

        if (!isset($payload['event'])) {
            return new Response('Invalid event', 400);
        }

        switch ($payload['event']) {
            case InpostStatusTaxonomy::INPOST_SHIPMENT_STATUS_CREATED:
                $this->inpostWebhookService->handleShipmentCreated($payload['payload']);
                break;
            case InpostStatusTaxonomy::INPOST_SHIPMENT_STATUS_CHANGED:
                $this->inpostWebhookService->handleShipmentUpdated($payload['payload']);
                break;
            case InpostStatusTaxonomy::INPOST_SHIPMENT_STATUS_DELETED:
                $this->inpostWebhookService->handleShipmentDeleted($payload['payload']);
                break;
            default:
                return new Response('Invalid event', 400);
        }

        return new Response('Webhook handled', 200);
    }
}