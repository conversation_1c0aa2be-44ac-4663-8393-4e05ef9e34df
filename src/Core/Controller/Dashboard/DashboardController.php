<?php

namespace App\Core\Controller\Dashboard;

use App\Engine\Response\PageResponse;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;

class DashboardController extends AbstractController {

    #[Route('/dashboard', name: 'dashboard')]
    public function index() {
        return PageResponse::create('dashboard/dashboard.html.twig', [
            'title' => 'Dashboard',
            'user' => $this->getUser()
        ]);
    }
}