<?php

namespace App\Core\Controller\Web;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;

class CommonController extends AbstractController {

    #[Route(path: '/web/funnyMessage/{count}', name: 'web_funny_message')]
    public function funnyMessage($count = 1) {
        $count = min($count, 10);
        $array = explode('.', $this->sentence($count));
        array_pop($array);
        return $this->render('web/common/funny-message.html.twig', ['sentence' => $array]);
    }

    function sentence(int $level, array $path = []): string {
        if ($level === 0) {

            return '';
        }

        $entityName = $this->nameOfEntity($path);

        $genitive = $this->genitive($path);

        $before = ['przeddzidzia', ...$genitive];
        $middle  = ['śróddzidzia',  ...$genitive];
        $after    = ['zadzidzia',    ...$genitive];

        $part1 = implode(' ', $before);
        $part2 = implode(' ', $middle);
        $part3 = implode(' ', $after);

        $sentence = ucfirst($entityName) . " składa się z $part1, $part2 i $part3.";

        $sentence .= ' ' . $this->sentence($level - 1, ['przeddzidzie', ...$genitive]);
        $sentence .= ' ' . $this->sentence($level - 1, ['śróddzidzie',  ...$genitive]);
        $sentence .= ' ' . $this->sentence($level - 1, ['zadzidzie',    ...$genitive]);

        return $sentence;
    }

    function nameOfEntity(array $path): string {
        return $path ? implode(' ', $path) : 'Dzida';
    }

    function genitive(array $path): array {
        return array_map(function($word) {
            return $this->genitiveDeclination($word);
        }, $path ?: ['dzida']);
    }

    function genitiveDeclination(string $word): string {
        return match($word) {
            'dzida' => 'dzidy',
            'przeddzidzie' => 'przeddzidzia',
            'śróddzidzie'  => 'śróddzidzia',
            'zadzidzie'    => 'zadzidzia',
            default => $word
        };
    }

}