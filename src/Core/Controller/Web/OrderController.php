<?php

namespace App\Core\Controller\Web;

use App\Core\Attribute\AsMenuLink;
use App\Core\Entity\Order;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Uid\Uuid;

#[Route(path: '/web/order')]
class OrderController extends AbstractController {


    public function __construct(private readonly EntityManagerInterface $entityManager) {}

    #[Route(path: '#', name: 'order_main_menu')]
    #[AsMenuLink('Orders', null, null, 'bx bx-dollar', 100)]
    public function defaultMenuItem() {
        return;
    }
    #[Route(path: '/list/{page}', name: 'web_orders_list', methods: ['GET'])]
    #[AsMenuLink('Order list', null, 'order_main_menu', 'bx bx-dollar', 100)]
    public function viewAll(Request $request, $page = 0) {
        if (!empty($query = $request->query->all())) {
            if (isset($query['searchOrderId'])) {
                if (Uuid::isValid($query['searchOrderId'])) {
                    $order = $this->entityManager->getRepository(Order::class)->find($query['searchOrderId']);
                } else {
                    $order = $this->entityManager->getRepository(Order::class)->findOneBy(['shop_order_id' => $query['searchOrderId']]);
                }
                if (!is_null($order)) {
                    return $this->redirectToRoute('web_orders_view_by_id', ['id' => $order->getId()]);
                }
            }
        }
        $perPage = 50;
        $offset = ($page * $perPage);
        $counted = $this->entityManager->getRepository(Order::class)->count();
        $pages = ceil($counted / $perPage);
        $paginator = [
            'currentPage' => $page,
            'pages' => $pages,
            'totalPages' => $pages,
            'min' => 1,
            'max' => $pages,
        ];

        return [
            'template' => 'web/order/view-all.html.twig',
            'data' => [
                'orders' => $this->entityManager->getRepository(Order::class)->findBy([], ['date_add' => 'DESC'], 50, $offset),
                'paginator' => $paginator
            ]
        ];
    }

    #[Route(path: '/{id}/view', name: 'web_orders_view_by_id', methods: ['GET'])]
    public function viewById(Order $order) {
        return [
            'template' => 'web/order/view.html.twig',
            'data' => [
                'order' => $order,
            ]
        ];
    }
}