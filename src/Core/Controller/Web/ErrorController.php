<?php

namespace App\Core\Controller\Web;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class ErrorController extends AbstractController {


    #[Route(path: '/web/error', name: 'web_error_page')]
    public function index(Request $request): Response {
        return $this->render('AccessDenied.html.twig', ['data' => [
            'reqUri' => $request->getRequestUri(),
            'ipAddress' => $request->getClientIp(),
            'userAgent' => $request->headers->get('User-Agent'),
        ]]);
    }
}