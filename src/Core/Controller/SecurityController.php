<?php

namespace App\Core\Controller;

use App\Core\Attribute\AsMenuLink;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;
use Twig\Environment;

class SecurityController extends AbstractController
{
    public function __construct(private Environment $twig) {}

    #[Route(path: '/', name: 'default_page')]
    public function defaultPage(AuthenticationUtils $authenticationUtils) {
        return $this->login($authenticationUtils);
    }

    #[Route(path: '/login', name: 'app_login')]
    public function login(AuthenticationUtils $authenticationUtils): Response|array {

        if ($this->isGranted('ROLE_USER')) {
            return $this->redirectToRoute('dashboard');
        }
        // get the login error if there is one
        $error = $authenticationUtils->getLastAuthenticationError();

        // last username entered by the user
        $lastUsername = $authenticationUtils->getLastUsername();
        $content = $this->twig->render('security/login.html.twig', [
            'title' => 'Login',
            'last_username' => $lastUsername,
            'error' => $error
        ]);

        return new Response($content);
    }

    #[Route(path: '/logout', name: 'app_logout')]
    #[AsMenuLink('Logout', 'IS_AUTHENTICATED_FULLY', parent: 'user_menu_group')]
    public function logout(Security $security): void
    {
        $security->logout();
    }
}
