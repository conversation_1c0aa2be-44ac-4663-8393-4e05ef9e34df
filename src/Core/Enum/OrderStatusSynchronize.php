<?php

namespace App\Core\Enum;

use BackedEnum;
use Symfony\Contracts\Translation\TranslatableInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

enum OrderStatusSynchronize: string implements BackedEnumInterface, TranslatableInterface
{
    case YES = 'YES';
    case NO = 'NO';

    public function trans(TranslatorInterface $translator, ?string $locale = null): string
    {
        return $translator->trans($this->name, locale: $locale);
    }

}