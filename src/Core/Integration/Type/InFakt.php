<?php

namespace App\Core\Integration\Type;

use App\Core\Form\Base\Element\TextType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

class InFakt {

    private array $configurationFields = [
        [
            'name' => 'name',
            'type' => 'text',
            'label' => 'Nazwa integracji',
        ],
        [
            'name' => 'COMPANY',
            'type' => 'text',
            'label' => 'Nazwa firmy',
        ],
        [
            'name' => 'SELLER_SIGNATURE',
            'type' => 'text',
            'label' => 'Podpis sprzedawcy',
        ],
        [
            'name' => 'API_TOKEN',
            'type' => 'text',
            'label' => 'Token Api'
        ],
        [
            'name' => 'SANDBOX',
            'type' => 'select',
            'label' => 'Środowisko',
            'options' => [
                [
                    'label' => 'Produkcja',
                    'value' => false,
                ],
                [
                    'label' => 'Sandbox',
                    'value' => true,
                ]
            ]
        ],
    ];

    public function getType(): string
    {
        return 'infakt';
    }

    public function getConfigurationFields(): array
    {
        return $this->getFormFields();
    }

    public function getFormFields(): array {
        return [
            [
                'type' => TextType::class,
                'name' => 'text',
                'options' => [
                    'label' => 'Nazwa integracji',
                ]
            ],
            [
                'type' => TextType::class,
                'name' => 'COMPANY',
                'options' => [
                    'label' => 'Nazwa firmy',
                ]
            ],
            [
                'type' => TextType::class,
                'name' => 'SELLER_SIGNATURE',
                'options' => [
                    'label' => 'Podpis sprzedawcy',
                ]

            ],
            [
                'type' => ChoiceType::class,
                'name' => 'SANDBOX',
                'options' => [
                    'label' => 'Środowisko',
                    'choices' => [
                        'Produkcja' => false,
                        'Sandbox' => true,
                    ]
                ],
            ]
        ];
    }
}

