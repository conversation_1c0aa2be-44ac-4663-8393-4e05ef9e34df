<?php

namespace App\Core\Integration\Type;

use Symfony\Component\Form\Extension\Core\Type\TextType;

class Prestashop extends AbstractIntegrationType {
    private array $configurationFields = [
        [
            'name' => 'apiUrl',
            'type' => TextType::class,
            'options' => [
                'label' => 'Adres URL API',
                'required' => true,
            ]
        ],
        [
            'name' => 'apiToken',
            'type' => TextType::class,
            'options' => [
                'label' => 'API Token',
                'required' => true,
            ]
        ],
        [
            'name' => 'apiSecret',
            'type' => TextType::class,
            'options' => [
                'label' => 'API Secret',
                'required' => true,
            ]
        ]
    ];

    public function getType(): string
    {
        return 'prestashop';
    }

    public function getConfigurationFields(): array {
        return $this->configurationFields;
    }
}

