<?php

namespace App\Core\Integration;

use App\Core\Entity\Integration;
use App\Core\Integration\Handler\IntegrationHandlerInterface;
use App\Core\Integration\Type\IntegrationTypeInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;

class IntegrationService {

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        #[AutowireIterator(IntegrationTypeInterface::class)] private readonly iterable $integrationTypes,
        #[AutowireIterator(IntegrationHandlerInterface::class)] private iterable $integrationHandlers
    ) {}

    public function getAllTypes(): array {
        $integrations = [];

        foreach ($this->integrationTypes as $integrationType) {
            $integrations[] = [
                'type' => $integrationType->getType(),
                'fields' => $integrationType->getConfigurationFields()
            ];
        }

        return $integrations;
    }

    public function getHandlerByType($type): ?IntegrationHandlerInterface {
        foreach ($this->integrationHandlers as $integrationHandler) {
            if ($integrationHandler->supports($type)) {
                return $integrationHandler;
            }
        }
    }

    public function create($data): Integration {
        if (!isset($data['type'])) {
            throw new \Exception('No type provided');
        }

        $handler = $this->getHandlerByType($data['type']);
        if (null === $handler) {
            throw new \Exception('No handler found for type ' . $data['type']);
        }
        return $handler->create($data);
    }

    public function update(Integration $integration, array $data): Integration {
        $handler = $this->getHandlerByType($integration->getType());
        if (null === $handler) {
            throw new \Exception('No handler found for type ' . $integration->getType());
        }

        return $handler->update($integration, $data);
    }

    public function delete(Integration $integration) {
        $handler = $this->getHandlerByType($integration->getType());
        if (null ===$handler) {
            throw new \Exception('No handler found for type ' . $integration->getType());
        }

        return $handler->delete($integration);
    }
}