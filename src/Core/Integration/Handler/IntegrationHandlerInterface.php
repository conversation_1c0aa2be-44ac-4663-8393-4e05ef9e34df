<?php

namespace App\Core\Integration\Handler;

use App\Core\Entity\Integration;
use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag(IntegrationHandlerInterface::class)]
interface IntegrationHandlerInterface {
    public function handle();
    public function supports($type): bool;
    public function create(array $data): Integration;
    public function update(Integration $integration, array $data): Integration;
    public function delete(Integration $integration): bool;


}