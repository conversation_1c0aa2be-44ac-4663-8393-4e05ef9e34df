<?php

namespace App\Core\Integration\Handler;

use App\Core\Entity\Integration;
use App\Core\Entity\IntegrationData;
use App\Core\Entity\IntegrationStatusMapping;
use App\Core\Service\Fetcher\Order\PrestashopOrderFetcherService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

class PrestashopHandler implements IntegrationHandlerInterface {

    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly EntityManagerInterface $entityManager,
        private readonly Security $security,
        private readonly PrestashopOrderFetcherService $orderFetcherService,
    ) {}
    private const TYPE = 'prestashop';

    public function handle()
    {
        // TODO: Implement handle() method.
    }

    public function supports($type): bool {
        return $type === self::TYPE;
    }

    public function create(array $data): Integration {
        $newIntegration = new Integration();
        $newIntegration->setType(self::TYPE);
        $newIntegration->setName($data['name']);
        $newIntegration->setOwner($this->security->getUser());
        $integrationData = new IntegrationData();
        $integrationData->setIntegration($newIntegration);
        $integrationData = $this->serializer->denormalize($data, IntegrationData::class, 'array');
        $newIntegration->setIntegrationData($integrationData);
        $this->entityManager->persist($newIntegration);
        $this->entityManager->flush();

        $this->fetchOrderStates($newIntegration);

        return $newIntegration;
    }

    public function update(Integration $integration, array $data): Integration {
        $integration = $this->serializer->denormalize($data, Integration::class, 'array', [AbstractNormalizer::OBJECT_TO_POPULATE => $integration]);
        $integrationData = $integration->getIntegrationData();
        $integrationData = $this->serializer->denormalize($data, IntegrationData::class, 'array', [AbstractNormalizer::OBJECT_TO_POPULATE => $integrationData]);
        $integration->setIntegrationData($integrationData);
        $this->entityManager->persist($integration);
        $this->entityManager->flush();
        if ($integration->getIntegrationData()->getIntegrationStatusMappings()->count() === 0) {
            $this->fetchOrderStates($integration);
        }

        return $integration;
    }

    public function delete(Integration $integration): bool {
       $this->entityManager->remove($integration);
       $this->entityManager->flush();

       return true;
    }

    private function fetchOrderStates(Integration $integration): void {
        $states = $this->orderFetcherService->getOrderStates($integration);
        $integrationData = $integration->getIntegrationData();
        foreach ($states as $state) {
            $statusMapping = new IntegrationStatusMapping();
            $statusMapping->setIntegrationStatusId($state['id']);
            $name = 'Brak nazwy statusu w systemie sklepu';
            if (is_array($state['name'])) {
                foreach ($state['name'] as $nameArray) {
                    if (null === $nameArray['value']) {
                        $a = 4;
                    }
                    if (null !== $nameArray['value']) {
                        $name = $nameArray['value'];
                        break;
                    }
                }
            }

            $statusMapping->setIntegrationStatusName($name);
            $statusMapping->setPaid((int) $state['paid']);
            $statusMapping->setShipped((int) $state['shipped']);
            $statusMapping->setDelivered((int) $state['delivery']);
            $integrationData->addIntegrationStatusMapping($statusMapping);
            $this->entityManager->persist($statusMapping);

        }
        $this->entityManager->flush();
    }
}