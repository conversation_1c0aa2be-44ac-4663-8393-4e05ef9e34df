<?php

namespace App\Core\Integration\Handler;

use App\Core\Entity\Integration;
use App\Core\Entity\IntegrationData;
use App\Core\Entity\IntegrationSettings;
use App\Core\Entity\IntegrationStatusMapping;
use App\Core\Service\Fetcher\Order\PrestashopOrderFetcherService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\SerializerInterface;

class InFaktHandler implements IntegrationHandlerInterface {

    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly EntityManagerInterface $entityManager,
        private readonly Security $security
    ) {}
    private const TYPE = 'infakt';

    public function handle()
    {
        // TODO: Implement handle() method.
    }

    public function supports($type): bool {
        return $type === self::TYPE;
    }

    public function create(array $data): Integration {
        $newIntegration = new Integration();
        $newIntegration->setType(self::TYPE);
        $newIntegration->setName($data['name']);
        $newIntegration->setOwner($this->security->getUser());

        foreach(['COMPANY', 'SELLER_SIGNATURE', 'API_TOKEN', 'SANDBOX'] as $setting) {
            $s = new IntegrationSettings();
            $s->setName($setting);
            $s->setValue($data[$setting]);
            $s->setIntegration($newIntegration);
            $newIntegration->addIntegrationSetting($s);
        }

        $this->entityManager->persist($newIntegration);
        $this->entityManager->flush();

        return $newIntegration;
    }

    public function update(Integration $integration, array $data): Integration {
        $integration = $this->serializer->denormalize($data, Integration::class, 'array', [AbstractNormalizer::OBJECT_TO_POPULATE => $integration]);
        $integrationData = $integration->getIntegrationData();
        $integrationData = $this->serializer->denormalize($data, IntegrationData::class, 'array', [AbstractNormalizer::OBJECT_TO_POPULATE => $integrationData]);
        $integration->setIntegrationData($integrationData);
        $this->entityManager->persist($integration);
        $this->entityManager->flush();

        return $integration;
    }

    public function delete(Integration $integration): bool {
       $this->entityManager->remove($integration);
       $this->entityManager->flush();

       return true;
    }

}