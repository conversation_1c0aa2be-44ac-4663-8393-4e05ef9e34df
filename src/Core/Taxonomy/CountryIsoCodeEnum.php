<?php

namespace App\Core\Taxonomy;

enum CountryIsoCodeEnum: string
{
    case AUSTRIA = 'AT';
    case BELGIUM = 'BE';
    case BULGARIA = 'BG';
    case CROATIA = 'HR';
    case CZECHREPUBLIC = 'CZ';
    case DENMARK = 'DK';
    case FINLAND = 'FI';
    case FRANCE = 'FR';
    case GERMANY = 'DE';
    case GREECE = 'GR';
    case HUNGARY = 'HU';
    case ITALY = 'IT';
    case LATVIA = 'LV';
    case LITHUANIA = 'LT';
    case LUXEMBURG = 'LU';
    case NETHERLANDS = 'NL';
    case POLAND = 'PL';
    case PORTUGAL = 'PT';
    case ROMANIA = 'RO';
    case SLOVAKIA = 'SK';
    case SLOVENIA = 'SI';
    case SPAIN = 'ES';
    case SWEDEN = 'SE';
    
    /**
     * Normalize country name to match the enum case name.
     */
    private static function normalizeCountryName(string $country): string
    {
        return strtoupper(str_replace(' ', '', $country));
    }
    
    /**
     * Custom method to match country by case name and return ISO code.
     */
    public static function getIsoCodeByCountry(string $country): ?string
    {
        $normalized = self::normalizeCountryName($country);
        
        return self::tryFromName($normalized)?->value ?? null;
    }
    
    private static function tryFromName(string $name): ?self
    {
        foreach (self::cases() as $case) {
            if ($case->name === $name) {
                return $case;
            }
        }

        if (in_array($name, ['LUXEMBOURG', 'LUXEMBURG'])) {
            return self::LUXEMBURG;
        }
        return null;
    }
}