<?php

namespace App\Core\Taxonomy;

enum SubiektVatSymbolEnum: int
{
    case AUSTRIA = 1000001;
    case CZECH_REPUBLIC = 1000004;
    case BELGIUM = 1000002;
    case BULGARIA = 1000003;
    case CROATIA = 1000011;
    case DENMARK = 1000005;
    case FINLAND = 1000008;
    case FRANCE = 1000009;
    case GERMANY = 1000000;
    case GREECE = 1000010;
    case HUNGARY = 1000012;
    case ITALY = 1000014;
    case LATVIA = 1000017;
    case LITHUANIA = 1000015;
    case LUXEMBOURG = 1000016;
    case NETHERLANDS = 1000018;
    case POLAND = 100001;
    case PORTUGAL = 1000019;
    case ROMANIA = 1000020;
    case SLOVAKIA = 1000023;
    case SLOVENIA = 1000022;
    case SPAIN = 1000007;
    case SWEDEN = 1000021;
    
    private static function normalizeCountryName(string $country): string
    {
        return strtoupper(str_replace(' ', '_', $country));
    }
    
    /**
     * Get VAT symbol by country name.
     */
    public static function getVatSymbolByCountry(string $country): ?int
    {
        $normalized = self::normalizeCountryName($country);
        return self::tryFromName($normalized)?->value ?? null;
    }
    
    /**
     * Custom method to match by case name (since tryFrom() only matches by value).
     */
    private static function tryFromName(string $name): ?self
    {
        foreach (self::cases() as $case) {
            if ($case->name === $name) {
                return $case;
            }
        }

        if (in_array($name, ['LUXEMBOURG', 'LUXEMBURG'])) {
            return self::LUXEMBOURG;
        }
        return null;
    }
    
    /**
     * Get all VAT symbols.
     */
    public static function getAllVatSymbols(): array
    {
        return array_reduce(
            self::cases(),
            fn($acc, $case) => $acc + [$case->name => $case->value],
            []
        );
    }
}