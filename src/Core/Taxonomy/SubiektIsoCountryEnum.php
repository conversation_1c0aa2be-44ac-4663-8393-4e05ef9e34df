<?php

namespace App\Core\Taxonomy;

enum SubiektIsoCountryEnum: int
{
    case PL = 1;
    case CZ = 2;
    case SK = 3;
    case HU = 6;
    case LT = 7;
    case LV = 8;
    case EE = 9;
    case SI = 10;
    case MT = 11;
    case CY = 12;
    case GB = 13;
    case FR = 14;
    case ES = 15;
    case PT = 16;
    case IT = 17;
    case AT = 18;
    case BE = 19;
    case NL = 20;
    case LU = 21;
    case DE = 22;
    case DK = 23;
    case SE = 24;
    case FI = 25;
    case IE = 26;
    case GR = 27;
    case US = 28;
    case RU = 29;
    case BG = 30;
    case RO = 31;
    case HR = 32;
    case UA = 33;
    case CA = 34;
    case BY = 35;
    case XI = 36;
    
    public static function getIdByIsoCode(string $isoCode): ?int
    {
        $normalized = strtoupper($isoCode);
        return self::tryFromName($normalized)?->value ?? null;
    }
    
    public static function getAllCountryIds(): array
    {
        return array_reduce(
            self::cases(),
            fn($acc, $case) => $acc + [$case->name => $case->value],
            []
        );
    }
    
    private static function tryFromName(string $name): ?self
    {
        foreach (self::cases() as $case) {
            if ($case->name === $name) {
                return $case;
            }
        }
        return null;
    }
}