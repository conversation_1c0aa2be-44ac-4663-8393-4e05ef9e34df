<?php

namespace App\Core\Taxonomy;
class DateTaxonomy {

    public const DATE_FORMAT = 'Y-m-d H:i:s';

    public const DATE_ZONE = 'Europe/Warsaw';

    static public function subtractBusinessTime(\DateTime $date, ?int $days = 0, ?int $hours = 0, ?int $minutes = 0): \DateTime {
        $interval = new \DateInterval("P{$days}DT{$hours}H{$minutes}M");
        $date->sub($interval);
        $days--;
        while ($days > 0) {
            if ((int)$date->format('N') < 6) {
                $days--;
            }
            $date->sub($interval);
        }
        return $date;
    }
    static public function substractTime(\DateTime $date, ?int $days = 0, ?int $hours = 0, ?int $minutes = 0): \DateTime {
        $interval = new \DateInterval("P{$days}DT{$hours}H{$minutes}M");
        $date->sub($interval);

        return $date;
    }
}