<?php

namespace App\Core\Taxonomy;

class InpostTaxonomy {

    const ALLEGRO_PACZKOMATY = [
        'Allegro Paczkomaty InPost',
        'Allegro Paczkomaty InPost pobranie',
    ];

    const PACZKOMATY = [
        'Paczkomat Inpost',
        '<PERSON><PERSON><PERSON><PERSON><PERSON> InPost, <PERSON><PERSON><PERSON><PERSON> z góry,Prz<PERSON>yłka',
    ];

    const ALLEGRO_COURIER = [
        'Allegro Kurier24 InPost pobranie',
        'Allegro Kurier24 InPost',
    ];

    const VALUE_ALLEGRO_PACZKOMATY = 'inpost_locker_allegro';

    const VALUE_PACZKOMATY = 'inpost_locker_standard';

    const VALUE_ALLEGRO_COURIER = 'inpost_courier_allegro';


    private static array $nameToValueMap = [];

    private static function initialize() {
        if (empty(self::$nameToValueMap)) {
            $groups = [
                self::VALUE_ALLEGRO_PACZKOMATY => self::ALLEGRO_PACZKOMATY,
                self::VALUE_PACZKOMATY => self::PACZKOMATY,
                self::VALUE_ALLEGRO_COURIER => self::ALLEGRO_COURIER,
            ];

            foreach ($groups as $value => $names) {
                foreach ($names as $name) {
                    self::$nameToValueMap[$name] = $value;
                }
            }
        }
    }

    public static function getValueForName(string $name): ?string {
        self::initialize();
        return self::$nameToValueMap[$name] ?? null;
    }
}
