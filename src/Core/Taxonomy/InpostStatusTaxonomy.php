<?php

namespace App\Core\Taxonomy;

class InpostStatusTaxonomy {

    CONST INPOST_STATUS_CREATED = 'created';
    CONST INPOST_STATUS_DELIVERED = 'delivered';
    CONST INPOST_STATUS_RETURNED = 'returned';
    CONST INPOST_STATUS_CANCELED = 'canceled';
    CONST INPOST_SHIPMENT_STATUS_CREATED = 'shipment_created';
    CONST INPOST_SHIPMENT_STATUS_CHANGED = 'shipment_status_changed';
    CONST INPOST_SHIPMENT_STATUS_DELETED = 'shipment_deleted';



    public static function getPlStatus ($status) {
        return match ($status) {
            self::INPOST_STATUS_CREATED => 'St<PERSON>rzono',
            self::INPOST_STATUS_DELIVERED => 'Doręczono',
            self::INPOST_STATUS_RETURNED => 'Zwrócono',
            self::INPOST_STATUS_CANCELED => 'Anulowano',
            default => 'Unknown',
        };
    }


}