<?php

namespace App\Core\Taxonomy;
class DatabaseLoggerActionTaxonomy {

    public const ORDER_ADDED_TO_BASKET = 'ORDER ADDED TO BASKET';
    public const BASKET_ADD_ORDERS = 'BASKET ADD ORDERS';
    public const BASKET_CHANGE_STATUS = 'BASKET CHANGE STATUS';
    public const ORDER_CHANGE_STATUS = 'ORDER CHANGE STATUS';
    public const EMAIL_SENT = 'SEND EMAIL';
    public const SUBIEKT_ORDER_ADD = 'SUBIEKT ORDER ADD';
    public const MANUALLY_ADDED_COD_PRICE = 'MANUALLY ADDED COD PRICE';
    public const SUBIEKT_ERRORS = 'SUBIEKT ERRORS';
}