<?php

namespace App\Core\Taxonomy;

enum SubiektDocumentEnumTaxonomy: int
{
    case GTA_SUBIEKT_DOKUMENT_DOWOLNY = 0;
    case GTA_SUBIEKT_DOKUMENT_FZ = 0xFFFFFFFF; // -1
    case GTA_SUBIEKT_DOKUMENT_FS = 0xFFFFFFFE; // -2
    case GTA_SUBIEKT_DOKUMENT_FSD = 0xFFFFFFE0; // -32
    case GTA_SUBIEKT_DOKUMENT_RZ = 0xFFFFFFE3; // -29
    case GTA_SUBIEKT_DOKUMENT_RS = 0xFFFFFFE4; // -28
    case GTA_SUBIEKT_DOKUMENT_KFZ = 0xFFFFFFDA; // -38
    case GTA_SUBIEKT_DOKUMENT_KFS = 0xFFFFFFE6; // -26
    case GTA_SUBIEKT_DOKUMENT_MM = 0xFFFFFFE5; // -27
    case GTA_SUBIEKT_DOKUMENT_PZ = 0xFFFFFFFD; // -3
    case GTA_SUBIEKT_DOKUMENT_PZV = 0xFFFFFFD5; // -43
    case GTA_SUBIEKT_DOKUMENT_WZ = 0xFFFFFFFC; // -4
    case GTA_SUBIEKT_DOKUMENT_WZV = 0xFFFFFFD3; // -45
    case GTA_SUBIEKT_DOKUMENT_PW = 0xFFFFFFFB; // -5
    case GTA_SUBIEKT_DOKUMENT_RW = 0xFFFFFFFA; // -6
    case GTA_SUBIEKT_DOKUMENT_ZW = 0xFFFFFFE1; // -31
    case GTA_SUBIEKT_DOKUMENT_ZD = 0xFFFFFFF9; // -7
    case GTA_SUBIEKT_DOKUMENT_ZK = 0xFFFFFFF8; // -8
    case GTA_SUBIEKT_DOKUMENT_PA = 0xFFFFFFF7; // -9
    case GTA_SUBIEKT_DOKUMENT_RR = 0xFFFFFFDD; // -35
    case GTA_SUBIEKT_DOKUMENT_PAI = 0xFFFFFFF0; // -16
    case GTA_SUBIEKT_DOKUMENT_FZZ = 0xFFFFFFDC; // -36
    case GTA_SUBIEKT_DOKUMENT_FSZAL = 0xFFFFFFDE; // -34
    case GTA_SUBIEKT_DOKUMENT_FSZ = 0xFFFFFFDF; // -33
    case GTA_SUBIEKT_DOKUMENT_PAK = 0xFFFFFFD7; // -41
    case GTA_SUBIEKT_DOKUMENT_PAF = 0xFFFFFFD8; // -40
    case GTA_SUBIEKT_DOKUMENT_KFZN = 0xFFFFFFD9; // -39
    case GTA_SUBIEKT_DOKUMENT_KFSN = 0xFFFFFFDB; // -37
    case GTA_SUBIEKT_DOKUMENT_ZWN = 0xFFFFFFD2; // -46
    case GTA_SUBIEKT_DOKUMENT_ZWZ = 0xFFFFFFD1; // -47
    case GTA_SUBIEKT_DOKUMENT_ZPZ = 0xFFFFFFE2; // -30
    case GTA_SUBIEKT_DOKUMENT_IW = 0xFFFFFFE7; // -25
    case GTA_SUBIEKT_DOKUMENT_FM = 0xFFFFFF79; // -135
    case GTA_SUBIEKT_DOKUMENT_KFM = 0xFFFFFF74; // -140
    case GTA_SUBIEKT_DOKUMENT_KFMN = 0xFFFFFF73; // -141

    public static function getDocumentValueByName(string $name): ?int
    {
        return self::tryFromName($name)?->value;
    }

    public static function getAllDocumentTypes(): array
    {
        return array_reduce(
            self::cases(),
            fn($acc, $case) => $acc + [$case->name => $case->value],
            []
        );
    }

    private static function tryFromName(string $name): ?self
    {
        return self::tryFrom(constant("self::$name")) ?? null;
    }
}
