<?php

namespace App\Core\Taxonomy;

use ReflectionClass;

class OrderTaxonomy {
    
    public const STATUS_NEW = 'Nowe';
    
    public const STATUS_VERIFIED = 'WYSTWIENIE FV';
    
    public const STATUS_TO_SEND = 'Przygotowane do wysyłki';
    
    public const STATUS_LABEL_ERROR = 'Brak etykiety';
    
    public const STATUS_INVOICE_ERROR = 'Błąd wystawiania faktury';
    
    public const STATUS_CANCELLED = 'Anulowane';
    
    public const STATUS_UNPAID = 'BANK TRANSFER';
    
    public const STATUS_ZK_ERROR = 'Problem z tworzeniem ZK';
    
    public const STATUS_UNPAID_3_DAYS = 'Nieoplacony > 3 dni';
    
    public const STATUS_MAKE_INVOICE = 'WYstaw fakturę';
    
    public const STATUS_PAYMENT_ACCEPTED = 'Płatność zaakceptowana';
    
    public const STATUS_ERROR_CREATE_SHIPMENT = 'Błąd tworzenia dostawy';
    
    public const STATUS_SENT = 'Wysłano';
    
    public static function getAllStatuses(): array {
        $reflection = new ReflectionClass(__CLASS__);
        
        return array_values($reflection->getConstants());
    }
    
    public static function getAllStatusesForFilters(): array {
        $reflection = new ReflectionClass(__CLASS__);
        $constants = $reflection->getConstants();
        $options = [];
        foreach ($constants as $key => $value) {
            $options[] = [
                'value' => $key,
                'label' => $value
            ];
        }
        
        return $options;
    }
}