<?php

namespace App\Core\Taxonomy;

use App\Core\Entity\Order;
use Doctrine\ORM\EntityManagerInterface;

class OrderFilterTaxonomy {

    CONST FILTER_ARRAY = [
            [
                "field" => "id",
                "name" => "Identyfikator",
                "type" => "input"
            ],
            [
                "field" => "order_id",
                "name" => "Numer zamówienia",
                "type" => "input"
            ],
//        [
//            "field" => "shop_order_id",
//            "name" => "Numer zamówienia sklepu",
//            "type" => "input"
//        ],
            [
                "field" => "external_order_id",
                "name" => "Zewnętrzny numer zamówienia",
                "type" => "input"
            ],
//        [
//            "field" => "order_source",
//            "name" => "Źródło zamówienia",
//            "type" => "input"
//        ],
            [
                "field" => "order_source_id",
                "name" => "Źródło zamówienia",
                "type" => "select",
            ],
//        [
//            "field" => "order_source_info",
//            "name" => "Informacje o źródle zamówienia",
//            "type" => "input"
//        ],
//        [
//            "field" => "order_status_id",
//            "name" => "Identyfikator statusu zamówienia",
//            "type" => "input"
//        ],
//        [
//            "field" => "date_add",
//            "name" => "Data dodania",
//            "type" => "input"
//        ],
//        [
//            "field" => "date_confirmed",
//            "name" => "Data potwierdzenia",
//            "type" => "input"
//        ],
//        [
//            "field" => "date_in_status",
//            "name" => "Data w statusie",
//            "type" => "input"
//        ],
//        [
//            "field" => "confirmed",
//            "name" => "Potwierdzone",
//            "type" => "input"
//        ],
            [
                "field" => "user_login",
                "name" => "Login użytkownika",
                "type" => "input"
            ],
            [
                "field" => "currency",
                "name" => "Waluta",
                "type" => "input"
            ],
            [
                "field" => "payment_method",
                "name" => "Metoda płatności",
                "type" => "select"
            ],
            [
                "field" => "payment_method_cod",
                "name" => "Metoda płatności za pobraniem",
                "type" => "select"
            ],
            [
                "field" => "payment_done",
                "name" => "Płatność wykonana",
                "type" => "input"
            ],
            [
                "field" => "user_comments",
                "name" => "Komentarze użytkownika",
                "type" => "input"
            ],
            [
                "field" => "admin_comments",
                "name" => "Komentarze administratora",
                "type" => "input"
            ],
            [
                "field" => "email",
                "name" => "Email",
                "type" => "input"
            ],
            [
                "field" => "phone",
                "name" => "Telefon",
                "type" => "input"
            ],
            [
                "field" => "extra_field_1",
                "name" => "Dodatkowe pole 1",
                "type" => "input"
            ],
            [
                "field" => "extra_field_2",
                "name" => "Dodatkowe pole 2",
                "type" => "input"
            ],
//        [
//            "field" => "custom_extra_fields",
//            "name" => "Niestandardowe dodatkowe pola",
//            "type" => "input"
//        ],
//        [
//            "field" => "order_page",
//            "name" => "Strona zamówienia",
//            "type" => "input"
//        ],
//        [
//            "field" => "pick_state",
//            "name" => "Stan wyboru",
//            "type" => "input"
//        ],
//        [
//            "field" => "pack_state",
//            "name" => "Stan pakowania",
//            "type" => "input"
//        ],
//        [
//            "field" => "status_date",
//            "name" => "Data statusu",
//            "type" => "input"
//        ],
//        [
//            "field" => "discount_value",
//            "name" => "Wartość rabatu",
//            "type" => "input"
//        ],
            [
                "field" => "discount_name",
                "name" => "Nazwa rabatu",
                "type" => "input"
            ],
            [
                "field" => "full_price",
                "name" => "Pełna cena",
                "type" => "input"
            ],
            [
                "field" => "products_id",
                "name" => "Identyfikator produktów",
                "type" => "input"
            ],
//        [
//            "field" => "products_storage",
//            "name" => "Magazyn produktów",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_storage_id",
//            "name" => "Identyfikator magazynu produktów",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_order_product_id",
//            "name" => "Identyfikator zamówionego produktu",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_product_id",
//            "name" => "Identyfikator produktu",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_variant_id",
//            "name" => "Identyfikator wariantu produktu",
//            "type" => "input"
//        ],
            [
                "field" => "products_name",
                "name" => "Nazwa produktu",
                "type" => "input"
            ],
            [
                "field" => "products_sku",
                "name" => "SKU produktu",
                "type" => "input"
            ],
            [
                "field" => "products_ean",
                "name" => "EAN produktu",
                "type" => "input"
            ],
//        [
//            "field" => "products_location",
//            "name" => "Lokalizacja produktu",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_warehouse_id",
//            "name" => "Identyfikator magazynu produktu",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_auction_id",
//            "name" => "Identyfikator aukcji produktu",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_attributes",
//            "name" => "Atrybuty produktu",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_price_brutto",
//            "name" => "Cena brutto produktu",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_tax_rate",
//            "name" => "Stawka podatku produktu",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_quantity",
//            "name" => "Ilość produktu",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_weight",
//            "name" => "Waga produktu",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_bundle_id",
//            "name" => "Identyfikator pakietu produktu",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_cover_image_url",
//            "name" => "URL zdjęcia produktu",
//            "type" => "input"
//        ],
//        [
//            "field" => "products_full_price",
//            "name" => "Pełna cena produktu",
//            "type" => "input"
//        ],
            [
                "field" => "internal_status_id",
                "name" => "Identyfikator statusu wewnętrznego",
                "type" => "select"
            ],
//            [
//                "field" => "internal_status_id_name",
//                "name" => "Nazwa statusu wewnętrznego",
//                "type" => "input"
//            ],
//        [
//            "field" => "orderDelivery_id",
//            "name" => "Identyfikator dostawy zamówienia",
//            "type" => "input"
//        ],
            [
                "field" => "orderDelivery_delivery_method",
                "name" => "Metoda dostawy",
                "type" => "select"
            ],
            [
                "field" => "orderDelivery_delivery_price",
                "name" => "Cena dostawy",
                "type" => "input"
            ],
//        [
//            "field" => "orderDelivery_delivery_package_module",
//            "name" => "Moduł paczki dostawy",
//            "type" => "input"
//        ],
            [
                "field" => "orderDelivery_delivery_package_nr",
                "name" => "Numer paczki dostawy",
                "type" => "input"
            ],
            [
                "field" => "orderDelivery_delivery_fullname",
                "name" => "Pełne imię i nazwisko dostawy",
                "type" => "input"
            ],
//        [
//            "field" => "orderDelivery_delivery_company",
//            "name" => "Firma dostawy",
//            "type" => "input"
//        ],
//        [
//            "field" => "orderDelivery_delivery_address",
//            "name" => "Adres dostawy",
//            "type" => "input"
//        ],
//        [
//            "field" => "orderDelivery_delivery_postcode",
//            "name" => "Kod pocztowy dostawy",
//            "type" => "input"
//        ],
//        [
//            "field" => "orderDelivery_delivery_city",
//            "name" => "Miasto dostawy",
//            "type" => "input"
//        ],
//        [
//            "field" => "orderDelivery_delivery_state",
//            "name" => "Stan dostawy",
//            "type" => "input"
//        ],
//        [
//            "field" => "orderDelivery_delivery_country",
//            "name" => "Kraj dostawy",
//            "type" => "input"
//        ],
//        [
//            "field" => "orderDelivery_delivery_country_code",
//            "name" => "Kod kraju dostawy",
//            "type" => "input"
//        ],
            [
                "field" => "orderDelivery_delivery_point_id",
                "name" => "Identyfikator punktu dostawy",
                "type" => "input"
            ],
            [
                "field" => "orderDelivery_delivery_point_name",
                "name" => "Nazwa punktu dostawy",
                "type" => "input"
            ],
//        [
//            "field" => "orderDelivery_delivery_point_address",
//            "name" => "Adres punktu dostawy",
//            "type" => "input"
//        ],
//        [
//            "field" => "orderDelivery_delivery_point_postcode",
//            "name" => "Kod pocztowy punktu dostawy",
//            "type" => "input"
//        ],
//        [
//            "field" => "orderDelivery_delivery_point_city",
//            "name" => "Miasto punktu dostawy",
//            "type" => "input"
//        ],
//        [
//            "field" => "orderDelivery_delivery_id",
//            "name" => "Identyfikator dostawy",
//            "type" => "input"
//        ],
//        [
//            "field" => "orderInvoice_id",
//            "name" => "Identyfikator faktury zamówienia",
//            "type" => "input"
//        ],
            [
                "field" => "orderInvoice_invoice_fullname",
                "name" => "Pełne imię i nazwisko na fakturze",
                "type" => "input"
            ],
            [
                "field" => "orderInvoice_invoice_company",
                "name" => "Firma na fakturze",
                "type" => "input"
            ],
            [
                "field" => "orderInvoice_invoice_nip",
                "name" => "NIP na fakturze",
                "type" => "input"
            ],
            [
                "field" => "orderInvoice_invoice_address",
                "name" => "Adres na fakturze",
                "type" => "input"
            ],
            [
                "field" => "orderInvoice_invoice_postcode",
                "name" => "Kod pocztowy na fakturze",
                "type" => "input"
            ],
            [
                "field" => "orderInvoice_invoice_city",
                "name" => "Miasto na fakturze",
                "type" => "input"
            ],
            [
                "field" => "orderInvoice_invoice_state",
                "name" => "Stan na fakturze",
                "type" => "input"
            ],
            [
                "field" => "orderInvoice_invoice_country",
                "name" => "Kraj na fakturze",
                "type" => "input"
            ],
            [
                "field" => "orderInvoice_invoice_country_code",
                "name" => "Kod kraju na fakturze",
                "type" => "input"
            ],
            [
                "field" => "orderInvoice_want_invoice",
                "name" => "Chcę fakturę",
                "type" => "input"
            ],
            [
                "field" => "orderInvoice_invoice_number",
                "name" => "Numer faktury",
                "type" => "input"
            ],
            [
                "field" => "dateFrom",
                "name" => "Data od",
                "type" => "datePicker"
            ],
            [
                "field" => "dateTo",
                "name" => "Data do",
                "type" => "datePicker"
            ],
        ];
}