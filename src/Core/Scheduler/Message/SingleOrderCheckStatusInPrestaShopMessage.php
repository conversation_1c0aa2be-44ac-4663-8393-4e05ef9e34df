<?php

namespace App\Core\Scheduler\Message;

use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use Symfony\Component\Uid\Uuid;

class SingleOrderCheckStatusInPrestaShopMessage {

    private int $integration;

    private Uuid $orderId;

    public function __construct(Integration $integration, Order $order) {
        $this->integration = $integration->getId();
        $this->orderId = $order->getId();
    }

    public function getIntegration(): int {
        return $this->integration;
    }

    public function getOrderId(): Uuid {
        return $this->orderId;
    }
}