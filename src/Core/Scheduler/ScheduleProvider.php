<?php

namespace App\Core\Scheduler;

use App\Core\Entity\Integration;
use App\Core\Scheduler\Message\{OrderCheckStatusInPrestaShopMessage,
    OrderInStatusTimeRule,
    PrestashopFetchOrdersMessage,
    SubiektDocumentFSMessage,
    SubiektDocumentZKMessage};
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Scheduler\Attribute\AsSchedule;
use Symfony\Component\Scheduler\RecurringMessage;
use Symfony\Component\Scheduler\Schedule;
use Symfony\Component\Scheduler\ScheduleProviderInterface;
use Symfony\Contracts\Cache\CacheInterface;

#[AsSchedule]
readonly class ScheduleProvider implements ScheduleProviderInterface {

    public function __construct(private EntityManagerInterface $entityManager, private CacheInterface $cache){}

    public function getSchedule(): Schedule
    {
        $schedule = new Schedule();
        $prestashopIntegrations = $this->entityManager->getRepository(Integration::class)->findBy(['type' => 'prestashop']);
        foreach ($prestashopIntegrations as $prestashopIntegration) {
            $interval = $prestashopIntegration->getIntegrationData()->getOrderFetchInterval();
            $schedule->add(RecurringMessage::every($interval->value . ' minutes', new PrestashopFetchOrdersMessage($prestashopIntegration)));
            $schedule->add(RecurringMessage::every($interval->value . ' minutes', new OrderCheckStatusInPrestaShopMessage($prestashopIntegration)));
        }
        $schedule->add(RecurringMessage::every('30 minutes', new OrderInStatusTimeRule()));
        $schedule->add(RecurringMessage::every('5 minutes', new SubiektDocumentZKMessage()));
        $schedule->add(RecurringMessage::every('5 minutes', new SubiektDocumentFSMessage()));

        return $schedule->stateful($this->cache);
    }
}