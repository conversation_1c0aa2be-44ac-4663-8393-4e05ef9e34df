<?php

namespace App\Core\Scheduler;

use App\Core\Scheduler\Message\RegenerateMailMessage;
use Symfony\Component\Scheduler\Attribute\AsSchedule;
use Symfony\Component\Scheduler\RecurringMessage;
use Symfony\Component\Scheduler\Schedule;
use Symfony\Component\Scheduler\ScheduleProviderInterface;

#[AsSchedule(name: 'dev_scheduler')]
readonly class DevScheduleProvider implements ScheduleProviderInterface {

    public function __construct(){}
    public function getSchedule(): Schedule {

        $schedule = new Schedule();
        $schedule->add(RecurringMessage::every('10 seconds', new RegenerateMailMessage()));

        return $schedule;
    }
}