<?php

namespace App\Core\Scheduler\Handler;

use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Scheduler\Message\SingleOrderCheckStatusInPrestaShopMessage;
use App\Core\Service\Fetcher\Order\PrestashopOrderFetcherService;
use App\Core\Service\LockService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class SingleOrderCheckStatusInPrestaShopHandler {

    public function __construct(
        private LockService        $lockService,
        private PrestashopOrderFetcherService $fetcherService,
        private EntityManagerInterface $entityManager,
    ) {
    }

    public function __invoke(SingleOrderCheckStatusInPrestaShopMessage $message): void {
        $this->lockService->acquireLock('single_order_prestashop_status_handler', function () use ($message) {
            $integrationEntity = $this->entityManager->getRepository(Integration::class)->find($message->getIntegration());
            $order = $this->entityManager->getRepository(Order::class)->find($message->getOrderId());
            try {
                $this->fetcherService->checkOrdersState($integrationEntity, [$order]);

            } catch (\Throwable $throwable) {
                throw $throwable;
            }
        });
    }
}