<?php

namespace App\Core\Scheduler\Handler;

use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Entity\User;
use App\Core\Rules\RuleTrigger;
use App\Core\Scheduler\Message\SubiektDocumentFSMessage;
use App\Core\Service\LockService;
use App\Core\Service\LoggerService;
use App\Core\Service\Subiekt\Orders\SubiektOrdersService;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Monolog\Attribute\WithMonologChannel;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
#[WithMonologChannel('subiekt')]
readonly class SubiektDocumentFSHandler {
    
    public function __construct(
        private LoggerInterface $logger,
        private EntityManagerInterface $entityManager,
        private LockService $lockService,
        private SubiektOrdersService $subiektOrdersService,
        private LoggerService $loggerService,
        private RuleTrigger $ruleTrigger
    ){}
    
    public function __invoke(SubiektDocumentFSMessage $subiektDocumentMessage): void {
        $this->lockService->acquireLock('subiekt_document_fs', function () {
            $this->logger->info('SubiektDocumentFSHandler');
            $ordersInvoiceError = $this->entityManager->getRepository(Order::class)->findBy(['internal_status_id' => $this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_INVOICE_ERROR])]);
            $ordersMakeInvoice = $this->entityManager->getRepository(Order::class)->findBy(['internal_status_id' => $this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_VERIFIED])]);
            $orders = array_merge($ordersInvoiceError, $ordersMakeInvoice);
            $user = $this->entityManager->getRepository(User::class)->findOneBy(['username' => 'system']);
            $statusPaymentAccepted = $this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_PAYMENT_ACCEPTED]);
            $statusInvoiceError = $this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_INVOICE_ERROR]);
            foreach ($orders as $order) {
                if ($order->checkIfFSExists()) {
                    $this->ruleTrigger->triggerEvent('order.fs.create.success', ['order' => $order]);
                    continue;
                }
                try {
                    $invoiceNo = $this->subiektOrdersService->makeSaleDoc($order);
                    if ('success' === $invoiceNo['status'] && isset($invoiceNo['content']['doc_state']) && 'ok' === $invoiceNo['content']['doc_state']) {
                        $this->ruleTrigger->triggerEvent('order.fs.create.success', ['order' => $order]);
                        $message = 'Invoice created';
                        $content = $invoiceNo['content']['doc_ref'] ?? '';
                        $contentType = 'invoiceCreated';
                        $code = 200;
                        $this->loggerService->logInvoiceError($order, $message, $content, $contentType, $code, $user);
                    } else {
                        $message = $invoiceNo['message'] ?? '';
                        $content = $invoiceNo['content'] ?? '';
                        if ('Nie można utworzyć dokumentu sprzedaży. Brakuje produktów na magazynie.' === $message) {
                            $contentType = 'missingProducts';
                        }
                        else if ('Nie odnaleziono dokumentu' === $message) {
                            $contentType = 'missingDocument';
                        }
                        else if ('Nie można wystawić dokumentu z wartością 0' === $message) {
                            $contentType = 'zeroValue';
                        }
                        else {
                            $contentType = '';
                        }
                        $code = $invoiceNo['code'] ?? null;
                        $this->loggerService->logInvoiceError($order, $message, $content, $contentType, $code, $user);
                        $this->ruleTrigger->triggerEvent('order.fs.create.error', ['order' => $order]);
                    }
                } catch (\Exception $exception) {
                    $content = $exception->getMessage();
                    $contentType = 'catch';
                    $message = 'Catch on create order invoice';
                    $code = $exception->getCode();
                    $this->loggerService->logInvoiceError($order, $message, $content, $contentType, $code, $user);
                    $this->ruleTrigger->triggerEvent('order.fs.create.error', ['order' => $order]);
                }
            }
        });
    }
}
