<?php

namespace App\Core\Scheduler\Handler;

use App\Core\Rules\RuleService;
use App\Core\Scheduler\Message\OrderInStatusTimeRule;
use App\Core\Service\LockService;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class OrderInStatusTimeRuleHandler {

    public function __construct(
        private LockService        $lockService,
        private RuleService $ruleService
    ) {
    }

    public function __invoke(OrderInStatusTimeRule $message): void {
        $this->lockService->acquireLock(OrderInStatusTimeRuleHandler::class, function () use ($message) {
            $this->ruleService->checkOrderInStatusTimeRules();
        });
    }
}