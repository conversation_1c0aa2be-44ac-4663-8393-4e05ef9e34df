<?php

namespace App\Core\Scheduler\Handler;

use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use App\Core\Scheduler\Message\SubiektDocumentZKMessage;
use App\Core\Service\Communicator\CommunicatorService;
use App\Core\Service\LockService;
use App\Core\Service\Subiekt\Orders\SubiektOrdersService;
use App\Core\Taxonomy\CommunicatorTaxonomy;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Monolog\Attribute\WithMonologChannel;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
#[WithMonologChannel('subiekt')]
readonly class SubiektDocumentZKHandler {
    
    public function __construct(private LoggerInterface $logger, private EntityManagerInterface $entityManager, private LockService $lockService, private SubiektOrdersService $subiektOrdersService, private CommunicatorService $communicatorService) {}
    
    public function __invoke(SubiektDocumentZKMessage $subiektDocumentMessage): void {
        $this->lockService->acquireLock('subiekt_document_zk', function () {
            $this->logger->info('SubiektDocumentZKHandler');
            $orders = $this->entityManager->getRepository(Order::class)->findBy(['internal_status_id' => $this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_ZK_ERROR])]);
            $countInvoiceAdded = 0;
            $countOrderInvoices = count($orders);
            foreach ($orders as $order) {
                $invoiceNumber = $order->getOrderInvoice()->getInvoiceNumber();
                if (!is_null($invoiceNumber) && str_starts_with($invoiceNumber, 'ZK')) {
                    $order->setInternalStatusId($this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_NEW]));
                    $this->entityManager->flush();
                    continue;
                }
                try {
                    if ($invoiceNo = $this->subiektOrdersService->addOrder($order)) {
                        $orderInvoice = $order->getOrderInvoice();
                        $orderInvoice->setInvoiceNumber($invoiceNo);
                        $this->entityManager->persist($orderInvoice);
                        $order->setInternalStatusId($this->entityManager->getRepository(OrderStatus::class)->findOneBy(['name' => OrderTaxonomy::STATUS_NEW]));
                        $this->entityManager->flush();
                        $countInvoiceAdded++;
                    }
                } catch (\Exception $exception) {
                }
            }
            $this->logger->info('Got  ' . $countOrderInvoices . ' orders without ZK');
            $this->logger->info('create  ' . $countInvoiceAdded . ' ZK');
            $message = 'Got  ' . $countOrderInvoices . ' orders without ZK ' . '. Created  ' . $countInvoiceAdded . ' ZK';
        });
    }
}