<?php

namespace App\Core\Scheduler\Handler;

use App\Core\Scheduler\Message\PrestashopFetchOrdersMessage;
use App\Core\Service\Communicator\CommunicatorService;
use App\Core\Service\Fetcher\Order\PrestashopOrderFetcherService;
use App\Core\Service\LockService;
use App\Core\Taxonomy\CommunicatorTaxonomy;
use Monolog\Attribute\WithMonologChannel;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
#[WithMonologChannel('prestashop')]
readonly class PrestashopFetchOrdersHandler {

    public function __construct(
        private PrestashopOrderFetcherService $PrestashopOrderFetcherService,
        private LoggerInterface              $prestashopLogger,
        private LockService                  $lockService
    ) {
    }
    public function __invoke(PrestashopFetchOrdersMessage $prestashopFetchOrdersMessage): void {
        $this->lockService->acquireLock('prestashop_fetch_orders', function () use ($prestashopFetchOrdersMessage) {
            $integrationEntity = $prestashopFetchOrdersMessage->getIntegration();
            $newOrders = $this->PrestashopOrderFetcherService->fetchAllOrders($integrationEntity);
            $this->prestashopLogger->info('Fetched orders from Prestashop', ['new Orders' => $newOrders]);
        });
    }
}