<?php

namespace App\Core\Scheduler\Handler;

use App\Core\Entity\Order;
use App\Core\Scheduler\Message\OrderCheckStatusInPrestaShopMessage;
use App\Core\Scheduler\Message\SingleOrderCheckStatusInPrestaShopMessage;
use App\Core\Service\LockService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsMessageHandler]
readonly class OrderCheckStatusInPrestaShopHandler {

    public function __construct(
        private LockService        $lockService,
        private EntityManagerInterface $entityManager,
        private MessageBusInterface $messageBus
    ) {
    }

    public function __invoke(OrderCheckStatusInPrestaShopMessage $message): void {
        $this->lockService->acquireLock('order_prestashop_status_handler', function () use ($message) {
            $integrationEntity = $message->getIntegration();
            $interval = new \DateInterval('P14D');
            $date = (new \DateTimeImmutable('now'))->sub($interval);
            $orders = $this->entityManager->getRepository(Order::class)->getUnpaidByDate($date, $integrationEntity);
            try {
                foreach ($orders as $order) {
                    $message = new SingleOrderCheckStatusInPrestaShopMessage($integrationEntity, $order);
                    $this->messageBus->dispatch($message);
                }
            } catch (\Throwable $throwable) {
                throw $throwable;
            }
        });
    }
}