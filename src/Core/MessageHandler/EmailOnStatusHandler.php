<?php

namespace App\Core\MessageHandler;

use App\Core\Entity\Order;
use App\Core\Entity\OrderEmailOnStatus;
use App\Core\Entity\OrderStatus;
use App\Core\Message\EmailOnStatusMessage;
use App\Core\Service\EmailEvent\EmailEventService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class EmailOnStatusHandler {
    public function __construct(
        private EntityManagerInterface $entityManager,
        private EmailEventService $emailEventService,
    ) {
    }

    public function __invoke(EmailOnStatusMessage $message) {
        $order = $this->entityManager->getRepository(Order::class)->find($message->getOrderId());
        $status = $this->entityManager->getRepository(OrderStatus::class)->find($message->getStatusId());
        $emailOnStatus = $this->entityManager->getRepository(OrderEmailOnStatus::class)->find($message->getEmailOnStatusId());
        $this->emailEventService->sendEmailOnStatus($order, $emailOnStatus);
    }
}