<?php

namespace App\Core\MessageHandler;

use App\Core\Entity\Order;
use App\Core\Message\CreateInvoiceMessage;
use App\Core\Rules\RuleTrigger;
use App\Core\Service\OrderStatusService;
use App\Core\Service\Subiekt\Orders\SubiektOrdersService;
use App\Core\Taxonomy\OrderTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class CreateInvoiceHandler {
    
    public function __construct(
        private SubiektOrdersService $subiektOrdersService,
        private EntityManagerInterface $entityManager,
        private LoggerInterface $logger,
        private RuleTrigger $ruleTrigger
    ) {}
    
    public function __invoke(CreateInvoiceMessage $message) {
        $order = $this->entityManager->getRepository(Order::class)->find($message->getOrderId());
        if (!$order) {
            $this->logger->error("Order not found: " . $message->getOrderId());
            
            return;
        }
        try {
            $response = $this->subiektOrdersService->makeSaleDoc($order);
            if ($response['status'] === 'success' && isset($response['content'])) {
                if ($response['message'] === 'Order already realized, invoice number assigned') {
                    $this->logger->info("Order ID {$order->getId()} already realized. Invoice number assigned: {$response['content']}");
                    $this->ruleTrigger->triggerEvent('order.fs.create.success', ['order' => $order]);
                    return;
                }
                $orderInvoice = $order->getOrderInvoice();
                $orderInvoice->setInvoiceNumber($response['content']['doc_ref']);
                $this->entityManager->persist($orderInvoice);
                $this->entityManager->flush();
                $this->logger->info("Invoice created successfully for Order ID: " . $order->getId());
                $this->ruleTrigger->triggerEvent('order.fs.create.success', ['order' => $order]);
            }
            else if ($response['status'] === 'error') {
                if (str_contains($response['message'], 'Order already has an invoice')) {
                    $this->ruleTrigger->triggerEvent('order.fs.create.success', ['order' => $order]);
                    $this->logger->warning("Invoice creation skipped: Order already has an invoice for Order ID: " . $order->getId());
                    return;
                }
                else if (str_contains($response['message'], 'Order is not ready for invoice')) {
                    $this->ruleTrigger->triggerEvent('order.fs.create.error', ['order' => $order]);
                    $this->logger->warning("Invoice creation skipped: Order not ready for invoice for Order ID: " . $order->getId());
                }
                else {
                    $this->logger->error("Failed to create invoice for Order ID: " . $order->getId() . " - " . $response['message']);
                    $this->ruleTrigger->triggerEvent('order.fs.create.error', ['order' => $order]);
                }
            }
        } catch (\Exception $exception) {
            $this->logger->error("Exception in CreateInvoiceHandler: " . $exception->getMessage());
            $this->ruleTrigger->triggerEvent('order.fs.create.error', ['order' => $order]);
        }
    }
}