<?php

namespace App\Core\MessageHandler;

use App\Core\Entity\Integration;
use App\Core\Message\FetchSingleOrderMessage;
use App\Core\Service\Fetcher\Order\PrestashopOrderFetcherService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Lock\LockFactory;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler(fromTransport: 'doctrine_async_fetch_single_order')]
readonly class FetchSingleOrder {
    
    public function __construct(
        private PrestashopOrderFetcherService $fetcherService,
        private EntityManagerInterface $entityManager,
        private LockFactory $lockFactory
    ) {}
    
    public function __invoke(FetchSingleOrderMessage $message) {
        $lock = $this->lockFactory->createLock('fetch_single_order_' . $message->getExternalOrderId());
        $lock->acquire(true);
        try {
            $this->fetcherService->fetchOrder($message->getExternalOrderId(), $this->entityManager->getRepository(Integration::class)->find($message->getIntegrationId()));
        } catch (Exception $exception) {
            throw $exception;
        } finally {
            $lock->release();
        }

    }
}
