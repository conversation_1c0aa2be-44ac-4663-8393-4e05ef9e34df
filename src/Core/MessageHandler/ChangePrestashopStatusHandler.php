<?php

namespace App\Core\MessageHandler;

use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Message\ChangePrestashopStatusMessenger;
use App\Core\Service\Fetcher\Api\PrestashopApiService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler(fromTransport: 'doctrine_async_change_prestashop_status')]
readonly class ChangePrestashopStatusHandler {
    public function __construct(
        private EntityManagerInterface $entityManager,
        private PrestashopApiService   $prestashopApiService
    ) {
    }

    public function __invoke(ChangePrestashopStatusMessenger $message): void {
        $integration = $this->entityManager->getRepository(Integration::class)->find($message->getIntegrationId());
        if (null === $integration) {
            throw new \Exception('Integration not found');
        }
        $order = $this->entityManager->getRepository(Order::class)->find($message->getOrderId());
        if (null === $order) {
            throw new \Exception('Order not found');
        }
        try {
            $this->handlePrestashopOrderStatusChange($order, $integration, $message->getStatus());
        } catch (\Exception $e) {
            throw $e;
        }
    }

    private function handlePrestashopOrderStatusChange(Order $order, Integration $integration, $newStatusId): void {
        $this->prestashopApiService->setConnectionData($integration->getIntegrationData()->getApiUrl(), $integration->getIntegrationData()->getApiToken());
        $this->prestashopApiService->updateOrderStatusViaHistory($order->getShopOrderId(), $newStatusId);
    }
}
