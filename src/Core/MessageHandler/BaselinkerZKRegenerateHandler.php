<?php

namespace App\Core\MessageHandler;

use App\Core\Entity\Order;
use App\Core\Message\BaselinkerZKRegenerate;
use App\Core\Service\Communicator\CommunicatorService;
use App\Core\Service\Fetcher\Order\BaselinkerOrderFetcherService;
use App\Core\Taxonomy\CommunicatorTaxonomy;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class BaselinkerZKRegenerateHandler {
    public function __construct(
        private BaselinkerOrderFetcherService $baselinkerOrderFetcherService,
        private EntityManagerInterface $entityManager,
        private CommunicatorService $communicatorService,
    ) {}

    public function __invoke(BaselinkerZKRegenerate $message): void {
        try {
            sleep(1);
            $result = $this->baselinkerOrderFetcherService->getOrderById($message->getOriginalNumber());
            if ($orderEntity = $this->entityManager->getRepository(Order::class)->findOneBy(['shop_order_id' => $result['shop_order_id']])) {
                if (str_starts_with($orderEntity->getOrderInvoice()->getInvoiceNumber(), 'FS')) {
                    return;
                }
                $orderEntity->getOrderInvoice()->setInvoiceNumber($message->getZkNumber());
                $this->entityManager->persist($orderEntity);
                $this->entityManager->flush();
            }
        } catch (\Exception $exception) {
            $this->communicatorService->sendTelegramMessage(CommunicatorTaxonomy::TELEGRAM_INFO_CHAT, sprintf('W trakcie regeneracji orignal number %s exception %s', $message->getOriginalNumber(), $exception->getMessage()));
            throw new \Exception(sprintf('W trakcie regeneracji orignal number %s exception %s', $message->getOriginalNumber(), $exception->getMessage()));

        }
    }
}