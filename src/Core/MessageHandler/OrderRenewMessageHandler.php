<?php

namespace App\Core\MessageHandler;

use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Message\OrderRenewMessage;
use App\Core\Service\Fetcher\Order\PrestashopOrderFetcherService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class OrderRenewMessageHandler {
    public function __construct(
        private EntityManagerInterface $entityManager,
        private PrestashopOrderFetcherService $prestashopApiService,
    ) {
    }

    public function __invoke(OrderRenewMessage $message) {
        $order = $this->entityManager->getRepository(Order::class)->find($message->getOrderId());
        $integration = $this->entityManager->getRepository(Integration::class)->find(1);
        $this->prestashopApiService->renewPrice($order, $integration);
    }
}