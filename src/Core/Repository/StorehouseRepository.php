<?php

namespace App\Core\Repository;

use App\Core\Entity\Storehouse;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Storehouse>
 *
 * @method Storehouse|null find($id, $lockMode = null, $lockVersion = null)
 * @method Storehouse|null findOneBy(array $criteria, array $orderBy = null)
 * @method Storehouse[]    findAll()
 * @method Storehouse[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class StorehouseRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Storehouse::class);
    }

    //    /**
    //     * @return Storehouse[] Returns an array of Storehouse objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('s')
    //            ->andWhere('s.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('s.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Storehouse
    //    {
    //        return $this->createQueryBuilder('s')
    //            ->andWhere('s.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
