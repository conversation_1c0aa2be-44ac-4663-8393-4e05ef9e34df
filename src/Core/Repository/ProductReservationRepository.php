<?php

namespace App\Core\Repository;

use App\Core\Entity\ProductReservation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ProductReservation>
 *
 * @method ProductReservation|null find($id, $lockMode = null, $lockVersion = null)
 * @method ProductReservation|null findOneBy(array $criteria, array $orderBy = null)
 * @method ProductReservation[]    findAll()
 * @method ProductReservation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProductReservationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProductReservation::class);
    }

    //    /**
    //     * @return ProductReservation[] Returns an array of ProductReservation objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('p.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?ProductReservation
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
