<?php

namespace App\Core\Repository;

use App\Core\Entity\DocumentStocktaking;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<DocumentStocktaking>
 *
 * @method DocumentStocktaking|null find($id, $lockMode = null, $lockVersion = null)
 * @method DocumentStocktaking|null findOneBy(array $criteria, array $orderBy = null)
 * @method DocumentStocktaking[]    findAll()
 * @method DocumentStocktaking[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DocumentStocktakingRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DocumentStocktaking::class);
    }

    //    /**
    //     * @return DocumentStocktaking[] Returns an array of DocumentStocktaking objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('d')
    //            ->andWhere('d.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('d.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?DocumentStocktaking
    //    {
    //        return $this->createQueryBuilder('d')
    //            ->andWhere('d.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
