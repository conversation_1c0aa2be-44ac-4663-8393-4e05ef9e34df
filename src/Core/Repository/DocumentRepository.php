<?php

namespace App\Core\Repository;

use App\Core\Entity\Document;
use App\Core\Entity\DocumentType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bridge\Doctrine\Types\UuidType;

/**
 * @extends ServiceEntityRepository<Document>
 *
 * @method Document|null find($id, $lockMode = null, $lockVersion = null)
 * @method Document|null findOneBy(array $criteria, array $orderBy = null)
 * @method Document[]    findAll()
 * @method Document[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DocumentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Document::class);
    }

    public function findAllByColumns(array $columns): array {
        $qb = $this->createQueryBuilder('d');

        foreach ($columns as $column) {
            $qb->addSelect('d.' . $column);
        }

        return $qb->getQuery()->getResult();
    }

    public function countDocumentsByTypeAndMonth(DocumentType $documentType, int $year, int $month): int {


        $qb = $this->createQueryBuilder('d');

        $startDate = new \DateTimeImmutable("$year-$month-01 00:00:00");
        $endDate = $startDate->modify('last day of this month')->setTime(23, 59, 59);

        $qb->select('COUNT(d.id)')
            ->leftJoin('d.type', 'dt')
            ->where('dt.id = :type')
            ->andWhere('d.createdAt >= :startDate')
            ->andWhere('d.createdAt <= :endDate')
            ->setParameter('type', $documentType->getId(), UuidType::NAME)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
;
        return (int) $qb->getQuery()->getSingleScalarResult();
    }




//    /**
//     * @return Document[] Returns an array of Document objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?Document
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
