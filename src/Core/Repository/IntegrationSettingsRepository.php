<?php

namespace App\Core\Repository;

use App\Core\Entity\IntegrationSettings;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<IntegrationSettings>
 *
 * @method IntegrationSettings|null find($id, $lockMode = null, $lockVersion = null)
 * @method IntegrationSettings|null findOneBy(array $criteria, array $orderBy = null)
 * @method IntegrationSettings[]    findAll()
 * @method IntegrationSettings[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class IntegrationSettingsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, IntegrationSettings::class);
    }

    //    /**
    //     * @return IntegrationSettings[] Returns an array of IntegrationSettings objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('i')
    //            ->andWhere('i.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('i.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?IntegrationSettings
    //    {
    //        return $this->createQueryBuilder('i')
    //            ->andWhere('i.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
