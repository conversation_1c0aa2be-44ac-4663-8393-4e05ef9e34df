<?php

namespace App\Core\Repository;

use App\Core\Entity\DocumentShelfStocktakingData;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<DocumentShelfStocktakingData>
 *
 * @method DocumentShelfStocktakingData|null find($id, $lockMode = null, $lockVersion = null)
 * @method DocumentShelfStocktakingData|null findOneBy(array $criteria, array $orderBy = null)
 * @method DocumentShelfStocktakingData[]    findAll()
 * @method DocumentShelfStocktakingData[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DocumentShelfStocktakingDataRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DocumentShelfStocktakingData::class);
    }

    //    /**
    //     * @return DocumentShelfStocktakingData[] Returns an array of DocumentShelfStocktakingData objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('d')
    //            ->andWhere('d.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('d.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?DocumentShelfStocktakingData
    //    {
    //        return $this->createQueryBuilder('d')
    //            ->andWhere('d.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
