<?php

namespace App\Core\Repository;

use App\Core\Entity\EanShelfQuantity;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\ParameterType;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EanShelfQuantity>
 *
 * @method EanShelfQuantity|null find($id, $lockMode = null, $lockVersion = null)
 * @method EanShelfQuantity|null findOneBy(array $criteria, array $orderBy = null)
 * @method EanShelfQuantity[]    findAll()
 * @method EanShelfQuantity[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EanShelfQuantityRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EanShelfQuantity::class);
    }

    public function filterByEansAndShelves($eans, $shelves): array {
        $eansIds =  array_map(function($ean) {
            return $ean->getId()->toBinary();
        }, $eans);
        $eansIds = implode(',', $eansIds);
        $shelvesIds =  array_map(function($shelf) {
            return $shelf->getId()->toBinary();
        }, $shelves);
        $shelvesIds = implode(',', $shelvesIds);
        $builder = $this->createQueryBuilder('e');
        $results = $builder->select('e')
            ->andWhere($builder->expr()->in('e.ean', ':eansIds'))
            ->andWhere($builder->expr()->in('e.shelf', ':shelvesIds'))
            ->setParameter('eansIds', $eansIds, ParameterType::BINARY)
            ->setParameter('shelvesIds', $shelvesIds, ParameterType::BINARY)
            ->getQuery()
            ->getResult();

        $esqs = [];
        if (!empty($results)) {
            foreach ($results as $esq) {
                if (!in_array($esq, $esqs, TRUE)) {
                    $esqs[] = $esq;

                }
            }
        }

        return $esqs;
    }
    public function filterByEansAndNotShelves($eans, $shelves): array {
        $eansIds =  array_map(function($ean) {
            return $ean->getId()->toBinary();
        }, $eans);
        $eansIds = implode(',', $eansIds);
        $shelvesIds =  array_map(function($shelf) {
            return $shelf->getId()->toBinary();
        }, $shelves);
        $shelvesIds = implode(',', $shelvesIds);
        $builder = $this->createQueryBuilder('e');
        $results = $builder->select('e')
            ->andWhere($builder->expr()->in('e.ean', ':eansIds'))
            ->andWhere($builder->expr()->notIn('e.shelf', ':shelvesIds'))
            ->setParameter('eansIds', $eansIds, ParameterType::BINARY)
            ->setParameter('shelvesIds', $shelvesIds, ParameterType::BINARY)
            ->getQuery()
            ->getResult();

        $esqs = [];
        if (!empty($results)) {
            foreach ($results as $esq) {
                if (!in_array($esq, $esqs, TRUE)) {
                    $esqs[] = $esq;

                }
            }
        }

        return $esqs;
    }

    //    /**
    //     * @return EanShelfQuantity[] Returns an array of EanShelfQuantity objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('e')
    //            ->andWhere('e.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('e.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?EanShelfQuantity
    //    {
    //        return $this->createQueryBuilder('e')
    //            ->andWhere('e.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
