<?php

namespace App\Core\Repository;

use App\Core\Entity\Integration;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Integration>
 *
 * @method Integration|null find($id, $lockMode = null, $lockVersion = null)
 * @method Integration|null findOneBy(array $criteria, array $orderBy = null)
 * @method Integration[]    findAll()
 * @method Integration[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class IntegrationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Integration::class);
    }

    public function findBaselinkerWithAllegroSettings(): array
    {
        $subQb = $this->createQueryBuilder('i')
            ->select('i.id')
            ->leftJoin('i.integrationSettings', 's')
            ->where('i.type = :integrationType')
            ->andWhere('s.name = :settingType')
            ->andWhere('s.value = :valueType')
            ->setParameter('integrationType', 'baselinker')
            ->setParameter('settingType', 'TYPE')
            ->setParameter('valueType', 'allegro');

        $integrationIds = array_column($subQb->getQuery()->getArrayResult(), 'id');

        if (empty($integrationIds)) {
            return [];
        }

        $qb = $this->createQueryBuilder('i')
            ->leftJoin('i.integrationSettings', 's')
            ->leftJoin('i.owner', 'u')
            ->addSelect('s')
            ->addSelect('u')
            ->where('i.id IN (:integrationIds)')
            ->setParameter('integrationIds', $integrationIds);

        return $qb->getQuery()->getResult();
    }

    public function getTypes():array {
        return $this->createQueryBuilder('i')
            ->select('i.type')
            ->groupBy('i.type')
            ->getQuery()
            ->getResult()
            ;
    }

    public function getIntegrationTypeById(int $id): ?string {
        $integration = $this->find($id);
        if (null === $integration) {
            return null;
        }
        if ('prestashop' === $integration->getType()){
            $type = $integration->getType();
        } else {
            $type = $integration->getSettingsByKey('TYPE');
        }
        return $type;
    }


    //    /**
    //     * @return Integration[] Returns an array of Integration objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('i')
    //            ->andWhere('i.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('i.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Integration
    //    {
    //        return $this->createQueryBuilder('i')
    //            ->andWhere('i.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
