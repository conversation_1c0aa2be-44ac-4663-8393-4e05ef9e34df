<?php

namespace App\Core\Repository;

use App\Core\Entity\DocumentShelfStocktaking;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<DocumentShelfStocktaking>
 *
 * @method DocumentShelfStocktaking|null find($id, $lockMode = null, $lockVersion = null)
 * @method DocumentShelfStocktaking|null findOneBy(array $criteria, array $orderBy = null)
 * @method DocumentShelfStocktaking[]    findAll()
 * @method DocumentShelfStocktaking[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DocumentShelfStocktakingRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DocumentShelfStocktaking::class);
    }

    //    /**
    //     * @return DocumentShelfStocktaking[] Returns an array of DocumentShelfStocktaking objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('d')
    //            ->andWhere('d.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('d.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?DocumentShelfStocktaking
    //    {
    //        return $this->createQueryBuilder('d')
    //            ->andWhere('d.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
