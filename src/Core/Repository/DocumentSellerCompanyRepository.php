<?php

namespace App\Core\Repository;

use App\Core\Entity\DocumentSellerCompany;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<DocumentSellerCompany>
 *
 * @method DocumentSellerCompany|null find($id, $lockMode = null, $lockVersion = null)
 * @method DocumentSellerCompany|null findOneBy(array $criteria, array $orderBy = null)
 * @method DocumentSellerCompany[]    findAll()
 * @method DocumentSellerCompany[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DocumentSellerCompanyRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DocumentSellerCompany::class);
    }

    //    /**
    //     * @return DocumentSellerCompany[] Returns an array of DocumentSellerCompany objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('d')
    //            ->andWhere('d.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('d.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?DocumentSellerCompany
    //    {
    //        return $this->createQueryBuilder('d')
    //            ->andWhere('d.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
