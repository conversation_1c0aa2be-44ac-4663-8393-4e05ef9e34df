<?php

namespace App\Core\Repository;

use App\Core\Entity\DocumentItem;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<DocumentItem>
 *
 * @method DocumentItem|null find($id, $lockMode = null, $lockVersion = null)
 * @method DocumentItem|null findOneBy(array $criteria, array $orderBy = null)
 * @method DocumentItem[]    findAll()
 * @method DocumentItem[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DocumentItemRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DocumentItem::class);
    }

    //    /**
    //     * @return DocumentItem[] Returns an array of DocumentItem objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('d')
    //            ->andWhere('d.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('d.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?DocumentItem
    //    {
    //        return $this->createQueryBuilder('d')
    //            ->andWhere('d.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
