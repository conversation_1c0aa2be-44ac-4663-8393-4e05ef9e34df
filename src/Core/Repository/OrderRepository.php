<?php

namespace App\Core\Repository;

use App\Core\Entity\Integration;
use App\Core\Entity\Order;
use App\Core\Taxonomy\DateTaxonomy;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

/**
 * @extends ServiceEntityRepository<Order>
 *
 * @method Order|null find($id, $lockMode = null, $lockVersion = null)
 * @method Order|null findOneBy(array $criteria, array $orderBy = null)
 * @method Order[]    findAll()
 * @method Order[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
final class OrderRepository extends ServiceEntityRepository {
    public function __construct(ManagerRegistry $registry) {
        parent::__construct($registry, Order::class);
    }

    public function getLastConfirmed() {
        return $this->createQueryBuilder('o')
            ->select('MAX(o.date_confirmed)')
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function getLastConfirmedForIntegration(array $integrationIds) {
        return $this->createQueryBuilder('o')
            ->select('MAX(o.date_confirmed)')
            ->where('o.order_source_id IN (:integrationIds)')
            ->setParameter('integrationIds', $integrationIds)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function checkIfOrderExists($orderId) {
        return $this->createQueryBuilder('o')
            ->andWhere('o.order_id = :val')
            ->setParameter('val', $orderId)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function findByData(array $data) {
        $queryBuilder = $this->createQueryBuilder('ord')
            ->leftJoin('ord.products', 'ordprod');
        if ($data['ean']) {
            $queryBuilder->andWhere('ordprod.ean = :eanVal')
                ->setParameter('eanVal', $data['ean']);
        }
        if ($data['sku']) {
            $queryBuilder->andWhere('ordprod.sku = :skuVal')
                ->setParameter('skuVal', $data['sku']);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    public function getUuidFields(): array {
        $uuidFields = [];
        $metadata = $this->getClassMetadata();

        foreach ($metadata->fieldMappings as $field => $mapping) {
            if ($mapping['type'] === UuidType::NAME) {
                $uuidFields[$field] = false; // false indicates direct field
            }
        }

        foreach ($metadata->associationMappings as $field => $mapping) {
            if (isset($mapping['targetEntity'])) {
                $relatedMetadata = $this->getEntityManager()->getClassMetadata($mapping['targetEntity']);
                foreach ($relatedMetadata->fieldMappings as $relatedField => $relatedMapping) {
                    if ($relatedMapping['type'] === UuidType::NAME) {
                        $uuidFields[$field] = true; // true indicates related field
                        break; // Stop after finding the first UUID field in the related entity
                    }
                }
            }
        }

        return $uuidFields;
    }

    public function getRelatedFields(): array {
        $relatedFields = [];
        $metadata = $this->getClassMetadata();

        foreach ($metadata->associationMappings as $field => $mapping) {
            if (isset($mapping['targetEntity'])) {
                $relatedMetadata = $this->getEntityManager()->getClassMetadata($mapping['targetEntity']);
                foreach ($relatedMetadata->fieldMappings as $relatedField => $relatedMapping) {
                    $relatedFieldKey = $field . '_' . $relatedField;
                    $relatedFields[$relatedFieldKey] = [$field, $relatedField];
                }
            }
        }

        return $relatedFields;
    }

    public function createFilteredQueryBuilder(array $filters, bool $orWhere): QueryBuilder {
        $uuidFields = $this->getUuidFields();
        $relatedFields = $this->getRelatedFields();
        $conditionMethod = $orWhere ? 'orWhere' : 'andWhere';
        $dateFrom = NULL;
        $dateTo = NULL;

        $queryBuilder = $this->createQueryBuilder('o');
        $aliases = [];
        foreach ($filters as $key => $value) {
            if ($key !== 'dateFrom' && $key !== 'dateTo') {
                if (isset($relatedFields[$key])) {
                    [$relatedEntity, $relatedField] = $relatedFields[$key];

                    if (!isset($aliases[$relatedEntity])) {
                        $relatedAlias = $relatedEntity . count($aliases);
                        $queryBuilder->leftJoin("o.$relatedEntity", $relatedAlias);
                        $aliases[$relatedEntity] = $relatedAlias;
                    } else {
                        $relatedAlias = $aliases[$relatedEntity];
                    }

                    $conditionField = "$relatedAlias.$relatedField";
                } elseif (isset($uuidFields[$key])) {
                    if ($uuidFields[$key]) {
                        if (!isset($aliases[$key])) {
                            $relatedAlias = $key . 'Alias';
                            $queryBuilder->leftJoin("o.$key", $relatedAlias);
                            $aliases[$key] = $relatedAlias;
                        } else {
                            $relatedAlias = $aliases[$key];
                        }
                        $conditionField = "$relatedAlias.id";
                    } else {
                        $conditionField = "o.$key";
                    }

                    if (Uuid::isValid($value)) {
                        $uuid = Uuid::fromString($value);
                        $queryBuilder->$conditionMethod("$conditionField = :$key")
                            ->setParameter($key, $uuid, UuidType::NAME);
                    }
                    continue;
                } else {
                    $conditionField = "o.$key";
                }

                if (str_contains($value, '%')) {
                    $queryBuilder->$conditionMethod("$conditionField LIKE :$key")
                        ->setParameter($key, $value);
                } else {
                    $queryBuilder->$conditionMethod("$conditionField = :$key")
                        ->setParameter($key, $value);
                }
            } else {
                $timeZone = new \DateTimeZone('Europe/Warsaw');
                if ($key === 'dateFrom') {
                        $dateFrom = \DateTime::createFromFormat('d-m-Y', $value, $timeZone);
                        $dateFrom->setTime(0, 0, 0);
                } elseif ($key === 'dateTo') {
                    $dateTo = \DateTime::createFromFormat('d-m-Y', $value, $timeZone);
                    $dateTo->setTime(23, 59, 59);
                }
            }
        }

            if ($dateFrom !== NULL && $dateTo !== NULL) {
                $queryBuilder->andWhere('o.date_add BETWEEN :dateFrom AND :dateTo')
                    ->setParameter('dateFrom', $dateFrom->format(DateTaxonomy::DATE_FORMAT))
                    ->setParameter('dateTo', $dateTo->format(DateTaxonomy::DATE_FORMAT));
            } elseif ($dateFrom !== NULL) {
                $queryBuilder->andWhere('o.date_add >= :dateFrom')
                    ->setParameter('dateFrom', $dateFrom->format(DateTaxonomy::DATE_FORMAT));
            } elseif ($dateTo !== NULL) {
                $queryBuilder->andWhere('o.date_add <= :dateTo')
                    ->setParameter('dateTo', $dateTo->format(DateTaxonomy::DATE_FORMAT));
            }

            return $queryBuilder;

        }


        public
        function getTotalOrdersCount(QueryBuilder $queryBuilder): int {
            $queryBuilder->select('COUNT(o.id)');
            return (int)$queryBuilder->getQuery()->getSingleScalarResult();
        }

        public
        function getFieldsForSearch($entity): array {
            $fields = [];
            $relatedFields = [];
            $metadata = $this->getEntityManager()->getClassMetadata($entity);

            foreach ($metadata->fieldMappings as $field => $mapping) {
                $fields[] = $field;
            }

            foreach ($metadata->associationMappings as $field => $mapping) {
                if (isset($mapping['targetEntity'])) {
                    $relatedMetadata = $this->getEntityManager()->getClassMetadata($mapping['targetEntity']);
                    foreach ($relatedMetadata->fieldMappings as $relatedField => $relatedMapping) {
                        $relatedFields[] = $field . '_' . $relatedField;
                    }
                }
            }

            return array_merge($fields, $relatedFields);
        }

        public
        function getDistinctOneByField($fieldName) {
            return $this->createQueryBuilder('o')
                ->select("DISTINCT o.$fieldName")
                ->getQuery()
                ->getArrayResult();
        }

        public
        function findOrdersWithInvoicesAndDeliveryData($statusToSend, $invoice = NULL, $limit = NULL) {
            $statusId = $statusToSend->getName();

            $q = $this->createQueryBuilder('o')
                ->join('o.internal_status_id', 's')
                ->join('o.orderInvoice', 'oi')
                ->where('s.name = :statusId')
                ->setParameter('statusId', $statusId);

            if ($invoice) {
                $q->andWhere('oi.invoice_number LIKE :invoiceNumber')
                    ->setParameter('invoiceNumber', $invoice);
            }

            if ($limit) {
                $q->setMaxResults($limit);
            }


            $orders = $q->getQuery()->getResult();

            return array_filter($orders, function ($order) {
                $orderDelivery = $order->getOrderDelivery();
                if (!$orderDelivery) {
                    return false;
                }

                foreach ($orderDelivery->getOrderDeliveryShipmentData() as $shipmentData) {
                    if ($shipmentData->isLabel()) {
                        return true;
                    }
                }

                return false;
            });
        }

        public
        function findOrdersByStatus($statusToSend) {
            $statusId = $statusToSend->getName();

            return $this->createQueryBuilder('o')
                ->join('o.internal_status_id', 's')
                ->join('o.orderInvoice', 'oi')
                ->where('s.name = :statusId')
                ->andWhere('oi.invoice_number LIKE :invoiceNumber')
                ->setParameter('statusId', $statusId)
                ->setParameter('invoiceNumber', 'ZK%')
                ->getQuery()
                ->getResult();
        }

        public
        function findOrdersByStatusAndInvoice($statusToSend, $invoice = NULL) {
            $statusId = $statusToSend->getName();

            $q = $this->createQueryBuilder('o')
                ->join('o.internal_status_id', 's')
                ->join('o.orderInvoice', 'oi')
                ->where('s.name = :statusId')
                ->setParameter('statusId', $statusId);

            if ($invoice) {
                $q->andWhere('oi.invoice_number LIKE :invoiceNumber')
                    ->setParameter('invoiceNumber', $invoice);
            }

            return $q
                ->getQuery()
                ->getResult();
        }

        public
        function findOrdersBetweenDate($dateFrom, $dateTo) {
            $q = $this->createQueryBuilder('o')
                ->select('o.id', 'o.date_add', 'od.orderDeliveryShipmentData')
                ->leftJoin('o.orderDelivery', 'od')
                ->where('o.date_add BETWEEN :dateFrom AND :dateTo')
                ->setParameter('dateFrom', $dateFrom)
                ->setParameter('dateTo', $dateTo);

            return $q
                ->getQuery()
                ->getArrayResult();
        }

        public function countOrdersByShipmentTime(string $startDateString, string $endDateString): array {
            $conn = $this->getEntityManager()->getConnection();
            $sql = 'SELECT o.id, o.date_add, od.id as odid, odsd.shipment_date  FROM `order` o left join order_delivery od on o.id = od.order_id left join order_delivery_shipment_data odsd on od.id = odsd.order_delivery_id_id
                                                            where o.date_add BETWEEN :startDateString AND :endDateString and odsd.shipment_date is not null GROUP BY o.id order by o.date_add ASC';

            return $conn->executeQuery($sql, ['startDateString' => strtotime($startDateString), 'endDateString' => strtotime($endDateString)])->fetchAllAssociative();
        }

        public function findAllSinceDate($date): array {
            $q = $this->createQueryBuilder('o')
                ->select('o.shop_order_id')
                ->where('o.date_add > :dateFrom')
                ->setParameter('dateFrom', $date);


            return $q
                ->getQuery()
                ->getSingleColumnResult();
        }

        public function orderSummary() {
            $dateFrom = (new \DateTime('now', new \DateTimeZone('Europe/Warsaw')))->format('Y-m-d 00:00:00');
            $dateTo = (new \DateTime('now', new \DateTimeZone('Europe/Warsaw')))->format('Y-m-d 23:59:59');
            $q = $this->createQueryBuilder('o')
                ->where('o.date_add BETWEEN :dateFrom AND :dateTo')
                ->setParameter('dateFrom', $dateFrom)
                ->setParameter('dateTo', $dateTo);
            $today = $q
                ->getQuery()
                ->getArrayResult();

            $q2 = $this->createQueryBuilder('o')
                ->leftJoin('o.orderDelivery', 'od')
                ->where('o.payment_done = (o.full_price + od.delivery_price)')
                ->where('o.date_add BETWEEN :dateFrom AND :dateTo')
                ->setParameter('dateFrom', $dateFrom)
                ->setParameter('dateTo', $dateTo);

            $todayPaid = $q2
                ->getQuery()
                ->getArrayResult();

            return ['todayFetched' => $today, 'todayPaid' => $todayPaid];
        }

        public function getOrderByStatusAndDate(array $filters) {
            if (!isset($filters['status']) ||  !isset($filters['date'])) {
                return null;
            }
            $criteria = Criteria::create()
                ->where(Criteria::expr()->lte('status_date', new \DateTimeImmutable($filters['date'])))
                ->andWhere(Criteria::expr()->eq('internal_status_id', Uuid::fromString($filters['status']->getId())));

            return $this->matching($criteria)->toArray();

        }

        public function getUnpaidByDate(\DateTimeInterface $dateTime, Integration $integration) {
            $criteria = Criteria::create()
                ->where(Criteria::expr()->gte('date_add', $dateTime))
                ->andWhere(Criteria::expr()->eq('paid', false))
                ->andWhere(Criteria::expr()->eq('cancelled', false))
                ->andWhere(Criteria::expr()->eq('order_source_id', $integration->getId()));

            return $this->matching($criteria)->toArray();
        }


//    /**
//     * @return Order[] Returns an array of Order objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('o')
//            ->andWhere('o.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('o.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?Order
//    {
//        return $this->createQueryBuilder('o')
//            ->andWhere('o.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
    }
