<?php

namespace App\Core\Repository;

use App\Core\Entity\BasketOrder;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<BasketOrder>
 *
 * @method BasketOrder|null find($id, $lockMode = null, $lockVersion = null)
 * @method BasketOrder|null findOneBy(array $criteria, array $orderBy = null)
 * @method BasketOrder[]    findAll()
 * @method BasketOrder[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class BasketOrderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, BasketOrder::class);
    }

    //    /**
    //     * @return BasketOrder[] Returns an array of BasketOrder objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('b')
    //            ->andWhere('b.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('b.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?BasketOrder
    //    {
    //        return $this->createQueryBuilder('b')
    //            ->andWhere('b.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
