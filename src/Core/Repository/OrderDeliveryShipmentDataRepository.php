<?php

namespace App\Core\Repository;

use App\Core\Entity\OrderDeliveryShipmentData;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<OrderDeliveryShipmentData>
 *
 * @method OrderDeliveryShipmentData|null find($id, $lockMode = null, $lockVersion = null)
 * @method OrderDeliveryShipmentData|null findOneBy(array $criteria, array $orderBy = null)
 * @method OrderDeliveryShipmentData[]    findAll()
 * @method OrderDeliveryShipmentData[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OrderDeliveryShipmentDataRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OrderDeliveryShipmentData::class);
    }

//    /**
//     * @return OrderDeliveryShipmentData[] Returns an array of OrderDeliveryShipmentData objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('o')
//            ->andWhere('o.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('o.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?OrderDeliveryShipmentData
//    {
//        return $this->createQueryBuilder('o')
//            ->andWhere('o.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
