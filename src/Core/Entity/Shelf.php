<?php

namespace App\Core\Entity;

use App\Core\Repository\ShelfRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: ShelfRepository::class)]
class Shelf
{
    #[Groups(['scan'])]
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;


    #[ORM\ManyToOne(fetch: 'EAGER', inversedBy: 'shelves')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Rack $rack = null;

    #[Groups(['scan'])]
    #[ORM\Column(length: 255)]
    private ?string $shelf_no = null;

    #[ORM\Column(type: 'string', length: 255)]
    private ?string $type = null;

    #[ORM\Column(length: 13)]
    private ?string $ean13 = null;

    /**
     * @var Collection<int, EanShelfQuantity>
     */
    #[ORM\OneToMany(targetEntity: EanShelfQuantity::class, mappedBy: 'shelf')]
    private Collection $eanShelfQuantities;

    public function __construct()
    {
        $this->eanShelfQuantities = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getRack(): ?Rack
    {
        return $this->rack;
    }

    public function setRack(?Rack $rack): static
    {
        $this->rack = $rack;

        return $this;
    }

    public function getShelfNo(): ?string
    {
        return $this->shelf_no;
    }

    public function setShelfNo(string $shelf_no): static
    {
        $this->shelf_no = $shelf_no;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getEan13(): ?string
    {
        return $this->ean13;
    }

    public function setEan13(string $ean13): static
    {
        $this->ean13 = $ean13;

        return $this;
    }

    /**
     * @return Collection<int, EanShelfQuantity>
     */
    public function getEanShelfQuantities(): Collection
    {
        return $this->eanShelfQuantities;
    }

    public function addEanShelfQuantity(EanShelfQuantity $eanShelfQuantity): static
    {
        if (!$this->eanShelfQuantities->contains($eanShelfQuantity)) {
            $this->eanShelfQuantities->add($eanShelfQuantity);
            $eanShelfQuantity->setShelf($this);
        }

        return $this;
    }

    public function removeEanShelfQuantity(EanShelfQuantity $eanShelfQuantity): static
    {
        if ($this->eanShelfQuantities->removeElement($eanShelfQuantity)) {
            // set the owning side to null (unless already changed)
            if ($eanShelfQuantity->getShelf() === $this) {
                $eanShelfQuantity->setShelf(null);
            }
        }

        return $this;
    }
}
