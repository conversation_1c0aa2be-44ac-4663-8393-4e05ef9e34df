<?php

namespace App\Core\Entity;

use App\Core\Repository\IntegrationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: IntegrationRepository::class)]
class Integration
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 255)]
    private ?string $type = null;

    #[ORM\ManyToOne(fetch: 'EAGER')]
    private ?User $owner = null;

    /**
     * @var Collection<int, IntegrationSettings>
     */
    #[ORM\OneToMany(targetEntity: IntegrationSettings::class, mappedBy: 'integration', cascade: ['persist'], fetch: 'EAGER', orphanRemoval: true)]
    private Collection $integrationSettings;

    #[ORM\OneToOne(mappedBy: 'integration', cascade: ['persist', 'remove'])]
    private ?IntegrationData $integrationData = null;


    public function __construct()
    {
        $this->integrationSettings = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getOwner(): ?User
    {
        return $this->owner;
    }

    public function setOwner(User $owner): static
    {
        $this->owner = $owner;

        return $this;
    }

    /**
     * @return Collection<int, IntegrationSettings>
     */
    public function getIntegrationSettings(): Collection
    {
        return $this->integrationSettings;
    }

    public function getSettingsByKey(string $key): ?string {
        $result = $this->integrationSettings->findFirst(function($k, $v) use ($key) {
            return $key === $v->getName();
        });
        if ($result instanceof IntegrationSettings) {
            return $result->getValue();
        }

        return null;
    }

    public function addIntegrationSetting(IntegrationSettings $integrationSetting): static
    {
        if (!$this->integrationSettings->contains($integrationSetting)) {
            $this->integrationSettings->add($integrationSetting);
            $integrationSetting->setIntegration($this);
        }

        return $this;
    }

    public function removeIntegrationSetting(IntegrationSettings $integrationSetting): static
    {
        if ($this->integrationSettings->removeElement($integrationSetting)) {
            // set the owning side to null (unless already changed)
            if ($integrationSetting->getIntegration() === $this) {
                $integrationSetting->setIntegration(null);
            }
        }

        return $this;
    }

    public function addOrUpdateSetting(string $name, ?string $value): static
    {
        $existingSetting = $this->integrationSettings->filter(function(IntegrationSettings $setting) use ($name) {
            return $setting->getName() === $name;
        })->first();

        if ($existingSetting) {
            $existingSetting->setValue($value);
        } else {
            $newSetting = new IntegrationSettings();
            $newSetting->setName($name);
            $newSetting->setValue($value);
            $this->addIntegrationSetting($newSetting);
        }

        return $this;
    }

    public function getIntegrationData(): ?IntegrationData
    {
        return $this->integrationData;
    }

    public function setIntegrationData(IntegrationData $integrationData): static
    {
        // set the owning side of the relation if necessary
        if ($integrationData->getIntegration() !== $this) {
            $integrationData->setIntegration($this);
        }

        $this->integrationData = $integrationData;

        return $this;
    }

    public function getPaidStatusList(): array {
        $statusList = [];
        foreach ($this->integrationData?->getIntegrationStatusMappings() ?? [] as $status) {
            if ($status->isPaid()) {
                $statusList[] = $status;
            }
        }

        return $statusList;
    }

    public function getShippedStatusList(): array {
        $statusList = [];
        foreach ($this->integrationData?->getIntegrationStatusMappings() ?? [] as $status) {
            if ($status->isShipped()) {
                $statusList[] = $status;
            }
        }

        return $statusList;
    }

    public function getDeliveredStatusList(): array {
        $statusList = [];
        foreach ($this->integrationData?->getIntegrationStatusMappings() ?? [] as $status) {
            if ($status->isDelivered()) {
                $statusList[] = $status;
            }
        }

        return $statusList;
    }
}
