<?php

namespace App\Core\Entity;

use App\Core\Repository\BasketOrderRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Serializer\Attribute\Ignore;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: BasketOrderRepository::class)]
class BasketOrder
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\OneToOne(fetch: 'EAGER')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Order $order = null;

    #[ORM\ManyToOne(targetEntity: Basket::class, fetch: 'EAGER')]
    private ?Basket $basket_id = null;

    #[ORM\ManyToOne(targetEntity: User::class, fetch: 'EAGER')]
    #[ORM\JoinColumn(nullable: false)]
    //@TODO Some day find a way to not ignore it and prevent error with lazy load proxy classes while serialize this property
    #[Ignore]
    private ?User $user = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    private ?\DateTimeInterface $date_created = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    private ?\DateTimeInterface $date_updated = null;

    /**
     * @var Collection<int, BasketOrderProduct>
     */
    #[ORM\OneToMany(targetEntity: BasketOrderProduct::class, mappedBy: 'basket_order', orphanRemoval: true)]
    private Collection $basketOrderProducts;

    #[ORM\Column]
    private ?int $status_id = null;

    public function __construct()
    {
        $this->basketOrderProducts = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getOrder(): ?Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): static
    {
        $this->order = $order;

        return $this;
    }

    public function getBasketId(): ?Basket
    {
        return $this->basket_id;
    }

    public function setBasketId(?Basket $basket_id): static
    {
        $this->basket_id = $basket_id;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function getDateCreated(): ?\DateTimeInterface
    {
        return $this->date_created;
    }

    public function setDateCreated(\DateTimeInterface $date_created): static
    {
        $this->date_created = $date_created;

        return $this;
    }

    public function getDateUpdated(): ?\DateTimeInterface
    {
        return $this->date_updated;
    }

    public function setDateUpdated(\DateTimeInterface $date_updated): static
    {
        $this->date_updated = $date_updated;

        return $this;
    }

    /**
     * @return Collection<int, BasketOrderProduct>
     */
    public function getBasketOrderProducts(): Collection
    {
        return $this->basketOrderProducts;
    }

    public function addBasketOrderProduct(BasketOrderProduct $basketOrderProduct): static
    {
        if (!$this->basketOrderProducts->contains($basketOrderProduct)) {
            $this->basketOrderProducts->add($basketOrderProduct);
            $basketOrderProduct->setBasketOrder($this);
        }

        return $this;
    }

    public function removeBasketOrderProduct(BasketOrderProduct $basketOrderProduct): static
    {
        if ($this->basketOrderProducts->removeElement($basketOrderProduct)) {
            // set the owning side to null (unless already changed)
            if ($basketOrderProduct->getBasketOrder() === $this) {
                $basketOrderProduct->setBasketOrder(null);
            }
        }

        return $this;
    }

    public function getStatusId(): ?int
    {
        return $this->status_id;
    }

    public function setStatusId(int $status_id): static
    {
        $this->status_id = $status_id;

        return $this;
    }
}
