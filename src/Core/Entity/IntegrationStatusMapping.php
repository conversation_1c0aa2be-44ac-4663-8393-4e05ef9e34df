<?php

namespace App\Core\Entity;

use App\Core\Repository\IntegrationStatusMappingRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: IntegrationStatusMappingRepository::class)]
class IntegrationStatusMapping
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\ManyToOne(inversedBy: 'integrationStatusMappings')]
    #[ORM\JoinColumn(nullable: false)]
    private ?IntegrationData $integrationData = null;

    #[ORM\ManyToOne]
    private ?OrderStatus $orderStatus = null;

    #[ORM\Column(length: 50, nullable: false)]
    private ?string $integrationStatusId = null;

    #[ORM\Column(length: 255, nullable: false)]
    private ?string $integrationStatusName = null;

    #[ORM\Column]
    private ?bool $paid = null;

    #[ORM\Column]
    private ?bool $shipped = null;

    #[ORM\Column]
    private ?bool $delivered = null;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getIntegrationData(): ?IntegrationData
    {
        return $this->integrationData;
    }

    public function setIntegrationData(?IntegrationData $integrationData): static
    {
        $this->integrationData = $integrationData;

        return $this;
    }

    public function getOrderStatus(): ?OrderStatus
    {
        return $this->orderStatus;
    }

    public function setOrderStatus(?OrderStatus $orderStatus): static
    {
        $this->orderStatus = $orderStatus;

        return $this;
    }

    public function getIntegrationStatusId(): ?string
    {
        return $this->integrationStatusId;
    }

    public function setIntegrationStatusId(?string $integrationStatusId): static
    {
        $this->integrationStatusId = $integrationStatusId;

        return $this;
    }

    public function getIntegrationStatusName(): ?string
    {
        return $this->integrationStatusName;
    }

    public function setIntegrationStatusName(?string $integrationStatusName): static
    {
        $this->integrationStatusName = $integrationStatusName;

        return $this;
    }

    public function isPaid(): ?bool
    {
        return $this->paid;
    }

    public function setPaid(bool $paid): static
    {
        $this->paid = $paid;

        return $this;
    }

    public function isShipped(): ?bool {
        return $this->shipped;
    }

    public function setShipped(bool $shipped): static {
        $this->shipped = $shipped;
        return $this;
    }

    public function isDelivered(): ?bool {
        return $this->delivered;
    }

    public function setDelivered(bool $delivered): static {
        $this->delivered = $delivered;
        return $this;
    }
}
