<?php

namespace App\Core\Entity;

use App\Core\Repository\OrderStatusTabRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: OrderStatusTabRepository::class)]
class OrderStatusTab
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    /**
     * @var Collection<int, OrderStatus>
     */
    #[ORM\OneToMany(targetEntity: OrderStatus::class, mappedBy: 'tab')]
    private Collection $orderStatuses;

    public function __construct()
    {
        $this->orderStatuses = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection<int, OrderStatus>
     */
    public function getOrderStatuses(): Collection
    {
        return $this->orderStatuses;
    }

    public function addOrderStatus(OrderStatus $orderStatus): static
    {
        if (!$this->orderStatuses->contains($orderStatus)) {
            $this->orderStatuses->add($orderStatus);
            $orderStatus->setTab($this);
        }

        return $this;
    }

    public function removeOrderStatus(OrderStatus $orderStatus): static
    {
        if ($this->orderStatuses->removeElement($orderStatus)) {
            // set the owning side to null (unless already changed)
            if ($orderStatus->getTab() === $this) {
                $orderStatus->setTab(null);
            }
        }

        return $this;
    }
}
