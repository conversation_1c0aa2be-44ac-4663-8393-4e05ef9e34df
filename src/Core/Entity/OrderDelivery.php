<?php

namespace App\Core\Entity;

use App\Core\Repository\OrderDeliveryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: OrderDeliveryRepository::class)]
class OrderDelivery
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 100)]
    private ?string $delivery_method = null;

    #[ORM\Column]
    private ?int $delivery_price = null;

    #[ORM\Column(length: 20)]
    private ?string $delivery_package_module = null;

    #[ORM\Column(length: 40)]
    private ?string $delivery_package_nr = null;

    #[ORM\Column(length: 100)]
    private ?string $delivery_fullname = null;

    #[ORM\Column(length: 100)]
    private ?string $delivery_company = null;

    #[ORM\Column(length: 100)]
    private ?string $delivery_address = null;

    #[ORM\Column(length: 100)]
    private ?string $delivery_postcode = null;

    #[ORM\Column(length: 100)]
    private ?string $delivery_city = null;

    #[ORM\Column(length: 20)]
    private ?string $delivery_state = null;

    #[ORM\Column(length: 50)]
    private ?string $delivery_country = null;

    #[ORM\Column(length: 2)]
    private ?string $delivery_country_code = null;

    #[ORM\Column(length: 40)]
    private ?string $delivery_point_id = null;

    #[ORM\Column(length: 100)]
    private ?string $delivery_point_name = null;

    #[ORM\Column(length: 100)]
    private ?string $delivery_point_address = null;

    #[ORM\Column(length: 100)]
    private ?string $delivery_point_postcode = null;

    #[ORM\Column(length: 100)]
    private ?string $delivery_point_city = null;

    #[ORM\OneToOne(inversedBy: 'orderDelivery', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?Order $order = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $delivery_id = null;

    /**
     * @var Collection<int, OrderDeliveryShipmentData>
     */
    #[ORM\OneToMany(targetEntity: OrderDeliveryShipmentData::class, mappedBy: 'order_delivery_id', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $orderDeliveryShipmentData;

    #[ORM\Column(nullable: true)]
    private ?float $manual_cod_price = null;

    public function __construct()
    {
        $this->orderDeliveryShipmentData = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getDeliveryMethod(): ?string
    {
        return $this->delivery_method;
    }

    public function setDeliveryMethod(string $delivery_method): static
    {
        $this->delivery_method = $delivery_method;

        return $this;
    }

    public function getDeliveryPrice(): ?int
    {
        return $this->delivery_price;
    }

    public function setDeliveryPrice(int $delivery_price): static
    {
        $this->delivery_price = $delivery_price;

        return $this;
    }

    public function getDeliveryPackageModule(): ?string
    {
        return $this->delivery_package_module;
    }

    public function setDeliveryPackageModule(string $delivery_package_module): static
    {
        $this->delivery_package_module = $delivery_package_module;

        return $this;
    }

    public function getDeliveryPackageNr(): ?string
    {
        return $this->delivery_package_nr;
    }

    public function setDeliveryPackageNr(string $delivery_package_nr): static
    {
        $this->delivery_package_nr = $delivery_package_nr;

        return $this;
    }

    public function getDeliveryFullname(): ?string
    {
        return $this->delivery_fullname;
    }

    public function getDeliveryFirstname(): ?string
    {
        if ($this->delivery_fullname === null) {
            return null;
        }

        $parts = explode(' ', $this->delivery_fullname);
        return $parts[0] ?? null;
    }

    public function getDeliveryLastname(): ?string
    {
        if ($this->delivery_fullname === null) {
            return null;
        }

        $parts = explode(' ', $this->delivery_fullname);
        return $parts[1] ?? null;
    }

    public function setDeliveryFullname(string $delivery_fullname): static
    {
        $this->delivery_fullname = $delivery_fullname;

        return $this;
    }

    public function getDeliveryCompany(): ?string
    {
        return $this->delivery_company;
    }

    public function setDeliveryCompany(string $delivery_company): static
    {
        $this->delivery_company = $delivery_company;

        return $this;
    }

    public function getDeliveryAddress(): ?string
    {
        return $this->delivery_address;
    }

    public function setDeliveryAddress(string $delivery_address): static
    {
        $this->delivery_address = $delivery_address;

        return $this;
    }

    public function getDeliveryPostcode(): ?string
    {
        return $this->delivery_postcode;
    }

    public function setDeliveryPostcode(string $delivery_postcode): static
    {
        $this->delivery_postcode = $delivery_postcode;

        return $this;
    }

    public function getDeliveryCity(): ?string
    {
        return $this->delivery_city;
    }

    public function setDeliveryCity(string $delivery_city): static
    {
        $this->delivery_city = $delivery_city;

        return $this;
    }

    public function getDeliveryState(): ?string
    {
        return $this->delivery_state;
    }

    public function setDeliveryState(string $delivery_state): static
    {
        $this->delivery_state = $delivery_state;

        return $this;
    }

    public function getDeliveryCountry(): ?string
    {
        return $this->delivery_country;
    }

    public function setDeliveryCountry(string $delivery_country): static
    {
        $this->delivery_country = $delivery_country;

        return $this;
    }

    public function getDeliveryCountryCode(): ?string
    {
        return $this->delivery_country_code;
    }

    public function setDeliveryCountryCode(string $delivery_country_code): static
    {
        $this->delivery_country_code = $delivery_country_code;

        return $this;
    }

    public function getDeliveryPointId(): ?string
    {
        return $this->delivery_point_id;
    }

    public function setDeliveryPointId(string $delivery_point_id): static
    {
        $this->delivery_point_id = $delivery_point_id;

        return $this;
    }

    public function getDeliveryPointName(): ?string
    {
        return $this->delivery_point_name;
    }

    public function setDeliveryPointName(string $delivery_point_name): static
    {
        $this->delivery_point_name = $delivery_point_name;

        return $this;
    }

    public function getDeliveryPointAddress(): ?string
    {
        return $this->delivery_point_address;
    }

    public function setDeliveryPointAddress(string $delivery_point_address): static
    {
        $this->delivery_point_address = $delivery_point_address;

        return $this;
    }

    public function getDeliveryPointPostcode(): ?string
    {
        return $this->delivery_point_postcode;
    }

    public function setDeliveryPointPostcode(string $delivery_point_postcode): static
    {
        $this->delivery_point_postcode = $delivery_point_postcode;

        return $this;
    }

    public function getDeliveryPointCity(): ?string
    {
        return $this->delivery_point_city;
    }

    public function setDeliveryPointCity(string $delivery_point_city): static
    {
        $this->delivery_point_city = $delivery_point_city;

        return $this;
    }

    public function getOrder(): ?Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): static
    {
        $this->order = $order;

        return $this;
    }

    public function getDeliveryId(): ?array
    {
        if (!empty($this->delivery_id)) {
            return json_decode($this->delivery_id, TRUE);
        }

        return null;
    }

    public function setDeliveryId(?string $delivery_id): static
    {
        if (empty($deliveryIds = $this->getDeliveryId())) {
            $deliveryIds = [];
        }

        if (in_array($delivery_id, $deliveryIds)) {
            return $this;
        }

        $deliveryIds[] = $delivery_id;
        $this->delivery_id = json_encode($deliveryIds);

        return $this;
    }

    public function getDeliveryAddressNoHouseNumber(): ?string
    {
        $pattern = '/^(.*?)(?=\s+\d+\s*[^\d\s]*\s*\S*$)/';
        if (preg_match($pattern, trim($this->getDeliveryAddress()), $matches)) {
            return trim($matches[0]);
        }
        return null;
    }
    public function getDeliveryAddressOnlyHouseNumber(): ?string
    {
        $pattern = '/\b\d+\s*[^\d\s]*\s*\S*$/';
        if (preg_match($pattern, $this->getDeliveryAddress(), $matches)) {
            return trim($matches[0]);
        }
        return null;
    }

    /**
     * @param string $shipmentId
     * @return OrderDeliveryShipmentData|null
     */
    public function getOrderDeliveryShipmentDataByShipmentId(string $shipmentId): ?array {
        $array = [];
        foreach ($this->orderDeliveryShipmentData as $orderDeliveryShipmentData) {
            if ($orderDeliveryShipmentData->getShipmentId() === $shipmentId) {
                $array[] = $orderDeliveryShipmentData;
            }
        }
        return $array;
    }

    public function getOrderDeliveryShipmentData(): Collection
    {
        return $this->orderDeliveryShipmentData;
    }

    public function addOrderDeliveryShipmentData(OrderDeliveryShipmentData $orderDeliveryShipmentData): static
    {
        if (!$this->orderDeliveryShipmentData->contains($orderDeliveryShipmentData)) {
            $this->orderDeliveryShipmentData->add($orderDeliveryShipmentData);
            $orderDeliveryShipmentData->setOrderDeliveryId($this);
        }

        return $this;
    }

    public function removeOrderDeliveryShipmentData(OrderDeliveryShipmentData $orderDeliveryShipmentData): static
    {
        if ($this->orderDeliveryShipmentData->removeElement($orderDeliveryShipmentData)) {
            // set the owning side to null (unless already changed)
            if ($orderDeliveryShipmentData->getOrderDeliveryId() === $this) {
                $orderDeliveryShipmentData->setOrderDeliveryId(null);
            }
        }

        return $this;
    }

    public function getManualCodPrice(): ?float
    {
        return $this->manual_cod_price;
    }

    public function setManualCodPrice(?float $manual_cod_price): static
    {
        $this->manual_cod_price = $manual_cod_price;

        return $this;
    }

}
