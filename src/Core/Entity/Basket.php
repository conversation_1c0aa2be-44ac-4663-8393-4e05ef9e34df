<?php

namespace App\Core\Entity;

use App\Core\Repository\BasketRepository;
use App\Core\Taxonomy\BasketTaxonomy;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: BasketRepository::class)]
class Basket
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 255)]
    private ?string $ean13 = null;

    #[ORM\Column]
    private ?int $status_id = null;

    #[ORM\ManyToOne(targetEntity: User::class, fetch: 'EAGER')]
    private ?User $user = null;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getEan13(): ?string
    {
        return $this->ean13;
    }

    public function setEan13(string $ean13): static
    {
        $this->ean13 = $ean13;

        return $this;
    }

    public function getStatusId(): ?int
    {
        return $this->status_id;
    }

    public function setStatusId(int $status_id): static
    {
        $this->status_id = $status_id;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function isEmpty(): bool {
        return $this->getStatusId() === BasketTaxonomy::STATUS_EMPTY;
    }

    public function isCompleting(): bool {
        return $this->getStatusId() === BasketTaxonomy::STATUS_COMPLETING;
    }

    public function isPacking(): bool {
        return $this->getStatusId() === BasketTaxonomy::STATUS_PACKING;
    }
}
