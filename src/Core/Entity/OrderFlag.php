<?php

namespace App\Core\Entity;

use App\Core\Repository\OrderFlagRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: OrderFlagRepository::class)]
class OrderFlag
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 20)]
    private ?string $flag_name = null;

    #[ORM\Column(length: 10)]
    private ?string $flag_color = null;

    /**
     * @var Collection<int, Order>
     */
    #[ORM\OneToMany(targetEntity: Order::class, mappedBy: 'markFlag')]
    private Collection $orders;

    public function __construct()
    {
        $this->orders = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getFlagName(): ?string
    {
        return $this->flag_name;
    }

    public function setFlagName(string $flag_name): static
    {
        $this->flag_name = $flag_name;

        return $this;
    }

    public function getFlagColor(): ?string
    {
        return $this->flag_color;
    }

    public function setFlagColor(string $flag_color): static
    {
        $this->flag_color = $flag_color;

        return $this;
    }

    /**
     * @return Collection<int, Order>
     */
    public function getOrders(): Collection
    {
        return $this->orders;
    }

    public function addOrder(Order $order): static
    {
        if (!$this->orders->contains($order)) {
            $this->orders->add($order);
            $order->setMarkFlag($this);
        }

        return $this;
    }

    public function removeOrder(Order $order): static
    {
        if ($this->orders->removeElement($order)) {
            // set the owning side to null (unless already changed)
            if ($order->getMarkFlag() === $this) {
                $order->setMarkFlag(null);
            }
        }

        return $this;
    }

    public function __toString() {
        return $this->flag_name;
    }
}
