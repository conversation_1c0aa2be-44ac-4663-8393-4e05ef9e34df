<?php

namespace App\Core\Entity;

use App\Core\Repository\DocumentShelfStocktakingDataRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: DocumentShelfStocktakingDataRepository::class)]
class DocumentShelfStocktakingData
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Ean $ean = null;

    #[ORM\Column(nullable: true)]
    private ?int $quantity = null;

    #[ORM\Column(nullable: true)]
    private ?int $old_quantity = null;

    #[ORM\ManyToOne(inversedBy: 'stock')]
    #[ORM\JoinColumn(nullable: false)]
    private ?DocumentShelfStocktaking $documentShelfStocktaking = null;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getEan(): ?Ean
    {
        return $this->ean;
    }

    public function setEan(?Ean $ean): static
    {
        $this->ean = $ean;

        return $this;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function setQuantity(?int $quantity): static
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getOldQuantity(): ?int
    {
        return $this->old_quantity;
    }

    public function setOldQuantity(?int $old_quantity): static
    {
        $this->old_quantity = $old_quantity;

        return $this;
    }

    public function getDocumentShelfStocktaking(): ?DocumentShelfStocktaking
    {
        return $this->documentShelfStocktaking;
    }

    public function setDocumentShelfStocktaking(?DocumentShelfStocktaking $documentShelfStocktaking): static
    {
        $this->documentShelfStocktaking = $documentShelfStocktaking;

        return $this;
    }
}
