<?php

namespace App\Core\Entity;

use App\Core\Repository\RackRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: RackRepository::class)]
class Rack
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\ManyToOne(fetch: 'EAGER', inversedBy: 'racks')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Storehouse $storehouse = null;

    #[SerializedName('rackNo')]
    #[ORM\Column(length: 255)]
    private ?string $rack_no = null;

    #[ORM\Column(length: 13)]
    private ?string $ean13 = null;

    /**
     * @var Collection<int, Shelf>
     */
    #[ORM\OneToMany(targetEntity: Shelf::class, mappedBy: 'rack', cascade: ['persist'], fetch: 'EAGER')]
    private Collection $shelves;

    public function __construct()
    {
        $this->shelves = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getStorehouse(): ?Storehouse
    {
        return $this->storehouse;
    }

    public function setStorehouse(?Storehouse $storehouse): static
    {
        $this->storehouse = $storehouse;

        return $this;
    }

    public function getRackNo(): ?string
    {
        return $this->rack_no;
    }

    public function setRackNo(string $rack_no): static
    {
        $this->rack_no = $rack_no;

        return $this;
    }

    public function getEan13(): ?string
    {
        return $this->ean13;
    }

    public function setEan13(string $ean13): static
    {
        $this->ean13 = $ean13;

        return $this;
    }

    /**
     * @return Collection<int, Shelf>
     */
    public function getShelves(): Collection
    {
        return $this->shelves;
    }

    public function addShelf(Shelf $shelf): static
    {
        if (!$this->shelves->contains($shelf)) {
            $this->shelves->add($shelf);
            $shelf->setRack($this);
        }

        return $this;
    }

    public function removeShelf(Shelf $shelf): static
    {
        if ($this->shelves->removeElement($shelf)) {
            // set the owning side to null (unless already changed)
            if ($shelf->getRack() === $this) {
                $shelf->setRack(null);
            }
        }

        return $this;
    }

}
