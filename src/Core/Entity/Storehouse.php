<?php

namespace App\Core\Entity;

use App\Core\Repository\StorehouseRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: StorehouseRepository::class)]
class Storehouse
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255)]
    private ?string $type = null;

    #[ORM\Column(length: 255)]
    #[SerializedName('internal_name')]
    private ?string $internal_name = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[SerializedName('id_external')]
    private ?string $id_external = null;

    /**
     * @var Collection<int, Rack>
     */
    #[ORM\OneToMany(targetEntity: Rack::class, mappedBy: 'storehouse', fetch: 'EAGER')]
    private Collection $racks;

    public function __construct()
    {
        $this->racks = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getInternalName(): ?string
    {
        return $this->internal_name;
    }

    public function setInternalName(string $internal_name): static
    {
        $this->internal_name = $internal_name;

        return $this;
    }

    public function getIdExternal(): ?string
    {
        return $this->id_external;
    }

    public function setIdExternal(?string $id_external): static
    {
        $this->id_external = $id_external;

        return $this;
    }

    /**
     * @return Collection<int, Rack>
     */
    public function getRacks(): Collection
    {
        return $this->racks;
    }

    public function addRack(Rack $rack): static
    {
        if (!$this->racks->contains($rack)) {
            $this->racks->add($rack);
            $rack->setStorehouse($this);
        }

        return $this;
    }

    public function removeShelf(Rack $rack): static
    {
        if ($this->racks->removeElement($rack)) {
            // set the owning side to null (unless already changed)
            if ($rack->getStorehouse() === $this) {
                $rack->setStorehouse(null);
            }
        }

        return $this;
    }
}
