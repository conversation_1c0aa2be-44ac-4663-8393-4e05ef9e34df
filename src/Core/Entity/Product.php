<?php

namespace App\Core\Entity;

use App\Core\Repository\ProductRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: ProductRepository::class)]
class Product
{#[Groups(['scan'])]
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;
    #[Groups(['scan'])]
    #[ORM\Column(length: 255)]
    private ?string $name = null;

    /**
     * @var Collection<int, Ean>
     */
    #[Groups(['scan'])]
    #[ORM\OneToMany(targetEntity: Ean::class, mappedBy: 'product', cascade: ['persist'], fetch: 'EAGER')]
    private Collection $eans;

    #[Groups(['scan'])]
    #[ORM\Column(length: 255)]
    private ?string $sku = null;

    /**
     * @var Collection<int, ProductReservation>
     */
    #[ORM\OneToMany(targetEntity: ProductReservation::class, mappedBy: 'product')]
    private Collection $productReservations;

    public function __construct()
    {
        $this->eans = new ArrayCollection();
        $this->productReservations = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection<int, Ean>
     */
    public function getEans(): Collection
    {
        return $this->eans;
    }

    public function addEan(Ean $ean): static
    {
        if (!$this->eans->contains($ean)) {
            $this->eans->add($ean);
            $ean->setProduct($this);
        }

        return $this;
    }

    public function removeEan(Ean $ean): static
    {
        if ($this->eans->removeElement($ean)) {
            // set the owning side to null (unless already changed)
            if ($ean->getProduct() === $this) {
                $ean->setProduct(null);
            }
        }

        return $this;
    }

    public function getSku(): ?string
    {
        return $this->sku;
    }

    public function setSku(string $sku): static
    {
        $this->sku = $sku;

        return $this;
    }

    /**
     * @return Collection<int, ProductReservation>
     */
    public function getProductReservations(): Collection
    {
        return $this->productReservations;
    }

    public function addProductReservation(ProductReservation $productReservation): static
    {
        if (!$this->productReservations->contains($productReservation)) {
            $this->productReservations->add($productReservation);
            $productReservation->setProduct($this);
        }

        return $this;
    }

    public function removeProductReservation(ProductReservation $productReservation): static
    {
        if ($this->productReservations->removeElement($productReservation)) {
            // set the owning side to null (unless already changed)
            if ($productReservation->getProduct() === $this) {
                $productReservation->setProduct(null);
            }
        }

        return $this;
    }
}
