<?php

namespace App\Core\Entity;

use App\Core\Repository\DocumentStatusRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: DocumentStatusRepository::class)]
class DocumentStatus
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    /**
     * @var Collection<int, Document>
     */
    #[ORM\OneToMany(targetEntity: Document::class, mappedBy: 'status')]
    private Collection $RelatedDocuments;

    public function __construct()
    {
        $this->RelatedDocuments = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection<int, Document>
     */
    public function getRelatedDocuments(): Collection
    {
        return $this->RelatedDocuments;
    }

    public function addRelatedDocument(Document $relatedDocument): static
    {
        if (!$this->RelatedDocuments->contains($relatedDocument)) {
            $this->RelatedDocuments->add($relatedDocument);
            $relatedDocument->setStatus($this);
        }

        return $this;
    }

    public function removeRelatedDocument(Document $relatedDocument): static
    {
        if ($this->RelatedDocuments->removeElement($relatedDocument)) {
            // set the owning side to null (unless already changed)
            if ($relatedDocument->getStatus() === $this) {
                $relatedDocument->setStatus(null);
            }
        }

        return $this;
    }
}
