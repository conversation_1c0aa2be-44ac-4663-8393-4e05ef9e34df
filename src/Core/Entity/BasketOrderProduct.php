<?php

namespace App\Core\Entity;

use App\Core\Repository\BasketOrderProductRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: BasketOrderProductRepository::class)]
class BasketOrderProduct
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    #[Groups('order_product_action')]
    private ?Uuid $id = null;

    #[ORM\ManyToOne(inversedBy: 'basketOrderProducts')]
    #[ORM\JoinColumn(nullable: false)]
    private ?BasketOrder $basket_order = null;

    #[ORM\Column]
    #[Groups('order_product_action')]
    private ?int $quantity_required = null;

    #[ORM\Column]
    #[Groups('order_product_action')]
    private ?int $quantity_completed = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
    private ?OrderProduct $order_product_id = null;

    #[ORM\Column]
    #[Groups('order_product_action')]
    private ?int $status_id = null;

    #[ORM\Column(nullable: true)]
    #[Groups('order_product_action')]
    private ?int $quantity_packed = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $location = null;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getBasketOrder(): ?BasketOrder
    {
        return $this->basket_order;
    }

    public function setBasketOrder(?BasketOrder $basket_order): static
    {
        $this->basket_order = $basket_order;

        return $this;
    }

    public function getQuantityRequired(): ?int
    {
        return $this->quantity_required;
    }

    public function setQuantityRequired(int $quantity_required): static
    {
        $this->quantity_required = $quantity_required;

        return $this;
    }

    public function getQuantityCompleted(): ?int
    {
        return $this->quantity_completed;
    }

    public function setQuantityCompleted(int $quantity_completed): static
    {
        $this->quantity_completed = $quantity_completed;

        return $this;
    }

    public function getOrderProductId(): ?OrderProduct
    {
        return $this->order_product_id;
    }

    public function setOrderProductId(?OrderProduct $order_product_id): static
    {
        $this->order_product_id = $order_product_id;

        return $this;
    }

    public function getStatusId(): ?int
    {
        return $this->status_id;
    }

    public function setStatusId(int $status_id): static
    {
        $this->status_id = $status_id;

        return $this;
    }

    public function getQuantityPacked(): ?int
    {
        return $this->quantity_packed;
    }

    public function setQuantityPacked(?int $quantity_packed): static
    {
        $this->quantity_packed = $quantity_packed;

        return $this;
    }

    public function getLocation(): ?string
    {
        return $this->location;
    }

    public function setLocation(?string $location): static
    {
        $this->location = $location;

        return $this;
    }
}
