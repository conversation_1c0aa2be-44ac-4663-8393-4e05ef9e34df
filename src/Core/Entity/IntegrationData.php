<?php

namespace App\Core\Entity;

use App\Core\Enum\FetchProducts;
use App\Core\Enum\OrderFetchInterval;
use App\Core\Enum\OrderStatusSendPaid;
use App\Core\Enum\OrderStatusSynchronize;
use App\Core\Repository\IntegrationDataRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: IntegrationDataRepository::class)]
class IntegrationData
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\OneToOne(inversedBy: 'integrationData', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?Integration $integration = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?OrderStatus $fetchStatus = null;

    #[ORM\Column(enumType: OrderFetchInterval::class)]
    private ?OrderFetchInterval $orderFetchInterval = null;

    #[ORM\Column(enumType: FetchProducts::class)]
    private ?FetchProducts $fetchProducts = null;

    #[ORM\Column(enumType: OrderStatusSendPaid::class)]
    private ?OrderStatusSendPaid $orderStatusSendPaid = null;

    #[ORM\Column(enumType: OrderStatusSynchronize::class)]
    private ?OrderStatusSynchronize $orderStatusSynchronize = null;

    /**
     * @var Collection<int, IntegrationStatusMapping>
     */
    #[ORM\OneToMany(targetEntity: IntegrationStatusMapping::class, mappedBy: 'integrationData', orphanRemoval: true)]
    private Collection $integrationStatusMappings;

    #[ORM\Column(length: 255)]
    private ?string $apiUrl = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $apiToken = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $apiSecret = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?OrderStatus $cancelStatus = null;

    public function __construct()
    {
        $this->integrationStatusMappings = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getIntegration(): ?Integration
    {
        return $this->integration;
    }

    public function setIntegration(Integration $integration): static
    {
        $this->integration = $integration;

        return $this;
    }

    public function getFetchStatus(): ?OrderStatus
    {
        return $this->fetchStatus;
    }

    public function setFetchStatus(?OrderStatus $fetchStatus): static
    {
        $this->fetchStatus = $fetchStatus;

        return $this;
    }

    public function getOrderFetchInterval(): ?OrderFetchInterval
    {
        return $this->orderFetchInterval;
    }

    public function setOrderFetchInterval(OrderFetchInterval $orderFetchInterval): static
    {
        $this->orderFetchInterval = $orderFetchInterval;

        return $this;
    }

    public function getFetchProducts(): ?FetchProducts
    {
        return $this->fetchProducts;
    }

    public function setFetchProducts(FetchProducts $fetchProducts): static
    {
        $this->fetchProducts = $fetchProducts;

        return $this;
    }

    public function getOrderStatusSendPaid(): ?OrderStatusSendPaid
    {
        return $this->orderStatusSendPaid;
    }

    public function setOrderStatusSendPaid(OrderStatusSendPaid $orderStatusSendPaid): static
    {
        $this->orderStatusSendPaid = $orderStatusSendPaid;

        return $this;
    }

    public function getOrderStatusSynchronize(): ?OrderStatusSynchronize
    {
        return $this->orderStatusSynchronize;
    }

    public function setOrderStatusSynchronize(OrderStatusSynchronize $orderStatusSynchronize): static
    {
        $this->orderStatusSynchronize = $orderStatusSynchronize;

        return $this;
    }

    /**
     * @return Collection<int, IntegrationStatusMapping>
     */
    public function getIntegrationStatusMappings(): Collection
    {
        return $this->integrationStatusMappings;
    }

    public function addIntegrationStatusMapping(IntegrationStatusMapping $integrationStatusMapping): static
    {
        if (!$this->integrationStatusMappings->contains($integrationStatusMapping)) {
            $this->integrationStatusMappings->add($integrationStatusMapping);
            $integrationStatusMapping->setIntegrationData($this);
        }

        return $this;
    }

    public function removeIntegrationStatusMapping(IntegrationStatusMapping $integrationStatusMapping): static
    {
        if ($this->integrationStatusMappings->removeElement($integrationStatusMapping)) {
            // set the owning side to null (unless already changed)
            if ($integrationStatusMapping->getIntegrationData() === $this) {
                $integrationStatusMapping->setIntegrationData(null);
            }
        }

        return $this;
    }

    public function getApiUrl(): ?string
    {
        return $this->apiUrl;
    }

    public function setApiUrl(string $apiUrl): static
    {
        $this->apiUrl = $apiUrl;

        return $this;
    }

    public function getApiToken(): ?string
    {
        return $this->apiToken;
    }

    public function setApiToken(?string $apiToken): static
    {
        $this->apiToken = $apiToken;

        return $this;
    }

    public function getApiSecret(): ?string
    {
        return $this->apiSecret;
    }

    public function setApiSecret(?string $apiSecret): static
    {
        $this->apiSecret = $apiSecret;

        return $this;
    }

    public function getCancelStatus(): ?OrderStatus
    {
        return $this->cancelStatus;
    }

    public function setCancelStatus(?OrderStatus $cancelStatus): static
    {
        $this->cancelStatus = $cancelStatus;

        return $this;
    }
}
