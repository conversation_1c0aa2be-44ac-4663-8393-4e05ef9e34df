<?php

namespace App\Core\Entity;

use App\Core\Repository\OrderInvoiceRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: OrderInvoiceRepository::class)]
class OrderInvoice
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 200)]
    private ?string $invoice_fullname = null;

    #[ORM\Column(length: 200)]
    private ?string $invoice_company = null;

    #[ORM\Column(length: 100)]
    private ?string $invoice_nip = null;

    #[ORM\Column(length: 250)]
    private ?string $invoice_address = null;

    #[ORM\Column(length: 20)]
    private ?string $invoice_postcode = null;

    #[ORM\Column(length: 100)]
    private ?string $invoice_city = null;

    #[ORM\Column(length: 20)]
    private ?string $invoice_state = null;

    #[ORM\Column(length: 50)]
    private ?string $invoice_country = null;

    #[ORM\Column(length: 2)]
    private ?string $invoice_country_code = null;

    #[ORM\Column(length: 1)]
    private ?string $want_invoice = null;

    #[ORM\OneToOne(inversedBy: 'orderInvoice', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?Order $order = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $invoice_number = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $additionalNumber = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $mmNumber = null;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getInvoiceFullname(): ?string
    {
        return $this->invoice_fullname;
    }

    public function setInvoiceFullname(string $invoice_fullname): static
    {
        $this->invoice_fullname = $invoice_fullname;

        return $this;
    }

    public function getInvoiceCompany(): ?string
    {
        return $this->invoice_company;
    }

    public function setInvoiceCompany(string $invoice_company): static
    {
        $this->invoice_company = $invoice_company;

        return $this;
    }

    public function getInvoiceNip(): ?string
    {
        return $this->invoice_nip;
    }

    public function setInvoiceNip(string $invoice_nip): static
    {
        $this->invoice_nip = $invoice_nip;

        return $this;
    }

    public function getInvoiceAddress(): ?string
    {
        return $this->invoice_address;
    }

    public function setInvoiceAddress(string $invoice_address): static
    {
        $this->invoice_address = $invoice_address;

        return $this;
    }

    public function getInvoicePostcode(): ?string
    {
        return $this->invoice_postcode;
    }

    public function setInvoicePostcode(string $invoice_postcode): static
    {
        $this->invoice_postcode = $invoice_postcode;

        return $this;
    }

    public function getInvoiceCity(): ?string
    {
        return $this->invoice_city;
    }

    public function setInvoiceCity(string $invoice_city): static
    {
        $this->invoice_city = $invoice_city;

        return $this;
    }

    public function getInvoiceState(): ?string
    {
        return $this->invoice_state;
    }

    public function setInvoiceState(string $invoice_state): static
    {
        $this->invoice_state = $invoice_state;

        return $this;
    }

    public function getInvoiceCountry(): ?string
    {
        return $this->invoice_country;
    }

    public function setInvoiceCountry(string $invoice_country): static
    {
        $this->invoice_country = $invoice_country;

        return $this;
    }

    public function getInvoiceCountryCode(): ?string
    {
        return $this->invoice_country_code;
    }

    public function setInvoiceCountryCode(string $invoice_country_code): static
    {
        $this->invoice_country_code = $invoice_country_code;

        return $this;
    }

    public function getWantInvoice(): ?string
    {
        return $this->want_invoice;
    }

    public function setWantInvoice(string $want_invoice): static
    {
        $this->want_invoice = $want_invoice;

        return $this;
    }

    public function getOrder(): ?Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): static
    {
        $this->order = $order;

        return $this;
    }

    public function getInvoiceNumber(): ?string
    {
        return $this->invoice_number;
    }

    public function setInvoiceNumber(?string $invoice_number): static
    {
        $this->invoice_number = $invoice_number;

        return $this;
    }

    public function getAdditionalNumber(): ?string
    {
        return $this->additionalNumber;
    }

    public function setAdditionalNumber(?string $additionalNumber): static
    {
        $this->additionalNumber = $additionalNumber;

        return $this;
    }

    public function getMmNumber(): ?string
    {
        return $this->mmNumber;
    }

    public function setMmNumber(?string $mmNumber): static
    {
        $this->mmNumber = $mmNumber;

        return $this;
    }
}
