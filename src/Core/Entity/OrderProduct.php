<?php

namespace App\Core\Entity;

use App\Core\Repository\OrderProductRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Serializer\Annotation\MaxDepth;

#[ORM\Entity(repositoryClass: OrderProductRepository::class)]
class OrderProduct
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 9)]
    private ?string $storage = null;

    #[ORM\Column]
    private ?int $storage_id = null;

    #[ORM\Column]
    private ?int $order_product_id = null;

    #[ORM\Column(length: 50)]
    private ?string $product_id = null;

    #[ORM\Column(length: 30)]
    private ?string $variant_id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 50)]
    private ?string $sku = null;

    #[ORM\Column(length: 32)]
    private ?string $ean = null;

    #[ORM\Column(length: 50)]
    private ?string $location = null;

    #[ORM\Column]
    private ?int $warehouse_id = null;

    #[ORM\Column(length: 50)]
    private ?string $auction_id = null;

    #[ORM\Column(length: 150)]
    private ?string $attributes = null;

    #[ORM\Column]
    private ?int $price_brutto = null;

    #[ORM\Column]
    private ?int $tax_rate = null;

    #[ORM\Column]
    private ?int $quantity = null;

    #[ORM\Column]
    private ?float $weight = null;

    #[ORM\Column]
    private ?int $bundle_id = null;

    #[ORM\ManyToOne(inversedBy: 'products')]
    #[ORM\JoinColumn(nullable: false)]
    #[MaxDepth(2)]
    private ?Order $order_id = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $cover_image_url = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?int $full_price = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $width = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $height = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $depth = null;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getStorage(): ?string
    {
        return $this->storage;
    }

    public function setStorage(string $storage): static
    {
        $this->storage = $storage;

        return $this;
    }

    public function getStorageId(): ?int
    {
        return $this->storage_id;
    }

    public function setStorageId(int $storage_id): static
    {
        $this->storage_id = $storage_id;

        return $this;
    }

    public function getOrderProductId(): ?int
    {
        return $this->order_product_id;
    }

    public function setOrderProductId(int $order_product_id): static
    {
        $this->order_product_id = $order_product_id;

        return $this;
    }

    public function getProductId(): ?string
    {
        return $this->product_id;
    }

    public function setProductId(string $product_id): static
    {
        $this->product_id = $product_id;

        return $this;
    }

    public function getVariantId(): ?string
    {
        return $this->variant_id;
    }

    public function setVariantId(string $variant_id): static
    {
        $this->variant_id = $variant_id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getSku(): ?string
    {
        return $this->sku;
    }

    public function setSku(string $sku): static
    {
        $this->sku = $sku;

        return $this;
    }

    public function getEan(): ?string
    {
        return $this->ean;
    }

    public function setEan(string $ean): static
    {
        $this->ean = $ean;

        return $this;
    }

    public function getLocation(): ?string
    {
        return $this->location;
    }

    public function setLocation(string $location): static
    {
        $this->location = $location;

        return $this;
    }

    public function getWarehouseId(): ?int
    {
        return $this->warehouse_id;
    }

    public function setWarehouseId(int $warehouse_id): static
    {
        $this->warehouse_id = $warehouse_id;

        return $this;
    }

    public function getAuctionId(): ?string
    {
        return $this->auction_id;
    }

    public function setAuctionId(string $auction_id): static
    {
        $this->auction_id = $auction_id;

        return $this;
    }

    public function getAttributes(): ?string
    {
        return $this->attributes;
    }

    public function setAttributes(string $attributes): static
    {
        $this->attributes = $attributes;

        return $this;
    }

    public function getPriceBrutto(): ?int
    {
        return $this->price_brutto;
    }

    public function setPriceBrutto(int $price_brutto): static
    {
        $this->price_brutto = $price_brutto;

        return $this;
    }

    public function getTaxRate(): ?float
    {
        return $this->tax_rate;
    }

    public function setTaxRate(int|float $tax_rate): static
    {
        $this->tax_rate = (float) $tax_rate;

        return $this;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): static
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getWeight(): ?float
    {
        return $this->weight / 100;
    }

    public function setWeight(int|float $weight): static
    {
        $this->weight = (float) $weight;

        return $this;
    }

    public function getBundleId(): ?int
    {
        return $this->bundle_id;
    }

    public function setBundleId(int $bundle_id): static
    {
        $this->bundle_id = $bundle_id;

        return $this;
    }

    public function getOrderId(): ?Order
    {
        return $this->order_id;
    }

    public function setOrderId(?Order $order_id): static
    {
        $this->order_id = $order_id;

        return $this;
    }

    public function __clone() {
        if ($this->id) {
            $this->id = null;
        }

        return $this;
    }

    public function getCoverImageUrl(): ?string
    {
        return $this->cover_image_url;
    }

    public function setCoverImageUrl(?string $cover_image_url): static
    {
        $this->cover_image_url = $cover_image_url;

        return $this;
    }

    public function getFullPrice(): ?int
    {
        return $this->full_price;
    }

    public function setFullPrice(?int $full_price): static
    {
        $this->full_price = $full_price;

        return $this;
    }

    public function getWidth(): ?string
    {
        return $this->width;
    }

    public function setWidth(?string $width): static
    {
        $this->width = $width;

        return $this;
    }

    public function getHeight(): ?string
    {
        return $this->height;
    }

    public function setHeight(?string $height): static
    {
        $this->height = $height;

        return $this;
    }

    public function getDepth(): ?string
    {
        return $this->depth;
    }

    public function setDepth(?string $depth): static
    {
        $this->depth = $depth;

        return $this;
    }
}
