<?php

namespace App\Core\Entity;

use App\Core\Repository\OrderDeliveryShipmentDataRepository;
use App\Core\Taxonomy\DateTaxonomy;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: OrderDeliveryShipmentDataRepository::class)]
class OrderDeliveryShipmentData
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255)]
    private ?string $carrier_type = null;

    #[ORM\ManyToOne(inversedBy: 'orderDeliveryShipmentData')]
    #[ORM\JoinColumn(nullable: false)]
    private ?OrderDelivery $order_delivery_id = null;

    #[ORM\Column(nullable: true)]
    private ?bool $label = null;

    #[ORM\Column(nullable: true)]
    private ?bool $invoice = null;

    #[ORM\Column(nullable: true)]
    private ?bool $invoice_mail = null;

    #[ORM\Column(nullable: true)]
    private ?bool $shipment_mail = null;

    #[ORM\Column(length: 200, nullable: true)]
    private ?string $shipment_id = null;

    #[ORM\Column(length: 200, nullable: true)]
    private ?string $tracking_id = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $shipment_status = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $shipment_status_date = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $label_date_created = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $shipment_date = null;

    #[ORM\ManyToOne]
    private ?Carrier $carrier = null;


    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getCarrierType(): ?string
    {
        return $this->carrier_type;
    }

    public function setCarrierType(string $carrier_type): static
    {
        $this->carrier_type = $carrier_type;

        return $this;
    }

    public function getOrderDeliveryId(): ?OrderDelivery
    {
        return $this->order_delivery_id;
    }

    public function setOrderDeliveryId(?OrderDelivery $order_delivery_id): static
    {
        $this->order_delivery_id = $order_delivery_id;

        return $this;
    }

    public function isLabel(): ?bool
    {
        return $this->label;
    }

    public function setLabel(?bool $label): static
    {
        $this->label = $label;
        $date = new \DateTime('now', new \DateTimeZone('Europe/Warsaw'));
        $this->setLabelDateCreated($date->format(DateTaxonomy::DATE_FORMAT));

        return $this;
    }

    public function isInvoice(): ?bool
    {
        return $this->invoice;
    }

    public function setInvoice(?bool $invoice): static
    {
        $this->invoice = $invoice;

        return $this;
    }

    public function isInvoiceMail(): ?bool
    {
        return $this->invoice_mail;
    }

    public function setInvoiceMail(?bool $invoice_mail): static
    {
        $this->invoice_mail = $invoice_mail;

        return $this;
    }

    public function isShipmentMail(): ?bool
    {
        return $this->shipment_mail;
    }

    public function setShipmentMail(?bool $shipment_mail): static
    {
        $this->shipment_mail = $shipment_mail;

        return $this;
    }

    public function getShipmentId(): ?string
    {
        return $this->shipment_id;
    }

    public function setShipmentId(?string $shipment_id): static
    {
        $this->shipment_id = $shipment_id;
        $date = new \DateTime('now', new \DateTimeZone('Europe/Warsaw'));
        $this->setShipmentDate($date->format(DateTaxonomy::DATE_FORMAT));

        return $this;
    }

    public function getTrackingId(): ?string
    {
        return $this->tracking_id;
    }

    public function setTrackingId(?string $tracking_id): static
    {
        $this->tracking_id = $tracking_id;

        return $this;
    }

    public function getLabelDateCreated(): ?string
    {
        return $this->label_date_created;
    }

    public function setLabelDateCreated(?string $label_date_created): static
    {
        $this->label_date_created = $label_date_created;

        return $this;
    }

    public function getShipmentDate(): ?string
    {
        return $this->shipment_date;
    }

    public function setShipmentDate(?string $shipment_date): static
    {
        $this->shipment_date = $shipment_date;

        return $this;
    }

    public function getShipmentStatus(): ?string {
        return $this->shipment_status;
    }

    public function setShipmentStatus(?string $shipment_status): void {
        $this->shipment_status = $shipment_status;
    }

    public function getShipmentStatusDate(): ?string {
        return $this->shipment_status_date;

    }

    public function setShipmentStatusDate(): static {
        $date = new \DateTime('now', new \DateTimeZone('Europe/Warsaw'));
        $this->shipment_status_date = $date->format(DateTaxonomy::DATE_FORMAT);

        return $this;

    }

    public function getCarrier(): ?Carrier
    {
        return $this->carrier;
    }

    public function setCarrier(?Carrier $carrier): static
    {
        $this->carrier = $carrier;

        return $this;
    }

    public function __toString() {
        return sprintf(
            "carierType: %s\n trackingId - %s\n, trackingUrl - %s\n, data - %s\n",
            $this->getCarrierType(),
            $this->getShipmentId(),
            $this->getTrackingId(),
            $this->getShipmentDate()
        );
    }
}
