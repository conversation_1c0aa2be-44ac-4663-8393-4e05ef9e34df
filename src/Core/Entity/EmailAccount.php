<?php

namespace App\Core\Entity;

use App\Core\Repository\EmailAccountRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: EmailAccountRepository::class)]
class EmailAccount
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255)]
    private ?string $smtp_address = null;

    #[ORM\Column(length: 255)]
    private ?string $smtp_login = null;

    #[ORM\Column(length: 255)]
    private ?string $smtp_password = null;

    #[ORM\Column(length: 255)]
    private ?string $email_address = null;

    #[ORM\Column(length: 255)]
    private ?string $sender_name = null;

    #[ORM\Column(length: 255)]
    private ?string $smtp_security = null;

    #[ORM\Column(length: 255)]
    private ?string $full_name = null;

    #[ORM\Column]
    private ?int $smtp_port = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $domain = null;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getSmtpAddress(): ?string
    {
        return $this->smtp_address;
    }

    public function setSmtpAddress(string $smtp_address): static
    {
        $this->smtp_address = $smtp_address;

        return $this;
    }

    public function getSmtpLogin(): ?string
    {
        return $this->smtp_login;
    }

    public function setSmtpLogin(string $smtp_login): static
    {
        $this->smtp_login = $smtp_login;

        return $this;
    }

    public function getSmtpPassword(): ?string
    {
        return $this->smtp_password;
    }

    public function setSmtpPassword(string $smtp_password): static
    {
        $this->smtp_password = $smtp_password;

        return $this;
    }

    public function getEmailAddress(): ?string
    {
        return $this->email_address;
    }

    public function setEmailAddress(string $email_address): static
    {
        $this->email_address = $email_address;

        return $this;
    }

    public function getSenderName(): ?string
    {
        return $this->sender_name;
    }

    public function setSenderName(string $sender_name): static
    {
        $this->sender_name = $sender_name;

        return $this;
    }

    public function getSmtpSecurity(): ?string
    {
        return $this->smtp_security;
    }

    public function setSmtpSecurity(string $smtp_security): static
    {
        $this->smtp_security = $smtp_security;

        return $this;
    }

    public function getFullName(): ?string
    {
        return $this->full_name;
    }

    public function setFullName(string $full_name): static
    {
        $this->full_name = $full_name;

        return $this;
    }

    public function getSmtpPort(): ?int
    {
        return $this->smtp_port;
    }

    public function setSmtpPort(int $smtp_port): static
    {
        $this->smtp_port = $smtp_port;

        return $this;
    }

    public function getDomain(): ?string
    {
        return $this->domain;
    }

    public function setDomain(?string $domain): static
    {
        $this->domain = $domain;

        return $this;
    }
}
