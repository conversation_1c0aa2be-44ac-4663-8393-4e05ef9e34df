<?php

namespace App\Core\Entity;

use App\Core\Repository\DocumentRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: DocumentRepository::class)]
class Document
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\ManyToOne(inversedBy: 'RelatedDocuments')]
    #[ORM\JoinColumn(nullable: false)]
    private ?DocumentType $type = null;

    #[ORM\Column(length: 255)]
    private ?string $documentNumberInternal = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $documentNumberExternal = null;

    #[ORM\ManyToOne(inversedBy: 'RelatedDocuments')]
    #[ORM\JoinColumn(nullable: false)]
    private ?DocumentStatus $status = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $updatedAt = null;

    /**
     * @var Collection<int, DocumentItem>
     */
    #[ORM\OneToMany(targetEntity: DocumentItem::class, mappedBy: 'document', cascade: ['persist'])]
    private Collection $documentItems;

    #[ORM\ManyToOne]
    private ?DocumentSellerCompany $sellerCompany = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $accounting_reference = null;

    public function __construct()
    {
        $this->documentItems = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getType(): ?DocumentType
    {
        return $this->type;
    }

    public function setType(?DocumentType $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getDocumentNumberInternal(): ?string
    {
        return $this->documentNumberInternal;
    }

    public function setDocumentNumberInternal(string $documentNumberInternal): static
    {
        $this->documentNumberInternal = $documentNumberInternal;

        return $this;
    }

    public function getDocumentNumberExternal(): ?string
    {
        return $this->documentNumberExternal;
    }

    public function setDocumentNumberExternal(?string $documentNumberExternal): static
    {
        $this->documentNumberExternal = $documentNumberExternal;

        return $this;
    }

    public function getStatus(): ?DocumentStatus
    {
        return $this->status;
    }

    public function setStatus(?DocumentStatus $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * @return Collection<int, DocumentItem>
     */
    public function getDocumentItems(): Collection
    {
        return $this->documentItems;
    }

    public function addDocumentItem(DocumentItem $documentItem): static
    {
        if (!$this->documentItems->contains($documentItem)) {
            $this->documentItems->add($documentItem);
            $documentItem->setDocument($this);
        }

        return $this;
    }

    public function removeDocumentItem(DocumentItem $documentItem): static
    {
        if ($this->documentItems->removeElement($documentItem)) {
            if ($documentItem->getDocument() === $this) {
                $documentItem->setDocument(null);
            }
        }

        return $this;
    }

    public function getSellerCompany(): ?DocumentSellerCompany
    {
        return $this->sellerCompany;
    }

    public function setSellerCompany(?DocumentSellerCompany $sellerCompany): static
    {
        $this->sellerCompany = $sellerCompany;

        return $this;
    }

    public function getAccountingReference(): ?string
    {
        return $this->accounting_reference;
    }

    public function setAccountingReference(?string $accounting_reference): static
    {
        $this->accounting_reference = $accounting_reference;

        return $this;
    }

}
