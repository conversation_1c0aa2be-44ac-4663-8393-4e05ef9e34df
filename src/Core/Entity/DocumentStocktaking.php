<?php

namespace App\Core\Entity;

use App\Core\Repository\DocumentStocktakingRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: DocumentStocktakingRepository::class)]
class DocumentStocktaking
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $internal_number = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $external_number = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Storehouse $storehouse = null;

    /**
     * @var Collection<int, DocumentShelfStocktaking>
     */
    #[ORM\OneToMany(targetEntity: DocumentShelfStocktaking::class, mappedBy: 'documentStocktaking', cascade: ['persist'])]
    private Collection $DocumentShelfStocktaking;

    #[ORM\Column]
    private ?bool $finished = false;

    public function __construct()
    {
        $this->DocumentShelfStocktaking = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getInternalNumber(): ?string
    {
        return $this->internal_number;
    }

    public function setInternalNumber(?string $internal_number): static
    {
        $this->internal_number = $internal_number;

        return $this;
    }

    public function getExternalNumber(): ?string
    {
        return $this->external_number;
    }

    public function setExternalNumber(?string $external_number): static
    {
        $this->external_number = $external_number;

        return $this;
    }

    public function getStorehouse(): ?Storehouse
    {
        return $this->storehouse;
    }

    public function setStorehouse(?Storehouse $storehouse): static
    {
        $this->storehouse = $storehouse;

        return $this;
    }

    /**
     * @return Collection<int, DocumentShelfStocktaking>
     */
    public function getDocumentShelfStocktaking(): Collection
    {
        return $this->DocumentShelfStocktaking;
    }

    public function addDocumentShelfStocktaking(DocumentShelfStocktaking $documentShelfStocktaking): static
    {
        if (!$this->DocumentShelfStocktaking->contains($documentShelfStocktaking)) {
            $this->DocumentShelfStocktaking->add($documentShelfStocktaking);
            $documentShelfStocktaking->setDocumentStocktaking($this);
        }

        return $this;
    }

    public function removeDocumentShelfStocktaking(DocumentShelfStocktaking $documentShelfStocktaking): static
    {
        if ($this->DocumentShelfStocktaking->removeElement($documentShelfStocktaking)) {
            // set the owning side to null (unless already changed)
            if ($documentShelfStocktaking->getDocumentStocktaking() === $this) {
                $documentShelfStocktaking->setDocumentStocktaking(null);
            }
        }

        return $this;
    }

    public function isFinished(): ?bool
    {
        return $this->finished;
    }

    public function setFinished(bool $finished): static
    {
        $this->finished = $finished;

        return $this;
    }
}
