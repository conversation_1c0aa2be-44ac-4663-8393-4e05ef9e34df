<?php

namespace App\Core\Entity;

use App\Core\Repository\EanShelfQuantityRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: EanShelfQuantityRepository::class)]
class EanShelfQuantity
{
    #[Groups(['scan'])]
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\ManyToOne(fetch: 'EAGER', inversedBy: 'eanShelfQuantities')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Ean $ean = null;
    #[Groups(['scan'])]
    #[ORM\ManyToOne(fetch: 'EAGER', inversedBy: 'eanShelfQuantities')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Shelf $shelf = null;
    #[Groups(['scan'])]
    #[ORM\Column(nullable: true)]
    private ?int $quantity = 0;

    #[Groups(['scan'])]
    #[ORM\Column(nullable: true)]
    private ?int $virtual_quantity = 0;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getEan(): ?Ean
    {
        return $this->ean;
    }

    public function setEan(?Ean $ean): static
    {
        $this->ean = $ean;

        return $this;
    }

    public function getShelf(): ?Shelf
    {
        return $this->shelf;
    }

    public function setShelf(?Shelf $shelf): static
    {
        $this->shelf = $shelf;

        return $this;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function setQuantity(?int $quantity): static
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getVirtualQuantity(): ?int
    {
        return $this->virtual_quantity;
    }

    public function setVirtualQuantity(?int $virtual_quantity): static
    {
        $this->virtual_quantity = $virtual_quantity;

        return $this;
    }
}
