<?php

namespace App\Core\Entity;

use App\Core\Repository\CarrierRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: CarrierRepository::class)]
class Carrier
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 255)]
    private ?string $type = null;

    /**
     * @var Collection<int, CarrierSettings>
     */
    #[ORM\OneToMany(targetEntity: CarrierSettings::class, mappedBy: 'carrier', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $carrierSettings;

    public function __construct()
    {
        $this->carrierSettings = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return Collection<int, CarrierSettings>
     */
    public function getCarrierSettings(): Collection
    {
        return $this->carrierSettings;
    }

    public function addCarrierSetting(CarrierSettings $carrierSetting): static
    {
        if (!$this->carrierSettings->contains($carrierSetting)) {
            $this->carrierSettings->add($carrierSetting);
            $carrierSetting->setCarrier($this);
        }

        return $this;
    }

    public function removeCarrierSetting(CarrierSettings $carrierSetting): static
    {
        if ($this->carrierSettings->removeElement($carrierSetting)) {
            // set the owning side to null (unless already changed)
            if ($carrierSetting->getCarrier() === $this) {
                $carrierSetting->setCarrier(null);
            }
        }

        return $this;
    }

    public function getSettingsByKey(string $key): ?string {
        $result = $this->carrierSettings->findFirst(function($k, $v) use ($key) {
            return $key === $v->getName();
        });
        if ($result instanceof CarrierSettings) {
            return $result->getValue();
        }

        return null;
    }
    
    public function getSettingObjectByKey(string $key): ?CarrierSettings {
        $result = $this->carrierSettings->findFirst(function($k, $v) use ($key) {
            return $key === $v->getName();
        });
        if ($result instanceof CarrierSettings) {
            return $result;
        }
        
        return null;
    }
}
