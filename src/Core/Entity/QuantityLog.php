<?php

namespace App\Core\Entity;

use App\Core\Repository\QuantityLogRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: QuantityLogRepository::class)]
class QuantityLog
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Order $id_order = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?OrderProduct $id_product = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?EanShelfQuantity $id_esq = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Storehouse $id_storehouse = null;

    #[ORM\Column]
    private ?int $quantity_required = null;

    #[ORM\Column]
    private ?int $quantity_available = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $date_checked = null;


    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getIdOrder(): ?Order
    {
        return $this->id_order;
    }

    public function setIdOrder(?Order $id_order): static
    {
        $this->id_order = $id_order;

        return $this;
    }

    public function getIdProduct(): ?OrderProduct
    {
        return $this->id_product;
    }

    public function setIdProduct(?OrderProduct $id_product): static
    {
        $this->id_product = $id_product;

        return $this;
    }

    public function getIdEsq(): ?EanShelfQuantity
    {
        return $this->id_esq;
    }

    public function setIdEsq(?EanShelfQuantity $id_esq): static
    {
        $this->id_esq = $id_esq;

        return $this;
    }

    public function getDateChecked(): ?\DateTimeInterface
    {
        return $this->date_checked;
    }

    public function setDateChecked(\DateTimeInterface $date_checked): static
    {
        $this->date_checked = $date_checked;

        return $this;
    }

    public function getQuantityRequired(): ?int
    {
        return $this->quantity_required;
    }

    public function setQuantityRequired(int $quantity_required): static
    {
        $this->quantity_required = $quantity_required;

        return $this;
    }

    public function getQuantityAvailable(): ?int
    {
        return $this->quantity_available;
    }

    public function setQuantityAvailable(int $quantity_available): static
    {
        $this->quantity_available = $quantity_available;

        return $this;
    }

    public function getIdStorehouse(): ?Storehouse
    {
        return $this->id_storehouse;
    }

    public function setIdStorehouse(?Storehouse $id_storehouse): static
    {
        $this->id_storehouse = $id_storehouse;

        return $this;
    }
}
