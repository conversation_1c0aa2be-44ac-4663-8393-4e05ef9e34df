<?php

namespace App\Core\Entity;

use App\Core\Repository\DocumentItemRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: DocumentItemRepository::class)]
class DocumentItem
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\ManyToOne(inversedBy: 'documentItems')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Document $document = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $name = null;

    #[ORM\Column(nullable: true)]
    private ?int $quantityRequired = null;

    #[ORM\Column(length: 15)]
    private ?string $ean13 = null;

    #[ORM\Column(length: 30, nullable: true)]
    private ?string $reference = null;

    #[ORM\Column(nullable: true)]
    private ?float $unitPriceNetto = null;

    #[ORM\Column(nullable: true)]
    private ?float $unitPriceBrutto = null;

    #[ORM\Column(nullable: true)]
    private ?float $totalPriceNetto = null;

    #[ORM\Column(nullable: true)]
    private ?float $totalPriceBrutto = null;

    #[ORM\Column(nullable: true)]
    private ?float $tax = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $expirationDate = null;

    #[ORM\Column(nullable: true)]
    private ?int $quantityCompleted = null;

    #[ORM\Column(nullable: true ,options: ['default' => false])]
    private ?bool $isInSubiekt = null;

    #[ORM\Column(options: ['default' => false])]
    private ?bool $sample = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $eans = null;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getDocument(): ?Document
    {
        return $this->document;
    }

    public function setDocument(?Document $document): static
    {
        $this->document = $document;

        return $this;
    }

    public function getName(): ?string {
        return $this->name;
    }

    public function setName(?string $name): void {
        $this->name = $name;
    }



    public function getQuantityRequired(): ?int
    {
        return $this->quantityRequired;
    }

    public function setQuantityRequired(int $quantityRequired): static
    {
        $this->quantityRequired = $quantityRequired;

        return $this;
    }

    public function getEan13(): ?string
    {
        return $this->ean13;
    }

    public function setEan13(string $ean13): static
    {
        $this->ean13 = $ean13;

        return $this;
    }

    public function getReference(): ?string
    {
        return $this->reference;
    }

    public function setReference(string $reference): static
    {
        $this->reference = $reference;

        return $this;
    }

    public function getUnitPriceNetto(): ?float
    {
        return $this->unitPriceNetto;
    }

    public function setUnitPriceNetto(float $unitPriceNetto): static
    {
        $this->unitPriceNetto = $unitPriceNetto;

        return $this;
    }

    public function getUnitPriceBrutto(): ?float
    {
        return $this->unitPriceBrutto;
    }

    public function setUnitPriceBrutto(float $unitPriceBrutto): static
    {
        $this->unitPriceBrutto = $unitPriceBrutto;

        return $this;
    }

    public function getTotalPriceNetto(): ?float
    {
        return $this->totalPriceNetto;
    }

    public function setTotalPriceNetto(float $totalPriceNetto): static
    {
        $this->totalPriceNetto = $totalPriceNetto;

        return $this;
    }

    public function getTotalPriceBrutto(): ?float
    {
        return $this->totalPriceBrutto;
    }

    public function setTotalPriceBrutto(float $totalPriceBrutto): static
    {
        $this->totalPriceBrutto = $totalPriceBrutto;

        return $this;
    }

    public function getTax(): ?int
    {
        return $this->tax;
    }

    public function setTax(int $tax): static
    {
        $this->tax = $tax;

        return $this;
    }

    public function getExpirationDate(): ?\DateTimeInterface
    {
        return $this->expirationDate;
    }

    public function setExpirationDate(?\DateTimeInterface $expirationDate): static
    {
        $this->expirationDate = $expirationDate;

        return $this;
    }

    public function getQuantityCompleted(): ?int
    {
        return $this->quantityCompleted;
    }

    public function setQuantityCompleted(int $quantityCompleted): static
    {
        $this->quantityCompleted = $quantityCompleted;

        return $this;
    }

    public function getIsInSubiekt(): ?bool
    {
        return $this->isInSubiekt;
    }

    public function setIsInSubiekt(?bool $isInSubiekt): static
    {
        $this->isInSubiekt = $isInSubiekt;

        return $this;
    }

    public function isSample(): ?bool
    {
        return $this->sample;
    }

    public function setSample(bool $sample): static
    {
        $this->sample = $sample;

        return $this;
    }

    public function getEans(): ?array
    {
        return $this->eans;
    }

    public function setEans(?array $eans): static
    {
        $this->eans = $eans;

        return $this;
    }
}
