<?php

namespace App\Core\Entity;

use App\Core\Repository\DocumentShelfStocktakingRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: DocumentShelfStocktakingRepository::class)]
class DocumentShelfStocktaking
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Shelf $shelf = null;

    /**
     * @var Collection<int, DocumentShelfStocktakingData>
     */
    #[ORM\OneToMany(targetEntity: DocumentShelfStocktakingData::class, mappedBy: 'documentShelfStocktaking', cascade: ['persist'], orphanRemoval: true)]
    private Collection $stock;

    #[ORM\Column]
    private ?bool $finished = false;

    #[ORM\ManyToOne(inversedBy: 'DocumentShelfStocktaking')]
    private ?DocumentStocktaking $documentStocktaking = null;

    public function __construct()
    {
        $this->stock = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getShelf(): ?Shelf
    {
        return $this->shelf;
    }

    public function setShelf(?Shelf $shelf): static
    {
        $this->shelf = $shelf;

        return $this;
    }

    /**
     * @return Collection<int, DocumentShelfStocktakingData>
     */
    public function getStock(): Collection
    {
        return $this->stock;
    }

    public function addStock(DocumentShelfStocktakingData $stock): static
    {
        if (!$this->stock->contains($stock)) {
            $this->stock->add($stock);
            $stock->setDocumentShelfStocktaking($this);
        }

        return $this;
    }

    public function removeStock(DocumentShelfStocktakingData $stock): static
    {
        if ($this->stock->removeElement($stock)) {
            // set the owning side to null (unless already changed)
            if ($stock->getDocumentShelfStocktaking() === $this) {
                $stock->setDocumentShelfStocktaking(null);
            }
        }

        return $this;
    }

    public function isFinished(): ?bool
    {
        return $this->finished;
    }

    public function setFinished(bool $finished): static
    {
        $this->finished = $finished;

        return $this;
    }

    public function getDocumentStocktaking(): ?DocumentStocktaking
    {
        return $this->documentStocktaking;
    }

    public function setDocumentStocktaking(?DocumentStocktaking $documentStocktaking): static
    {
        $this->documentStocktaking = $documentStocktaking;

        return $this;
    }
}
