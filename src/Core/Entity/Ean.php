<?php

namespace App\Core\Entity;

use App\Core\Repository\EanRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: EanRepository::class)]
class Ean
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    #[Groups(['scan'])]
    private ?Uuid $id = null;

    #[Groups(['scan'])]
    #[ORM\Column(length: 255)]
    private ?string $ean = null;

    #[ORM\ManyToOne(fetch: 'EAGER', inversedBy: 'eans')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Product $product = null;

    /**
     * @var Collection<int, EanShelfQuantity>
     */
    #[Groups(['scan'])]
    #[ORM\OneToMany(targetEntity: EanShelfQuantity::class, mappedBy: 'ean', cascade: ['persist'], fetch: 'EAGER', orphanRemoval: true)]
    private Collection $eanShelfQuantities;

    public function __construct()
    {
        $this->eanShelfQuantities = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getEan(): ?string
    {
        return $this->ean;
    }

    public function setEan(string $ean): static
    {
        $this->ean = $ean;

        return $this;
    }

    public function getProduct(): ?Product
    {
        return $this->product;
    }

    public function setProduct(?Product $product): static
    {
        $this->product = $product;

        return $this;
    }

    /**
     * @return Collection<int, EanShelfQuantity>
     */
    public function getEanShelfQuantities(): Collection
    {
        return $this->eanShelfQuantities;
    }

    public function addEanShelfQuantity(EanShelfQuantity $eanShelfQuantity): static
    {
        if (!$this->eanShelfQuantities->contains($eanShelfQuantity)) {
            $this->eanShelfQuantities->add($eanShelfQuantity);
            $eanShelfQuantity->setEan($this);
        }

        return $this;
    }

    public function removeEanShelfQuantity(EanShelfQuantity $eanShelfQuantity): static
    {
        if ($this->eanShelfQuantities->removeElement($eanShelfQuantity)) {
            // set the owning side to null (unless already changed)
            if ($eanShelfQuantity->getEan() === $this) {
                $eanShelfQuantity->setEan(null);
            }
        }

        return $this;
    }
}
