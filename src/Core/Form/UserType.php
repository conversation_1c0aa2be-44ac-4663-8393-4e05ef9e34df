<?php

namespace App\Core\Form;

use App\Core\Entity\User;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class UserType extends AbstractType
{
    public function __construct(private UserPasswordHasherInterface $passwordHasher) {
    }
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('email')
            ->add('roles', CollectionType::class)
            ->add('password')
            ->add('isVerified')
        ;
        $builder->addEventListener(FormEvents::POST_SUBMIT, function (FormEvent $event) {

            $user = $event->getData();
            $form = $event->getForm();
            $plainPassword = $form->get('password')->getData();

            if ($plainPassword) {
                $this->generateUserPassword($user, $plainPassword);
            }
        });
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => User::class,
        ]);
    }

    private function generateUserPassword(&$user, $password): void
    {
        $hashed = $this->passwordHasher->hashPassword($user, $password);
        $user->setPassword($hashed);
    }
}
