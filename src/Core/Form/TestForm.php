<?php

namespace App\Core\Form;

use App\Core\Form\Base\FormBase;

class TestForm extends FormBase {

    public function getFormId(): string {
        return 'test_form';
    }

    public function buildForm(array $form, array $form_state): array {
        $form['test_field'] = [
            'type' => 'text',
            'title' => 'Test field',
            'default_value' => 'Test value',
        ];

        return $form;
    }
}