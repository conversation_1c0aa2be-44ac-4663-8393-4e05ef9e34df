<?php

namespace App\Core\Attribute;

use Attribute;

#[Attribute(Attribute::TARGET_METHOD)]
class AsMenuLink {

    private string $linkName;

    private ?string $permission;

    private ?string $parent;

    private ?string $icon;

    private ?int $weight;


    public function __construct($linkName, $permission = null, $parent = null, $icon = null, $weight = 0)
    {
        $this->linkName = $linkName;
        $this->permission = $permission;
        $this->parent = $parent;
        $this->icon = $icon;
        $this->weight = $weight;
    }

    public function getLinkName(): string {
        return $this->linkName;
    }
    public function getPermission(): ?string {
        return $this->permission;
    }
    public function getParent(): ?string {
        return $this->parent;
    }
    public function getIcon(): ?string {
        return $this->icon;
    }
    public function getWeight(): ?int {
        return $this->weight;
    }
}