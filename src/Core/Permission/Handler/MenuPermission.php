<?php

namespace App\Core\Permission\Handler;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

class MenuPermission extends Voter {

    public function __construct(private readonly EntityManagerInterface $entityManager) {

    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return $attribute === 'menu';
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        return true;
    }
}