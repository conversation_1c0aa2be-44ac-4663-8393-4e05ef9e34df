<?php

namespace App\Module\Core;

use App\Engine\Hook\HookManager;
use App\Engine\Hook\RegionComponent;
use App\Module\AbstractModule;
use App\Module\ModuleInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

#[AutoconfigureTag(ModuleInterface::class)]
class NavigationModule extends AbstractModule {

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly HookManager $hookManager,
        private readonly UrlGeneratorInterface $urlGenerator,
    ) {}

    public function getId(): string
    {
        return 'navigation';
    }

    public function getName(): string
    {
        return 'Navigation';
    }

    public function getDescription(): string
    {
        return 'Provides main navigation menu and page header';
    }

    public function getVersion(): string
    {
        return '1.0.0';
    }

    public function getHooks(): array
    {
        return ['top_panel', 'sidebar_menu'];
    }

    protected function hook_top_panel(array $context): array
    {
        $request = $context['request'] ?? null;
        $pageData = $context['page_data'] ?? [];

        if (!$request) {
            return [];
        }

        $pageTitle = $pageData['title'] ?? $this->generatePageTitle($request);
        $breadcrumbs = $pageData['breadcrumbs'] ?? $this->generateBreadcrumbs($request);

        return [
            RegionComponent::create('components/topbar.html.twig', [
                'page_title' => $pageTitle,
                'breadcrumbs' => $breadcrumbs,
                'user' => $context['user'] ?? null,
                'current_route' => $request->attributes->get('_route')
            ], [], [], 0)
        ];
    }

    protected function hook_sidebar_menu(array $context): array
    {
        $request = $context['request'] ?? null;

        if (!$request) {
            return [];
        }

        $menuItems = $this->getMenuItems();
        $menuItems = $this->hookManager->callModuleHooks('menu', $menuItems);
        return [
            RegionComponent::create('components/sidebar.html.twig', [
                'menu_items' => $menuItems,
                'current_route' => $request->attributes->get('_route'),
                'user' => $context['user'] ?? null
            ], [], [], 0)
        ];
    }

    private function generatePageTitle($request): string
    {
        $route = $request->attributes->get('_route');

        return match($route) {
            'app_homepage' => 'Dashboard',
            default => 'Page'
        };
    }

    private function generateBreadcrumbs($request): array
    {
        $route = $request->attributes->get('_route');

        $breadcrumbs = [
            ['label' => 'Home', 'url' => '/', 'icon' => 'fas fa-home']
        ];

        switch ($route) {
            case 'app_homepage':
                $breadcrumbs[] = ['label' => 'Dashboard', 'active' => true];
                break;
            default:
                $breadcrumbs[] = ['label' => 'Page', 'active' => true];
        }

        return $breadcrumbs;
    }

    private function getMenuItems(): array
    {
        return [
            [
                'label' => 'Dashboard',
                'route' => 'dashboard',
                'icon' => 'fas fa-home',
                'url' => $this->urlGenerator->generate('dashboard')
            ],
            [
                'label' => 'Users',
                'route' => 'admin_users',
                'icon' => 'fas fa-users',
                'url' => $this->urlGenerator->generate('admin_users')
            ],
            [
                'label' => 'Blocks',
                'route' => 'admin_blocks',
                'icon' => 'fas fa-th-large',
                'url' => $this->urlGenerator->generate('admin_blocks')
            ],
            [
                'label' => 'Modules',
                'route' => 'admin_modules',
                'icon' => 'fas fa-puzzle-piece',
                'url' => $this->urlGenerator->generate('admin_modules')
            ],
            [
                'label' => 'Settings',
                'route' => 'app_settings',
                'icon' => 'fas fa-cog',
                'url' => '#'
            ],
            [
                'label' => 'Messages',
                'route' => 'app_messages',
                'icon' => 'fas fa-envelope',
                'url' => '#'
            ]
        ];
    }

    protected function onInstall(): void
    {

    }

    protected function onEnable(): void
    {

    }

    public function getConfigurationSchema(): array
    {
        return [
            'show_user_menu' => [
                'type' => 'checkbox',
                'label' => 'Show User Menu',
                'default' => true,
                'description' => 'Display user menu in the topbar'
            ],
            'show_breadcrumbs' => [
                'type' => 'checkbox',
                'label' => 'Show Breadcrumbs',
                'default' => true,
                'description' => 'Display breadcrumb navigation'
            ],
            'menu_style' => [
                'type' => 'select',
                'label' => 'Menu Style',
                'options' => [
                    'default' => 'Default',
                    'compact' => 'Compact',
                    'minimal' => 'Minimal'
                ],
                'default' => 'default'
            ]
        ];
    }
}
