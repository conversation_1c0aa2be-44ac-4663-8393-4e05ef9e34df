<?php

namespace App\Module;

use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag(ModuleInterface::class)]
interface ModuleInterface
{

    public function getId(): string;

    public function getName(): string;

    public function getDescription(): string;

    public function getVersion(): string;

    public function getDependencies(): array;

    public function getModulePath(): ?string;

    public function getModuleNamespace(): ?string;

    public function isEnabled(): bool;

    public function isInstalled(): bool;

    public function install(): void;

    public function uninstall(): void;

    public function enable(): void;

    public function disable(): void;

    public function getConfigurationSchema(): array;

    public function getConfiguration(): array;

    public function setConfiguration(array $config): void;

    public function getHooks(): array;

    public function executeHook(string $hookName, array $context): mixed;
}
