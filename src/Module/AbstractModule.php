<?php

namespace App\Module;

abstract class AbstractModule implements ModuleInterface {

    protected bool $enabled = false;
    protected bool $installed = false;
    protected array $configuration = [];
    protected ?string $modulePath = null;
    protected ?string $moduleNamespace = null;

    public function isEnabled(): bool
    {
        return $this->enabled && $this->installed;
    }

    public function isInstalled(): bool
    {
        return $this->installed;
    }

    public function install(): void
    {
        $this->onInstall();
        $this->installed = true;
    }

    public function uninstall(): void
    {
        $this->onUninstall();
        $this->installed = false;
        $this->enabled = false;
    }

    public function enable(): void
    {
        if (!$this->installed) {
            throw new \RuntimeException('Cannot enable module that is not installed');
        }

        $this->onEnable();
        $this->enabled = true;
    }

    public function disable(): void
    {
        $this->onDisable();
        $this->enabled = false;
    }

    public function getConfiguration(): array
    {
        return $this->configuration;
    }

    public function setConfiguration(array $config): void
    {
        $this->configuration = array_merge($this->configuration, $config);
        $this->onConfigurationChange($config);
    }

    public function getDependencies(): array
    {
        return [];
    }

    public function getConfigurationSchema(): array
    {
        return [];
    }

    public function getHooks(): array
    {
        return [];
    }

    public function executeHook(string $hookName, array $context): mixed
    {
        if (!$this->isEnabled() || !in_array($hookName, $this->getHooks())) {
            return null;
        }

        $methodName = 'hook_' . str_replace('.', '_', $hookName);

        if (method_exists($this, $methodName)) {
            return $this->$methodName($context);
        }

        return null;
    }

    protected function onInstall(): void
    {

    }

    protected function onUninstall(): void
    {

    }

    protected function onEnable(): void
    {

    }

    protected function onDisable(): void
    {

    }

    public function getModulePath(): ?string
    {
        return $this->modulePath;
    }

    public function setModulePath(?string $modulePath): void
    {
        $this->modulePath = $modulePath;
    }

    public function getModuleNamespace(): ?string
    {
        return $this->moduleNamespace;
    }

    public function setModuleNamespace(?string $moduleNamespace): void
    {
        $this->moduleNamespace = $moduleNamespace;
    }

    protected function onConfigurationChange(array $config): void
    {

    }
}
