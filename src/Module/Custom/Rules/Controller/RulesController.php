<?php

namespace App\Module\Custom\Rules\Controller;

use App\Core\Rules\RuleService;
use App\Engine\Response\PageResponse;
use Doctrine\ORM\EntityManagerInterface;
use SCA\Rules\Domain\Rule\RuleEngine;
use SCA\Rules\Entity\Rule;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/rules')]
class RulesController extends AbstractController {

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly RuleEngine $ruleEngine,
        private readonly RuleService $ruleService,
    ) {}

    #[Route('/list', name: 'admin_rules_list')]
    public function list() {
        return PageResponse::create('@rulesModule/list.html.twig', [
            'rules' => $this->entityManager->getRepository(Rule::class)->findAll(),
            'events' => array_map($this->extractData(...), iterator_to_array($this->ruleEngine->getEvents()), [false]),
            'conditions' => array_map($this->extractData(...), iterator_to_array($this->ruleEngine->getConditions())),
            'actions' => array_map($this->extractData(...), iterator_to_array($this->ruleEngine->getActions())),
        ]);
    }

    private function extractData($option, $settings = true) {
        return [
            'name' => $option->getName(),
            'label' => $option->getLabel(),
            'settings' => $settings ? $option->getAllowedValues() : null,
        ];
    }

    #[Route('/create', name: 'admin_rules_create', methods: ['POST', 'GET'])]
    public function create(Request $request) {
        if ($request->isMethod('POST')) {
            $payload = (string) $request->request->get('payload', '{}');
            $data = json_decode($payload, true, 512, JSON_THROW_ON_ERROR);
            $data['weight'] = (int)($data['weight'] ?? 0);
            $this->ruleService->createRule($data);
            return $this->redirectToRoute('rule_index');
        }

        // OPTIONAL: you can keep GET fallback if someone visits /rules/new directly
        return $this->redirectToRoute('rule_index');
    }

    #[Route('/create', name: 'admin_rules_edit')]
    public function edit() {
//        $this->createFormBuilder();
    }

    #[Route('/delete', name: 'admin_rules_delete')]
    public function delete() {
//        $this->createFormBuilder();
    }
}