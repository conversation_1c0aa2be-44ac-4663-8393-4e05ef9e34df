{# templates/rules/_rule_modal.twig #}
<div class="modal fade" id="ruleModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ruleModalTitle">{{ 't-rule'|trans }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <div id="rule-form-root">
                    <form id="ruleForm" method="post">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">{{ 't-name'|trans }}</label>
                                <input type="text" class="form-control" id="ruleName">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">{{ 't-event'|trans }}</label>
                                <select class="form-select" id="eventSelect"></select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">{{ 't-weight'|trans }}</label>
                                <input type="number" class="form-control" id="ruleWeight" min="0" step="1" value="0">
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5 class="mb-0">{{ 't-conditions'|trans }}</h5>
                            <button type="button" class="btn btn-sm btn-primary" id="addConditionBtn">
                                <i class="fa fa-plus me-2"></i>
                                {{ 't-add'|trans }}</button>
                        </div>
                        <div id="conditionsContainer"></div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5 class="mb-0">{{ 't-actions'|trans }}</h5>
                            <button type="button" class="btn btn-sm btn-primary" id="addActionBtn">
                                <i class="fa fa-plus me-2"></i>
                                {{ 't-add'|trans }}</button>
                        </div>
                        <div id="actionsContainer"></div>

                        <input type="hidden" name="payload" id="payload">

                        <div class="mt-4 d-flex gap-2">
                            <button type="submit" class="btn btn-success">
                                <i class="fa fa-save me-2"></i>
                                {{ 't-save'|trans }}</button>
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                <i class="fa fa-ban me-2"></i>
                                {{ 't-cancel'|trans }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script id="ruleModalTranslations" type="application/json">{{ {modal: {
        title: {
            create: 't-create-rule'|trans,
            edit:'t-edit-rule'|trans
        } },
        common: {
            remove: 't-delete'|trans
        },
        placeholders: {
            chooseEvent: 't-choose-event'|trans,
            chooseAction: 't-choose-action'|trans,
            chooseCondition: 't-choose-condition'|trans
        },
        locale: app.request.locale
    }|json_encode|raw }}</script>
