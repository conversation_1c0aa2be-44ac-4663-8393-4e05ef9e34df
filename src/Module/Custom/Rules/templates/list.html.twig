<div data-controller="rules">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0 text-light">{{ 't-rules'|trans }}</h3>
        <button class="btn btn-primary" data-action="click->rules#createRule" data-create-url="{{ path('admin_rules_create') }}">
            <i class="fa fa-plus me-2"></i>
            {{ 't-new-rule'|trans }}</button>
    </div>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead class="table-light">
                    <tr>
                        <th></th>
                        <th>{{ 't-name'|trans }}</th>
                        <th>{{ 't-event'|trans }}</th>
                        <th>{{ 't-weight'|trans }}</th>
                        <th class="text-end">{{ 't-actions'|trans }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for rule in rules %}
                        {% set ruleView = {
                            id: rule.id, name: rule.name ?? 'NoName', event: rule.event,
                            conditions: rule.conditions, actions: rule.actions, weight: rule.weight ?? 0
                        } %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ rule.name ?? 'NoName' }}</td>
                            <td>
                                <code>{{ rule.event }}</code>
                            </td>
                            <td>{{ rule.weight ?? 0 }}</td>
                            <td class="text-end">
                                <button class="btn btn-sm btn-primary" data-action="click->rules#editRule" data-edit-url="{{ path('rule_edit', {id: rule.id}) }}" data-rule='{{ ruleView|json_encode|e('html_attr') }}'>
                                    <i class="fa fa-pen me-2"></i>
                                    {{ 't-edit'|trans }}</button>
                                <button class="btn btn-sm btn-outline-danger" data-action="click->rules#deleteRule" data-delete-url="{{ path('rule_delete', {id: rule.id}) }}">
                                    <i class="fa fa-trash me-2"></i>
                                    {{ 't-delete'|trans }}</button>
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="4" class="text-center text-muted">{{ 't-no-rules-yet'|trans }}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div data-rules-target="modal">
        {% include '@rulesModule/_rule_modal.html.twig' %}
    </div>

    {# metadata for JS #}


    <script id="events-json" type="application/json">{{ events|json_encode|raw }}</script>
    <script id="conditions-json" type="application/json">{{ conditions|json_encode|raw }}</script>
    <script id="actions-json" type="application/json">{{ actions|json_encode|raw }}</script>

    <script id="ruleModalTranslations" type="application/json" data-rules-target="translations">{{ {modal: {
        title: {
            create: 't-create-rule'|trans,
            edit:'t-edit-rule'|trans
        } },
        common: {
            remove: 't-delete'|trans
        },
        placeholders: {
            chooseEvent: 't-choose-event'|trans,
            chooseAction: 't-choose-action'|trans,
            chooseCondition: 't-choose-condition'|trans
        },
        locale: app.request.locale
    }|json_encode|raw }}</script>
</div>
