<?php

namespace App\Module\Custom\Rules\Form;
use App\Core\Rules\RuleService;
use SCA\Rules\Entity\Rule;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class RuleType extends  AbstractType {

    public function __construct(private readonly RuleService $ruleService) {

    }
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'required' => true,
            ])
            ->add('event', ChoiceType::class, [
                'required' => true,
                'choices' => $this->getEvents(),
            ])
            ->add('conditions', ChoiceType::class, [
                'required' => true,
                'multiple' => true,

                'choices' => $this->getConditions(),
            ])
            ->add('actions', ChoiceType::class, [
                'required' => true,
                'choices' => $this->getActions(),
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Rule::class,
        ]);
    }

    private function getEvents(): array {
        $this->ruleService->ruleEngine->getEvents();
    }

    private function getConditions(): array {
        $this->ruleService->ruleEngine->getConditions();
    }

    private function getActions(): array {
        $this->ruleService->ruleEngine->getActions();
    }
}