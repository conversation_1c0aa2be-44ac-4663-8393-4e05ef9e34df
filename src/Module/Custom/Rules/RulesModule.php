<?php

namespace App\Module\Custom\Rules;

use App\Module\AbstractModule;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class RulesModule extends AbstractModule {

    public function __construct(
        private readonly UrlGeneratorInterface $urlGenerator,
    ) {
    }

    public function getId(): string
    {
        return 'rulesModule';
    }

    public function getName(): string
    {
        return 'Rules Module';
    }

    public function getDescription(): string
    {
        return 'Rules allows create a dynamic action';
    }

    public function getVersion(): string
    {
        return '1.0.0';
    }

    public function getDependencies(): array
    {
        return [];
    }

    public function getHooks(): array
    {
        return ['menu'];
    }

    public function hook_menu($menuItems) {
        $menuItems[] = [
            'label' => 'Rules',
            'route' => 'admin_rules_list',
            'icon' => 'fas fa-puzzle-piece',
            'url' => $this->urlGenerator->generate('admin_rules_list'),
        ];

        return $menuItems;
    }

    public function getConfigurationSchema(): array
    {
        return [
            'show_message' => [
                'type' => 'checkbox',
                'label' => 'Show Footer Message',
                'default' => true,
                'description' => 'Display example message in page footer'
            ],
            'message_text' => [
                'type' => 'text',
                'label' => 'Custom Message',
                'default' => 'Hello from Example Module!',
                'description' => 'Custom message to display'
            ],
            'message_color' => [
                'type' => 'select',
                'label' => 'Message Color',
                'options' => [
                    'primary' => 'Blue',
                    'success' => 'Green',
                    'warning' => 'Yellow',
                    'danger' => 'Red',
                    'info' => 'Light Blue'
                ],
                'default' => 'primary'
            ]
        ];
    }

    protected function onInstall(): void
    {

    }

    protected function onEnable(): void
    {

    }

    protected function onDisable(): void
    {

    }

    protected function onUninstall(): void
    {

    }
}
