<?php

namespace App\Module\Custom\Carrier;

use App\Engine\Hook\RegionComponent;
use App\Module\AbstractModule;
use App\Module\Custom\Carrier\Form\ShipmentForm\ShipmentFormService;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class CarrierModule extends AbstractModule {

    public function __construct(private readonly UrlGeneratorInterface $urlGenerator, private readonly CarrierUtil $carrierUtil, private readonly ShipmentFormService $shipmentFormService) {}

    public function getId(): string
    {
        return 'carrierModule';
    }

    public function getName(): string
    {
        return 'Carrier Module';
    }

    public function getDescription(): string
    {
        return 'Carrier module to help us with carrier';
    }

    public function getVersion(): string
    {
        return '1.0.0';
    }

    public function getDependencies(): array
    {
        return [
            'orderModule'
        ];
    }

    public function getHooks(): array
    {
        return ['menu', 'page_content'];
    }

    public function hook_menu($menuItems) {
        $menuItems[] = [
            'label' => 'Carriers',
            'route' => 'admin_carrier_list',
            'icon' => 'fas fa-car',
            'url' => $this->urlGenerator->generate('admin_carrier_list'),
        ];

        return $menuItems;
    }

    public function hook_page_content($context): ?RegionComponent  {
        if('admin_order_view' !== $context['current_route']) {
            return null;
        }
        $order = $context['page_data']['order'];
        $availableCarriers = [];

        foreach ($this->carrierUtil->getAvailableCarriers() as $carrier) {
            if (in_array($carrier->getType(), $availableCarriers)) {
                continue;
            }
            $handler = $this->carrierUtil->getCarrierHandler($carrier->getType());
            $provider = $handler->getCarrierProvider();
            $availableCarriers[$carrier->getType()] = [
                'name' => $carrier->getType(),
                'shipmentForm' => $this->shipmentFormService->createFormByCarrierType($provider, [
                    'order' => $order,
                    'action' => $this->urlGenerator->generate('admin_shipment_create', [
                        'order' => $order->getId(),
                        'carrier' => $carrier->getId(),
                    ])
                ])->createView()];
        }

        return RegionComponent::create('@carrierModule/hook/orderPageContent.html.twig', [
            'availableCarriers' => $availableCarriers
        ]);
    }

    public function getConfigurationSchema(): array
    {
        return [
            'show_message' => [
                'type' => 'checkbox',
                'label' => 'Show Footer Message',
                'default' => true,
                'description' => 'Display example message in page footer'
            ],
            'message_text' => [
                'type' => 'text',
                'label' => 'Custom Message',
                'default' => 'Hello from Example Module!',
                'description' => 'Custom message to display'
            ],
            'message_color' => [
                'type' => 'select',
                'label' => 'Message Color',
                'options' => [
                    'primary' => 'Blue',
                    'success' => 'Green',
                    'warning' => 'Yellow',
                    'danger' => 'Red',
                    'info' => 'Light Blue'
                ],
                'default' => 'primary'
            ]
        ];
    }

    protected function onInstall(): void
    {

    }

    protected function onEnable(): void
    {

    }

    protected function onDisable(): void
    {

    }

    protected function onUninstall(): void
    {

    }
}
