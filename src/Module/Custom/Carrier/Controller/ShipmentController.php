<?php

namespace App\Module\Custom\Carrier\Controller;

use App\Core\Entity\Carrier;
use App\Core\Entity\Order;
use App\Core\Service\Carrier\CarrierService;
use App\Module\Custom\Carrier\CarrierUtil;
use App\Module\Custom\Carrier\Form\ShipmentForm\ShipmentFormFactory;
use App\Module\Custom\Carrier\Form\ShipmentForm\ShipmentFormService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

#[Route('/admin/shipment')]
class ShipmentController extends AbstractController {

    public function __construct(
        private readonly CarrierUtil         $carrierUtil,
        private readonly ShipmentFormService $shipmentFormService,
        private CarrierService $carrierService
    ) {
    }
    #[Route('/create/{order}/{carrier}', name: 'admin_shipment_create', methods: ['POST'])]
    public function create(Request $request, Order $order, Carrier $carrier) {
        $handler = $this->carrierUtil->getCarrierHandler($carrier->getType());
        $form = $this->shipmentFormService->createFormByCarrierType($handler->getCarrierProvider(), ['order' => $order]);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $this->carrierService->createShipment($order, $data, $carrier);
        }

        return $this->redirectToRoute('admin_order_view', ['id' => $order->getId()]);
    }
}