<?php

namespace App\Module\Custom\Carrier\Controller;

use App\Core\Entity\Carrier;
use App\Core\Entity\User;
use App\Engine\Response\PageResponse;
use App\Module\Custom\Carrier\CarrierFactory;
use App\Module\Custom\Carrier\DataTransferObject\CarrierBaseDetails;
use App\Module\Custom\Carrier\Form\CarrierEditType;
use App\Module\Custom\Carrier\Form\CarrierType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Contracts\Translation\TranslatorInterface;

#[Route('/admin/carrier')]
class CarrierController extends AbstractController {

    private const CARRIER_CREATE_STEP_ONE = 'base-elements';
    private const CARRIER_CREATE_STEP_TWO = 'carrier-details';


    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly RequestStack $requestStack,
        private readonly CarrierFactory $carrierFactory,
        private readonly TranslatorInterface $translator
    ) {
    }

    #[Route('/list', name: 'admin_carrier_list')]
    public function list(): PageResponse {
        $carriers = $this->entityManager->getRepository(Carrier::class)->findAll();
        return PageResponse::create('@carrierModule/list.html.twig', [
            'carriers' => $carriers
        ]);
    }

    #[Route(path: '/create/{step}', name: 'admin_carrier_create', methods: ['POST', 'GET'])]
    public function create(Request $request, #[CurrentUser] User $currentUser, string $step = self::CARRIER_CREATE_STEP_ONE): PageResponse|Response
    {
        $form = match ($step) {
            self::CARRIER_CREATE_STEP_ONE => $this->renderCarrierCreateFromStepOne(),
            self::CARRIER_CREATE_STEP_TWO => $this->renderCarrierCreateFromStepTwo(),
            default => $this->redirectToRoute('admin_carrier_create', ['step' => self::CARRIER_CREATE_STEP_ONE])
        };

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            return match (true) {
                $step === self::CARRIER_CREATE_STEP_ONE => $this->handleEventFormStepOne($form),
                $step === self::CARRIER_CREATE_STEP_TWO => $this->handleEventFormStepTwo($form),
                default => $this->redirectToRoute('admin_carrier_create', ['step' => self::CARRIER_CREATE_STEP_ONE])
            };
        }

        return PageResponse::create('@carrierModule/create.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/view', name: 'admin_carrier_view', methods: ['POST', 'GET'])]
    public function read(Carrier $carrier, Request $request): PageResponse|Response {
        $form = $this->createForm(CarrierEditType::class, $carrier);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($carrier);
            $this->entityManager->flush();
            $this->addFlash('success', $this->translator->trans('carrier.update.success'));
            return $this->redirectToRoute('admin_carrier_view', ['id' => $carrier->getId()]);
        }
        return PageResponse::create('@carrierModule/view.html.twig', [
            'carrier' => $carrier,
            'form' => $form->createView()
        ]);
    }

    #[Route('/{id}/delete', name: 'admin_carrier_delete', methods: ['POST'])]
    public function delete(Request $request, Carrier $carrier): Response {
        if ($this->isCsrfTokenValid('delete'.$carrier->getId(), $request->getPayload()->getString('_token'))) {
            $this->entityManager->remove($carrier);
            $this->entityManager->flush();
            $this->addFlash('success', $this->translator->trans('carrier.delete.success'));
            return $this->redirectToRoute('admin_carrier_list');
        }
        $this->addFlash('error', $this->translator->trans('carrier.delete.error'));
        return $this->redirectToRoute('admin_carrier_list');
    }

    private function renderCarrierCreateFromStepOne(): FormInterface
    {
        $carrierDetailsDto = $this->requestStack->getSession()->get(self::CARRIER_CREATE_STEP_ONE);

        if (!$carrierDetailsDto instanceof CarrierBaseDetails) {
            $carrierDetailsDto = new CarrierBaseDetails();
        }

        return $this->createForm(CarrierType::class, $carrierDetailsDto);
    }

    private function renderCarrierCreateFromStepTwo(): FormInterface
    {
        $carrierDetailsDto = $this->requestStack->getSession()->get(self::CARRIER_CREATE_STEP_ONE);
        $type = $carrierDetailsDto->getType();
        $carrierType = $type . 'Type';
        $DTOClass = $this->carrierFactory->matchDataTransferObject($carrierType);

        return $this->createForm($this->carrierFactory->matchFormType($carrierType), new $DTOClass());
    }
    private function handleEventFormStepOne(FormInterface $form): Response
    {
        $this->requestStack->getSession()->set(self::CARRIER_CREATE_STEP_ONE, $form->getData());
        return $this->redirectToRoute('admin_carrier_create', ['step' => self::CARRIER_CREATE_STEP_TWO]);
    }

    private function handleEventFormStepTwo(FormInterface $form): Response
    {
        $carrierStepOne = $this->requestStack->getSession()->get(self::CARRIER_CREATE_STEP_ONE);

        $carrier = $this->carrierFactory->createFromDTO(
            carrierBaseDetails: $carrierStepOne,
            stepTwo: $form->getData(),
        );

        $this->entityManager->persist($carrier);
        $this->entityManager->flush();

        $this->requestStack->getSession()->set(self::CARRIER_CREATE_STEP_TWO, null);
        $this->requestStack->getSession()->set(self::CARRIER_CREATE_STEP_ONE, null);

        $this->addFlash('success', $this->translator->trans('carrier.create.success'));

        return $this->redirectToRoute('admin_carrier_list');
    }
}