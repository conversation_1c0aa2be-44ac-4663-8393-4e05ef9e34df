<?php

namespace App\Module\Custom\Carrier\Form;

use App\Core\Service\Carrier\CarrierManager;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;

class CarrierType extends AbstractType {

    public function __construct(private readonly CarrierManager $carrierService) {}
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->add('name', TextType::class, [
            'attr' => ['class' => 'form-control']
        ]);
        $builder->add('type', ChoiceType::class, [
            'choices' => array_map(function($item) {
                return $item['type'];
            }, $this->carrierService->getAvailableCarrierTypes()),
            'choice_label' => function($choice) {
                return $choice;
            },
            'choice_value' => function($choice) {
                return $choice;
            },
            'attr' => ['class' => 'form-control']
        ]);

    }
}