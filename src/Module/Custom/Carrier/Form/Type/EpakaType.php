<?php

namespace App\Module\Custom\Carrier\Form\Type;

use App\Module\Custom\Carrier\DataTransferObject\EpakaDetails;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TelType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\TimeType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class EpakaType extends AbstractType
{
    public function __construct(
        private readonly TranslatorInterface $translator
    ) {
    }
    public function getBlockPrefix(): string
    {
        return 'epaka-carrier-type-form';
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('LOGIN', TextType::class, [
                'label' => $this->translator->trans('api.login'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PASSWORD', PasswordType::class, [
                'label' => $this->translator->trans('api.password'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('API_URL', TextType::class, [
                'label' => $this->translator->trans('api.url'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('NAME', TextType::class, [
                'label' => $this->translator->trans('first.name'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('SURNAME', TextType::class, [
                'label' => $this->translator->trans('last.name'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('COMPANY_NAME', TextType::class, [
                'label' => $this->translator->trans('company.name'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('ADDRESS', TextType::class, [
                'label' => $this->translator->trans('address'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('STREET', TextType::class, [
                'label' => $this->translator->trans('street'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('HOUSE_NUMBER', TextType::class, [
                'label' => $this->translator->trans('house.number'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('APARTMENT_NUMBER', TextType::class, [
                'label' => $this->translator->trans('apartment.number'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('POSTAL_CODE', TextType::class, [
                'label' => $this->translator->trans('postal.code'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('CITY', TextType::class, [
                'label' => $this->translator->trans('city'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('COUNTRY', TextType::class, [
                'label' => $this->translator->trans('country'),
                'required' => true,
                'help' => 'Dwuliterowy Kod ISO kraju',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PHONE', TelType::class, [
                'label' => $this->translator->trans('phone'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('EMAIL', EmailType::class, [
                'label' => $this->translator->trans('email'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_POINT_NUMBER', TextType::class, [
                'label' => $this->translator->trans('pickup.point.number'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_TYPE', ChoiceType::class, [
                'label' => $this->translator->trans('pickup.type'),
                'required' => true,
                'choices' => [
                    'Przyjazd kuriera' => 'COURIER',
                    'Dostarczę przesyłkę samodzielnie do punktu odbioru' => 'SELF',
                ],
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_HOURS_DHL_FROM', TimeType::class, [
                'label' => $this->translator->trans('pickup.hours.dhl.from'),
                'required' => true,
                'help' => 'Format HH:MM',
                'widget' => 'single_text',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_HOURS_DHL_TO', TimeType::class, [
                'label' => $this->translator->trans('pickup.hours.dhl.to'),
                'required' => true,
                'help' => 'Format HH:MM',
                'widget' => 'single_text',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_HOURS_DPD_FROM', TimeType::class, [
                'label' => $this->translator->trans('pickup.hours.dpd.from'),
                'required' => true,
                'help' => 'Format HH:MM',
                'widget' => 'single_text',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_HOURS_DPD_TO', TimeType::class, [
                'label' => $this->translator->trans('pickup.hours.dpd.to'),
                'required' => true,
                'help' => 'Format HH:MM',
                'widget' => 'single_text',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_HOURS_FEDEX_FROM', TimeType::class, [
                'label' => $this->translator->trans('pickup.hours.fedex.from'),
                'required' => true,
                'help' => 'Format HH:MM',
                'widget' => 'single_text',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_HOURS_FEDEX_TO', TimeType::class, [
                'label' => $this->translator->trans('pickup.hours.fedex.to'),
                'required' => true,
                'help' => 'Format HH:MM',
                'widget' => 'single_text',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PRINT_TYPE', ChoiceType::class, [
                'label' => $this->translator->trans('print.type'),
                'required' => true,
                'choices' => [
                    'Etykieta PDF' => 'PDF',
                    'Etykieta PDF (Zebra)' => 'ZEBRA',
                ],
                'attr' => ['class' => 'form-control'],
            ]);

        parent::buildForm($builder, $options);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => EpakaDetails::class,
        ]);
    }
}