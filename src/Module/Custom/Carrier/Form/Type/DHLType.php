<?php

namespace App\Module\Custom\Carrier\Form\Type;

use App\Module\Custom\Carrier\DataTransferObject\DHLDetails;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class DHLType extends AbstractType
{
    public function __construct(
        private readonly TranslatorInterface $translator
    ) {
    }
    public function getBlockPrefix(): string
    {
        return 'dhl-carrier-type-form';
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('DHL24_WSDL_URL', TextType::class, [
                'label' => $this->translator->trans('wsdl.url'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('DHL24_USERNAME', TextType::class, [
                'label' => $this->translator->trans('username'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('DHL24_PASSWORD', PasswordType::class, [
                'label' => $this->translator->trans('password'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ]);

        parent::buildForm($builder, $options);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => DHLDetails::class,
        ]);
    }
}