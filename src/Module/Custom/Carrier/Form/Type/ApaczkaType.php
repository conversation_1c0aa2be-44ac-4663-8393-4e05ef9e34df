<?php

namespace App\Module\Custom\Carrier\Form\Type;

use App\Module\Custom\Carrier\DataTransferObject\ApaczkaDetails;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TelType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\TimeType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class ApaczkaType extends AbstractType
{
    public function __construct(
        private readonly TranslatorInterface $translator
    ) {
    }
    public function getBlockPrefix(): string
    {
        return 'apaczka-carrier-type-form';
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('APP_ID', TextType::class, [
                'label' => $this->translator->trans('app.id'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('APP_SECRET', PasswordType::class, [
                'label' => $this->translator->trans('app.secret'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('API_URL', TextType::class, [
                'label' => $this->translator->trans('api.url'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PRINT_TYPE', ChoiceType::class, [
                'label' => $this->translator->trans('print.type'),
                'required' => true,
                'choices' => [
                    'Standardowa etykieta PDF' => 0,
                    'Etykieta PDF do wydruku na drukarkach Zebra' => 1,
                ],
                'attr' => ['class' => 'form-control'],
            ])
            ->add('COMPANY_NAME', TextType::class, [
                'label' => $this->translator->trans('company.name'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('CONTACT_PERSON', TextType::class, [
                'label' => $this->translator->trans('contact.person'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('ADDRESS', TextType::class, [
                'label' => $this->translator->trans('address'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('POSTAL_CODE', TextType::class, [
                'label' => $this->translator->trans('postal.code'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('CITY', TextType::class, [
                'label' => $this->translator->trans('city'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('COUNTRY', TextType::class, [
                'label' => $this->translator->trans('country'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PHONE', TelType::class, [
                'label' => $this->translator->trans('phone'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('EMAIL', TextType::class, [
                'label' => $this->translator->trans('email'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_TYPE', ChoiceType::class, [
                'label' => $this->translator->trans('pickup.type'),
                'required' => true,
                'choices' => [
                    'Przyjazd kuriera' => 'COURIER',
                    'Dostarczę przesyłkę samodzielnie do punktu odbioru' => 'SELF',
                ],
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_HOURS_FROM', TimeType::class, [
                'label' => $this->translator->trans('pickup.hours.from'),
                'required' => true,
                'widget' => 'single_text',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_HOURS_TO', TimeType::class, [
                'label' => $this->translator->trans('pickup.hours.to'),
                'required' => true,
                'widget' => 'single_text',
                'attr' => ['class' => 'form-control'],
            ]);

        parent::buildForm($builder, $options);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => ApaczkaDetails::class,
        ]);
    }
}