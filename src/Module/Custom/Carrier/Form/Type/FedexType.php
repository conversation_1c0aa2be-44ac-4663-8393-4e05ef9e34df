<?php

namespace App\Module\Custom\Carrier\Form\Type;

use App\Module\Custom\Carrier\DataTransferObject\FedexDetails;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TelType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\TimeType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class FedexType extends AbstractType
{
    public function __construct(
        private readonly TranslatorInterface $translator
    ) {
    }
    public function getBlockPrefix(): string
    {
        return 'fedex-carrier-type-form';
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('API_KEY', TextType::class, [
                'label' => $this->translator->trans('api.key'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('API_ACCOUNT_NO', TextType::class, [
                'label' => $this->translator->trans('api.account.number'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('API_ACCOUNT_METER_NO', TextType::class, [
                'label' => $this->translator->trans('api.meter.number'),
                'required' => false,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('API_SECRET', PasswordType::class, [
                'label' => $this->translator->trans('api.secret'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('API_URL', ChoiceType::class, [
                'label' => $this->translator->trans('api.url'),
                'required' => true,
                'choices' => [
                    'Produkcja' => 'https://apis.fedex.com',
                    'Sandbox' => 'https://apis-sandbox.fedex.com',
                ],
                'attr' => ['class' => 'form-control'],
            ])
            ->add('COMPANY_NAME', TextType::class, [
                'label' => $this->translator->trans('company.name'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('CONTACT_PERSON', TextType::class, [
                'label' => $this->translator->trans('contact.person'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('ADDRESS', TextType::class, [
                'label' => $this->translator->trans('address'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('POSTAL_CODE', TextType::class, [
                'label' => $this->translator->trans('postal.code'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('CITY', TextType::class, [
                'label' => $this->translator->trans('city'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('COUNTRY', TextType::class, [
                'label' => $this->translator->trans('country.english'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PHONE', TelType::class, [
                'label' => $this->translator->trans('phone.international'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('EMAIL', TextType::class, [
                'label' => $this->translator->trans('email'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('EORI_NUMBER', TextType::class, [
                'label' => $this->translator->trans('eori.number'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PAYER_FOR_SHIPPING', ChoiceType::class, [
                'label' => $this->translator->trans('shipping.payer'),
                'required' => true,
                'choices' => [
                    'Nadawca' => 'SENDER',
                    'Odbiorca' => 'RECIPIENT',
                    'Trzecia strona' => 'THIRD_PARTY',
                ],
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_HOURS_FROM', TimeType::class, [
                'label' => $this->translator->trans('pickup.hours.from'),
                'required' => true,
                'widget' => 'single_text',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('PICKUP_HOURS_TO', TimeType::class, [
                'label' => $this->translator->trans('pickup.hours.to'),
                'required' => true,
                'widget' => 'single_text',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('SHIPPING_PURPOSE', ChoiceType::class, [
                'label' => $this->translator->trans('shipping.purpose'),
                'required' => true,
                'choices' => [
                    'Cel handlowy' => 'SOLD',
                    'Prezent' => 'GIFT',
                    'Próbka' => 'SAMPLE',
                    'Rzeczy do użytku własnego' => 'NOT SOLD',
                    'Przedmioty osobiste' => 'PERSONAL_EFFECTS',
                    'Naprawa i zwrot' => 'REPAIR_AND_RETURN',
                ],
                'attr' => ['class' => 'form-control'],
            ])
            ->add('LIMIT_SINGLE_LABEL', ChoiceType::class, [
                'label' => $this->translator->trans('limit.single.label'),
                'required' => true,
                'choices' => [
                    'NIE' => '0',
                    'TAK' => '1',
                ],
                'attr' => ['class' => 'form-control'],
            ])
            ->add('FORWARD_SHIPPING_COSTS', ChoiceType::class, [
                'label' => $this->translator->trans('forward.shipping.costs'),
                'required' => true,
                'choices' => [
                    'NIE' => '0',
                    'TAK' => '1',
                ],
                'attr' => ['class' => 'form-control'],
            ])
            ->add('INCOTERMS', ChoiceType::class, [
                'label' => $this->translator->trans('incoterms'),
                'required' => true,
                'choices' => [
                    '-----WYBIERZ-----' => '0',
                    'FCA' => 'FCA',
                    'CIP' => 'CIP',
                    'CPT' => 'CPT',
                    'EXW' => 'EXW',
                    'DDU' => 'DDU',
                    'DDP' => 'DDP',
                    'DAP' => 'DAP',
                    'DPU' => 'DPU',
                ],
                'attr' => ['class' => 'form-control'],
            ]);

        parent::buildForm($builder, $options);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => FedexDetails::class,
        ]);
    }
}