<?php

namespace App\Module\Custom\Carrier\Form\Type;

use App\Module\Custom\Carrier\DataTransferObject\GLSDetails;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class GLSType extends AbstractType
{
    public function __construct(
        private readonly TranslatorInterface $translator
    ) {
    }
    public function getBlockPrefix(): string
    {
        return 'gls-carrier-type-form';
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('GLS_USERNAME', TextType::class, [
                'label' => $this->translator->trans('username'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('GLS_PASSWORD', PasswordType::class, [
                'label' => $this->translator->trans('password'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('GLS_WSDL_URL', TextType::class, [
                'label' => $this->translator->trans('wsdl.url'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('GLS_SESSIONS_JSON', TextType::class, [
                'label' => $this->translator->trans('sessions.json'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ]);

        parent::buildForm($builder, $options);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => GLSDetails::class,
        ]);
    }
}