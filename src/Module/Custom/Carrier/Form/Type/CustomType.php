<?php

namespace App\Module\Custom\Carrier\Form\Type;

use App\Module\Custom\Carrier\DataTransferObject\CustomDetails;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class CustomType extends AbstractType
{
    public function __construct(
        private readonly TranslatorInterface $translator
    ) {
    }
    public function getBlockPrefix(): string
    {
        return 'custom-carrier-type-form';
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('CUSTOM_NAME', TextType::class, [
                'label' => $this->translator->trans('name'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ]);

        parent::buildForm($builder, $options);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => CustomDetails::class,
        ]);
    }
}