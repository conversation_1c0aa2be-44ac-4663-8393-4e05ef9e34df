<?php

namespace App\Module\Custom\Carrier\Form\Type;

use App\Module\Custom\Carrier\DataTransferObject\InPostDetails;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class InPostType extends AbstractType
{
    public function __construct(
        private readonly TranslatorInterface $translator
    ) {
    }
    public function getBlockPrefix(): string
    {
        return 'inpost-carrier-type-form';
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('INPOST_API_URL', TextType::class, [
                'label' => $this->translator->trans('api.url'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('INPOST_API_KEY', PasswordType::class, [
                'label' => $this->translator->trans('api.key'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('INPOST_API_ORG_ID', TextType::class, [
                'label' => $this->translator->trans('organization.id'),
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ]);

        parent::buildForm($builder, $options);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => InPostDetails::class,
        ]);
    }
}