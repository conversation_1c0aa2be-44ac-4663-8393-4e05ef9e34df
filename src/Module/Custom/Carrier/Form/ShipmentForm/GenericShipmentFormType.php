<?php

namespace App\Module\Custom\Carrier\Form\ShipmentForm;

use App\Core\Entity\Order;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TelType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;

class GenericShipmentFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $additionalInfo = $options['additional_info'] ?? [];
        
        if (empty($additionalInfo)) {
            $this->addBasicFields($builder);
            return;
        }

        foreach ($additionalInfo as $fieldKey => $field) {
            $this->addFieldToForm($builder, $fieldKey, $field);
        }
    }

    private function addFieldToForm(FormBuilderInterface $builder, string $fieldKey, $field): void
    {
        if (!is_array($field)) {
            return;
        }

        if (isset($field['name'])) {
            $this->addStructuredField($builder, $field);
            return;
        }

        if (is_numeric($fieldKey) && isset($field[0])) {
            $this->addArrayField($builder, $fieldKey, $field);
            return;
        }

        $this->addAssociativeArrayField($builder, $fieldKey, $field);
    }

    private function addStructuredField(FormBuilderInterface $builder, array $field): void
    {
        $fieldName = $field['name'];
        $fieldType = $field['type'] ?? 'text';
        $label = $field['label'] ?? ucfirst($fieldName);
        $defaultValue = $field['defaultValue'] ?? null;
        $required = $field['required'] ?? false;

        switch ($fieldType) {
            case 'select':
                $choices = [];
                if (isset($field['options']) && is_array($field['options'])) {
                    foreach ($field['options'] as $option) {
                        if (isset($option['label']) && isset($option['value'])) {
                            $choices[$option['label']] = $option['value'];
                        }
                    }
                }
                
                $builder->add($fieldName, ChoiceType::class, [
                    'label' => $label,
                    'choices' => $choices,
                    'required' => $required,
                    'placeholder' => 'Wybierz opcję',
                    'data' => $defaultValue,
                    'attr' => ['class' => 'form-control']
                ]);
                break;

            case 'file':
                $builder->add($fieldName, FileType::class, [
                    'label' => $label,
                    'required' => $required,
                    'constraints' => [
                        new File([
                            'maxSize' => '10M',
                            'mimeTypes' => [
                                'application/pdf',
                                'image/jpeg',
                                'image/png',
                                'image/gif',
                            ],
                            'mimeTypesMessage' => 'Proszę wybrać prawidłowy plik (PDF, JPEG, PNG, GIF)',
                        ])
                    ],
                    'attr' => ['class' => 'form-control']
                ]);
                break;

            case 'checkbox':
            case 'bool':
                $builder->add($fieldName, CheckboxType::class, [
                    'label' => $label,
                    'required' => false,
                    'data' => (bool)$defaultValue,
                    'attr' => ['class' => 'form-check-input']
                ]);
                break;

            case 'number':
                $builder->add($fieldName, NumberType::class, [
                    'label' => $label,
                    'required' => $required,
                    'data' => $defaultValue,
                    'attr' => ['class' => 'form-control']
                ]);
                break;

            case 'email':
                $builder->add($fieldName, EmailType::class, [
                    'label' => $label,
                    'required' => $required,
                    'data' => $defaultValue,
                    'attr' => ['class' => 'form-control']
                ]);
                break;

            case 'tel':
                $builder->add($fieldName, TelType::class, [
                    'label' => $label,
                    'required' => $required,
                    'data' => $defaultValue,
                    'attr' => ['class' => 'form-control']
                ]);
                break;

            case 'text':
            default:
                $builder->add($fieldName, TextType::class, [
                    'label' => $label,
                    'required' => $required,
                    'data' => $defaultValue,
                    'attr' => ['class' => 'form-control']
                ]);
                break;
        }
    }

    private function addArrayField(FormBuilderInterface $builder, string $fieldKey, array $field): void
    {
        // Jeśli to jest tablica opcji dla select
        $choices = [];
        foreach ($field as $option) {
            if (is_array($option) && isset($option['label']) && isset($option['value'])) {
                $choices[$option['label']] = $option['value'];
            } elseif (is_string($option)) {
                $choices[$option] = $option;
            }
        }

        if (!empty($choices)) {
            $builder->add('field_' . $fieldKey, ChoiceType::class, [
                'label' => 'Opcja ' . ($fieldKey + 1),
                'choices' => $choices,
                'required' => false,
                'placeholder' => 'Wybierz opcję',
                'attr' => ['class' => 'form-control']
            ]);
        }
    }

    private function addAssociativeArrayField(FormBuilderInterface $builder, string $fieldKey, array $field): void
    {
        // Jeśli to jest tablica asocjacyjna, spróbuj utworzyć pole select
        $choices = [];
        foreach ($field as $key => $value) {
            if (is_string($value)) {
                $choices[$value] = $key;
            } elseif (is_array($value) && isset($value['desc'])) {
                $choices[$value['desc']] = $key;
            }
        }

        if (!empty($choices)) {
            $builder->add($fieldKey, ChoiceType::class, [
                'label' => ucfirst(str_replace('_', ' ', $fieldKey)),
                'choices' => $choices,
                'required' => false,
                'placeholder' => 'Wybierz opcję',
                'attr' => ['class' => 'form-control']
            ]);
        }
    }

    private function addBasicFields(FormBuilderInterface $builder): void
    {
        $builder->add('notes', TextType::class, [
            'label' => 'Uwagi',
            'required' => false,
            'attr' => ['class' => 'form-control', 'placeholder' => 'Dodatkowe uwagi do przesyłki']
        ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'additional_info' => [],
            'order' => null,
        ]);

        $resolver->setAllowedTypes('additional_info', 'array');
        $resolver->setAllowedTypes('order', [Order::class, 'null']);
    }

    public function getBlockPrefix(): string
    {
        return 'generic_shipment';
    }
}