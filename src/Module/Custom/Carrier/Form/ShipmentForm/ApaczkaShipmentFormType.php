<?php

namespace App\Module\Custom\Carrier\Form\ShipmentForm;

use App\Core\Entity\Order;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ApaczkaShipmentFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $additionalInfo = $options['additional_info'] ?? [];
        
        if (empty($additionalInfo)) {
            return;
        }

        if (isset($additionalInfo['services'])) {
            $serviceChoices = [];
            foreach ($additionalInfo['services'] as $supplier => $services) {
                foreach ($services as $service) {
                    $serviceChoices[$supplier][$service['name']] = $service['service_id'];
                }
            }
            
            $builder->add('service_id', ChoiceType::class, [
                'label' => 'Usługa',
                'choices' => $serviceChoices,
                'required' => true,
                'placeholder' => 'Wybierz usługę',
                'attr' => ['class' => 'form-control']
            ]);
        }

        if (isset($additionalInfo['options'])) {
            foreach ($additionalInfo['options'] as $optionId => $option) {
                if ($option['type'] === 'bool') {
                    $builder->add('option_' . $optionId, ChoiceType::class, [
                        'label' => $option['name'],
                        'help' => $option['desc'] ?? '',
                        'required' => false,
                        'attr' => ['class' => 'form-check-input']
                    ]);
                }
            }
        }

        if (isset($additionalInfo['package_type'])) {
            $packageChoices = [];
            foreach ($additionalInfo['package_type'] as $key => $package) {
                $packageChoices[$package['desc']] = $key;
            }
            
            $builder->add('package_type', ChoiceType::class, [
                'label' => 'Typ paczki',
                'choices' => $packageChoices,
                'required' => true,
                'placeholder' => 'Wybierz typ paczki',
                'attr' => ['class' => 'form-control']
            ]);
        }

        if (isset($additionalInfo['points_type'])) {
            $pointChoices = [];
            foreach ($additionalInfo['points_type'] as $pointType) {
                $pointChoices[$pointType] = $pointType;
            }
            
            $builder->add('points_type', ChoiceType::class, [
                'label' => 'Typ punktu',
                'choices' => $pointChoices,
                'required' => false,
                'placeholder' => 'Wybierz typ punktu',
                'attr' => ['class' => 'form-control']
            ]);
        }

        if (isset($additionalInfo['pickup_type'])) {
            $pickupChoices = [];
            foreach ($additionalInfo['pickup_type'] as $key => $pickup) {
                $pickupChoices[$pickup['desc']] = $key;
            }
            
            $builder->add('pickup_type', ChoiceType::class, [
                'label' => 'Typ odbioru',
                'choices' => $pickupChoices,
                'required' => true,
                'placeholder' => 'Wybierz typ odbioru',
                'attr' => ['class' => 'form-control']
            ]);
        }

        if (isset($additionalInfo['unit_type'])) {
            $unitChoices = [];
            foreach ($additionalInfo['unit_type'] as $key => $unit) {
                $unitChoices[$unit['desc']] = $key;
            }
            
            $builder->add('unit_type', ChoiceType::class, [
                'label' => 'Typ jednostki',
                'choices' => $unitChoices,
                'required' => false,
                'placeholder' => 'Wybierz typ jednostki',
                'attr' => ['class' => 'form-control']
            ]);
        }

        $builder->add('submit', SubmitType::class, []);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'additional_info' => [],
            'order' => null,
        ]);
        
        $resolver->setAllowedTypes('additional_info', 'array');
        $resolver->setAllowedTypes('order', [Order::class, 'null']);
    }

    public function getBlockPrefix(): string
    {
        return 'apaczka_shipment';
    }
}