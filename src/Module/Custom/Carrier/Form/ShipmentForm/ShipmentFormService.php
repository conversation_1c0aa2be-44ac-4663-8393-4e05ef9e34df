<?php

namespace App\Module\Custom\Carrier\Form\ShipmentForm;

use App\Core\Entity\Carrier;
use App\Core\Service\Carrier\CarrierProviderInterface;
use App\Core\Service\Carrier\CarrierFactory;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Request;

class ShipmentFormService
{
    public function __construct(
        private readonly ShipmentFormFactory $formFactory,
        private readonly CarrierFactory $carrierFactory
    ) {}

    public function createFormForCarrier(Carrier $carrier, array $options = []): FormInterface
    {
        $carrierProvider = $this->carrierFactory->createCarrierProvider($carrier);
        $carrierProvider->setConnectionData($carrier);
        
        return $this->formFactory->createFormForCarrier($carrierProvider, $options);
    }

    public function createFormByCarrierType($carrierProvider, array $options = []): FormInterface
    {
        return $this->formFactory->createFormForCarrier($carrierProvider, $options);
    }

    public function handleFormSubmission(FormInterface $form, Request $request): array
    {
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            return [
                'success' => true,
                'data' => $form->getData(),
                'errors' => []
            ];
        }

        $errors = [];
        if ($form->isSubmitted()) {
            foreach ($form->getErrors(true) as $error) {
                $errors[] = $error->getMessage();
            }
        }

        return [
            'success' => false,
            'data' => null,
            'errors' => $errors
        ];
    }

    public function getAdditionalInfoForCarrier(Carrier $carrier): array
    {
        try {
            $carrierProvider = $this->carrierFactory->createCarrierProvider($carrier);
            $carrierProvider->setConnectionData($carrier);
            
            return $carrierProvider->getAdditionalInfo();
        } catch (\Exception $e) {
            return [];
        }
    }

    public function validateAdditionalSettings(array $additionalSettings, Carrier $carrier): array
    {
        $errors = [];
        $additionalInfo = $this->getAdditionalInfoForCarrier($carrier);
        
        // Walidacja na podstawie struktury additional info
        foreach ($additionalInfo as $fieldKey => $field) {
            if (!is_array($field) || !isset($field['name'])) {
                continue;
            }

            $fieldName = $field['name'];
            $required = $field['required'] ?? false;
            
            if ($required && (!isset($additionalSettings[$fieldName]) || empty($additionalSettings[$fieldName]))) {
                $errors[$fieldName] = sprintf('Pole "%s" jest wymagane', $field['label'] ?? $fieldName);
            }

            // Walidacja dla pól select
            if (isset($field['type']) && $field['type'] === 'select' && isset($additionalSettings[$fieldName])) {
                $validValues = array_column($field['options'] ?? [], 'value');
                if (!empty($validValues) && !in_array($additionalSettings[$fieldName], $validValues)) {
                    $errors[$fieldName] = sprintf('Nieprawidłowa wartość dla pola "%s"', $field['label'] ?? $fieldName);
                }
            }
        }

        return $errors;
    }

    public function getSupportedCarriers(): array
    {
        return $this->formFactory->getSupportedCarriers();
    }

    public function hasSpecificFormForCarrier(string $carrierName): bool
    {
        return $this->formFactory->hasSpecificFormForCarrier($carrierName);
    }
}