<?php

namespace App\Module\Custom\Carrier\Form\ShipmentForm;

use App\Core\Entity\Order;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;

class FedexShipmentFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $additionalInfo = $options['additional_info'] ?? [];
        
        if (empty($additionalInfo)) {
            return;
        }

        foreach ($additionalInfo as $field) {
            if (!is_array($field) || !isset($field['name'])) {
                continue;
            }

            $fieldName = $field['name'];
            $fieldType = $field['type'] ?? 'text';
            $label = $field['label'] ?? ucfirst($fieldName);
            $defaultValue = $field['defaultValue'] ?? null;
            $required = $field['required'] ?? false;

            switch ($fieldType) {
                case 'select':
                    $choices = [];
                    if (isset($field['options']) && is_array($field['options'])) {
                        foreach ($field['options'] as $option) {
                            if (isset($option['label']) && isset($option['value'])) {
                                $choices[$option['label']] = $option['value'];
                            }
                        }
                    }
                    
                    $builder->add($fieldName, ChoiceType::class, [
                        'label' => $label,
                        'choices' => $choices,
                        'required' => $required,
                        'placeholder' => 'Wybierz opcję',
                        'data' => $defaultValue,
                        'attr' => ['class' => 'form-control']
                    ]);
                    break;

                case 'file':
                    $builder->add($fieldName, FileType::class, [
                        'label' => $label,
                        'required' => $required,
                        'constraints' => [
                            new File([
                                'maxSize' => '10M',
                                'mimeTypes' => [
                                    'application/pdf',
                                    'image/jpeg',
                                    'image/png',
                                    'image/gif',
                                ],
                                'mimeTypesMessage' => 'Proszę wybrać prawidłowy plik (PDF, JPEG, PNG, GIF)',
                            ])
                        ],
                        'attr' => ['class' => 'form-control']
                    ]);
                    break;

                case 'text':
                default:
                    $builder->add($fieldName, TextType::class, [
                        'label' => $label,
                        'required' => $required,
                        'data' => $defaultValue,
                        'attr' => ['class' => 'form-control']
                    ]);
                    break;
            }
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'additional_info' => [],
            'order' => null,
        ]);

        $resolver->setAllowedTypes('additional_info', 'array');
        $resolver->setAllowedTypes('order', [Order::class, 'null']);
    }

    public function getBlockPrefix(): string
    {
        return 'fedex_shipment';
    }
}