<?php

namespace App\Module\Custom\Carrier\Form\ShipmentForm;

use App\Core\Service\Carrier\CarrierProviderInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormInterface;

class ShipmentFormFactory
{
    private const FORM_TYPE_MAPPING = [
        'apaczka' => ApaczkaShipmentFormType::class,
        'fedex' => FedexShipmentFormType::class,
        'epaka' => EpakaShipmentFormType::class,
        'generic' => GenericShipmentFormType::class,
    ];

    public function __construct(
        private readonly FormFactoryInterface $formFactory
    ) {}

    public function createFormForCarrier(
        CarrierProviderInterface $carrierProvider,
        array $options = []
    ): FormInterface {
        $additionalInfo = $carrierProvider->getAdditionalInfo($options);
        $formType = $this->determineFormType($carrierProvider, $additionalInfo);
        
        $formOptions = array_merge([
            'additional_info' => $additionalInfo,
        ], $options);

        return $this->formFactory->create($formType, null, $formOptions);
    }

    public function createFormByCarrierName(
        string $carrierName,
        array $additionalInfo = [],
        array $options = []
    ): FormInterface {
        $formType = $this->getFormTypeByCarrierName($carrierName);
        
        $formOptions = array_merge([
            'additional_info' => $additionalInfo,
        ], $options);

        return $this->formFactory->create($formType, null, $formOptions);
    }

    private function determineFormType(
        CarrierProviderInterface $carrierProvider,
        array $additionalInfo
    ): string {
        $carrierClass = get_class($carrierProvider);
        $carrierName = $this->extractCarrierNameFromClass($carrierClass);
        
        return $this->getFormTypeByCarrierName($carrierName);
    }

    public function getFormTypeByCarrierName(string $carrierName): string
    {
        $carrierName = strtolower($carrierName);
        
        if (isset(self::FORM_TYPE_MAPPING[$carrierName])) {
            return self::FORM_TYPE_MAPPING[$carrierName];
        }

        foreach (array_keys(self::FORM_TYPE_MAPPING) as $mappedName) {
            if ($mappedName === 'generic') {
                continue;
            }
            
            if (str_contains($carrierName, $mappedName) || str_contains($mappedName, $carrierName)) {
                return self::FORM_TYPE_MAPPING[$mappedName];
            }
        }

        return self::FORM_TYPE_MAPPING['generic'];
    }

    private function extractCarrierNameFromClass(string $className): string
    {
        $shortClassName = substr($className, strrpos($className, '\\') + 1);
        
        if (str_ends_with($shortClassName, 'Service')) {
            $shortClassName = substr($shortClassName, 0, -7);
        }

        return $shortClassName;
    }

    public function getSupportedCarriers(): array
    {
        return array_keys(self::FORM_TYPE_MAPPING);
    }

    public function hasSpecificFormForCarrier(string $carrierName): bool
    {
        $carrierName = strtolower($carrierName);
        return isset(self::FORM_TYPE_MAPPING[$carrierName]) && 
               self::FORM_TYPE_MAPPING[$carrierName] !== GenericShipmentFormType::class;
    }
}