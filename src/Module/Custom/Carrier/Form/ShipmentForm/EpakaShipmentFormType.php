<?php

namespace App\Module\Custom\Carrier\Form\ShipmentForm;

use App\Core\Entity\Order;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class EpakaShipmentFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $additionalInfo = $options['additional_info'] ?? [];
        
        if (empty($additionalInfo)) {
            return;
        }

        foreach ($additionalInfo as $field) {
            if (!is_array($field) || !isset($field['name']) || empty($field)) {
                continue;
            }

            $fieldName = $field['name'];
            $fieldType = $field['type'] ?? 'text';
            $label = $field['label'] ?? ucfirst($fieldName);
            $defaultValue = $field['defaultValue'] ?? null;
            $required = $field['required'] ?? false;

            switch ($fieldType) {
                case 'select':
                    $choices = [];
                    if (isset($field['options']) && is_array($field['options'])) {
                        foreach ($field['options'] as $option) {
                            if (isset($option['label']) && isset($option['value'])) {
                                $choices[$option['label']] = $option['value'];
                            }
                        }
                    }
                    
                    $builder->add($fieldName, ChoiceType::class, [
                        'label' => $label,
                        'choices' => $choices,
                        'required' => $required,
                        'placeholder' => 'Wybierz opcję',
                        'data' => $defaultValue,
                        'attr' => ['class' => 'form-control']
                    ]);
                    break;

                case 'text':
                default:
                    $builder->add($fieldName, TextType::class, [
                        'label' => $label,
                        'required' => $required,
                        'data' => $defaultValue,
                        'attr' => ['class' => 'form-control']
                    ]);
                    break;
            }
        }

        if (isset($additionalInfo['services']) || method_exists($this, 'addEpakaServiceFields')) {
            $this->addEpakaServiceFields($builder, $additionalInfo);
        }
    }

    private function addEpakaServiceFields(FormBuilderInterface $builder, array $additionalInfo): void
    {
        if (isset($additionalInfo['services'])) {
            $courierChoices = [];
            foreach ($additionalInfo['services'] as $service) {
                if (isset($service['id']) && isset($service['name'])) {
                    $courierChoices[$service['name']] = $service['id'];
                }
            }
            
            if (!empty($courierChoices)) {
                $builder->add('courierId', ChoiceType::class, [
                    'label' => 'Kurier',
                    'choices' => $courierChoices,
                    'required' => true,
                    'placeholder' => 'Wybierz kuriera',
                    'attr' => ['class' => 'form-control']
                ]);
            }
        }

        $builder->add('shipmentTypeCode', ChoiceType::class, [
            'label' => 'Typ przesyłki',
            'choices' => [
                'Paczka' => 'paczka',
                'Paleta' => 'paleta',
                'Koperta' => 'koperta',
            ],
            'required' => true,
            'placeholder' => 'Wybierz typ przesyłki',
            'attr' => ['class' => 'form-control']
        ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'additional_info' => [],
            'order' => null,
        ]);

        $resolver->setAllowedTypes('additional_info', 'array');
        $resolver->setAllowedTypes('order', [Order::class, 'null']);
    }

    public function getBlockPrefix(): string
    {
        return 'epaka_shipment';
    }
}