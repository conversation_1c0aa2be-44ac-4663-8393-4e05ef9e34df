<?php

namespace App\Module\Custom\Carrier\Form;

use App\Core\Entity\Carrier;
use App\Module\Custom\Carrier\CarrierFactory;
use App\Module\Custom\Carrier\DataTransferObject\CarrierBaseDetails;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CarrierEditType extends AbstractType {

    public function __construct(private readonly CarrierFactory $carrierFactory) {}

    public function getBlockPrefix(): string {
        return 'carrier-edit-form';
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void {
        $builder->add('name', TextType::class, [
            'attr' => ['class' => 'form-control']
        ]);

        if ($options['data']) {
            $carrier = $options['data'];
            $carrierFormType = $this->carrierFactory->matchFormType($carrier->getType() . 'Type');
            $DTOClass = $this->carrierFactory->matchDataTransferObject($carrier->getType() . 'Type');

            $DTO = $this->carrierFactory->mapSettingsToDTO($carrier->getCarrierSettings(), new $DTOClass());
            $builder->add('carrierSettings', $carrierFormType, [
                'data' => $DTO,
                'mapped' => false,
            ]);
        }

        // Dodaj event listener do obsługi zapisywania danych z dynamicznego formularza
        $builder->addEventListener(FormEvents::POST_SUBMIT, [$this, 'onPostSubmit']);
    }

    public function onPostSubmit(FormEvent $event): void {
        $form = $event->getForm();
        $carrier = $form->getData();

        if (!$carrier instanceof Carrier) {
            return;
        }

        if ($form->has('carrierSettings') && $form->get('carrierSettings')->isSubmitted()) {
            $carrierSettingsData = $form->get('carrierSettings')->getData();

            if ($carrierSettingsData) {
                foreach ($carrier->getCarrierSettings() as $setting) {
                    $carrier->removeCarrierSetting($setting);
                }

                $newSettingsArray = $this->carrierFactory->mapDTOToSettingsArray($carrierSettingsData);
                foreach ($this->carrierFactory->parseArrayOfSettingsToObjects($newSettingsArray) as $newSetting) {
                    $carrier->addCarrierSetting($newSetting);
                }
            }
        }
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults([
            'data_class' => Carrier::class,
        ]);
    }
}