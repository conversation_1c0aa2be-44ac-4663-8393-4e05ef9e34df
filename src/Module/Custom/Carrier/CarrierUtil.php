<?php

namespace App\Module\Custom\Carrier;
use App\Core\Entity\Carrier;
use Doctrine\ORM\EntityManagerInterface;
use App\Core\Service\Carrier\CarrierManager as coreCarrierManager;
readonly class CarrierUtil {

    public function __construct(
        private EntityManagerInterface $entityManager,
        private coreCarrierManager $carrierFactory,
    ) {}

    public function getAvailableCarriers(): array {
        return $this->entityManager->getRepository(Carrier::class)->findAll();
    }

    public function getAvailableCarrierTypes(): array {
        return $this->carrierFactory->getAvailableCarrierTypes();
    }

    public function getCarriersByType($type): array {
        return $this->carrierFactory->getAllCarriersByType($type);
    }

    public function getCarrierHandler($type) {
        return $this->carrierFactory->getCarrierHandler($type);
    }
}