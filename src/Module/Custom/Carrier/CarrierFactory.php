<?php

namespace App\Module\Custom\Carrier;

use App\Core\Entity\Carrier;
use App\Core\Entity\CarrierSettings;
use App\Module\Custom\Carrier\DataTransferObject\ApaczkaDetails;
use App\Module\Custom\Carrier\DataTransferObject\CarrierBaseDetails;
use App\Module\Custom\Carrier\DataTransferObject\CustomDetails;
use App\Module\Custom\Carrier\DataTransferObject\DHLDetails;
use App\Module\Custom\Carrier\DataTransferObject\EpakaDetails;
use App\Module\Custom\Carrier\DataTransferObject\FedexDetails;
use App\Module\Custom\Carrier\DataTransferObject\GLSDetails;
use App\Module\Custom\Carrier\DataTransferObject\InPostDetails;
use App\Module\Custom\Carrier\DataTransferObject\StepTwoInterface;
use App\Module\Custom\Carrier\Form\Type\ApaczkaType;
use App\Module\Custom\Carrier\Form\Type\CustomType;
use App\Module\Custom\Carrier\Form\Type\DHLType;
use App\Module\Custom\Carrier\Form\Type\EpakaType;
use App\Module\Custom\Carrier\Form\Type\FedexType;
use App\Module\Custom\Carrier\Form\Type\GLSType;
use App\Module\Custom\Carrier\Form\Type\InPostType;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\PropertyAccess\PropertyAccess;
use Symfony\Component\PropertyAccess\PropertyAccessor;
use Symfony\Component\PropertyInfo\PropertyInfoExtractorInterface;

class CarrierFactory {

    public function __construct(
        private PropertyInfoExtractorInterface $propertyInfoExtractor
    ) {}
    public function createFromDTO(CarrierBaseDetails $carrierBaseDetails, StepTwoInterface $stepTwo): Carrier {

        $carrier = new Carrier();
        $carrier->setName($carrierBaseDetails->getName());
        $carrier->setType($carrierBaseDetails->getType());
        $reflectedClass = new \ReflectionClass(get_class($stepTwo));
        $propertyAccessor = PropertyAccess::createPropertyAccessor();
        foreach ($reflectedClass->getProperties() as $property) {
            $value = $propertyAccessor->getValue($stepTwo, $property->getName());
            $carrierSetting = new CarrierSettings();
            $carrierSetting->setName($property->getName());
            if ($value instanceof \DateTimeInterface) {
                $value = $value->format('H:i');
            }
            $carrierSetting->setValue($value);
            $carrier->addCarrierSetting($carrierSetting);
        }

        return $carrier;
    }

    public function mapSettingsToDTO(Collection $carrierSettings, StepTwoInterface $DTO): StepTwoInterface {
        $propertyAccessor = PropertyAccess::createPropertyAccessor();
        foreach ($carrierSettings as $setting) {
            $type = $this->propertyInfoExtractor->getType(get_class($DTO), $setting->getName());
            if ($type->accepts($setting->getValue())) {
                $propertyAccessor->setValue($DTO, $setting->getName(), $setting->getValue());
            } else {
                match($type->getWrappedType()->getTypeIdentifier()->value) {
                    'int' => $propertyAccessor->setValue($DTO, $setting->getName(), (int) $setting->getValue()),
                    'string' => $propertyAccessor->setValue($DTO, $setting->getName(), (string) $setting->getValue()),
                    'bool' => $propertyAccessor->setValue($DTO, $setting->getName(), (bool) $setting->getValue()),
                    'object' => $this->handleMatchObjectType($DTO, $type, $setting, $propertyAccessor),
                };
            }
        }

        return $DTO;
    }

    public function mapDTOToSettingsArray(StepTwoInterface $DTO): array {
        $propertyAccessor = PropertyAccess::createPropertyAccessor();
        $reflectedClass = new \ReflectionClass(get_class($DTO));
        $settings = [];
        foreach ($reflectedClass->getProperties() as $property) {
            $settings[] = [
                'name' => $property->getName(),
                'value' => $propertyAccessor->getValue($DTO, $property->getName())
            ];
        }

        return $settings;
    }

    public function parseArrayOfSettingsToObjects(array $carrierSettings): array {
        $settings = [];
        $propertyAccessor = PropertyAccess::createPropertyAccessor();
        foreach ($carrierSettings as $setting) {
            $newSetting = new CarrierSettings();
            $propertyAccessor->setValue($newSetting, 'name', $setting['name']);
            if ($setting['value'] instanceof \DateTimeInterface) {
                $setting['value'] = $setting['value']->format('H:i');
            }
            $propertyAccessor->setValue($newSetting, 'value', $setting['value']);
            $settings[] = $newSetting;
        }

        return $settings;
    }

    public function handleMatchObjectType(StepTwoInterface &$DTO, $type, CarrierSettings $setting, PropertyAccessor $propertyAccessor): void {
        if ('DateTime' === $type->getWrappedType()->getClassName()) {
            $propertyAccessor->setValue($DTO, $setting->getName(), new \DateTime($setting->getValue()));
        }
    }

    public function matchFormType(string $type): string {
        return match($type) {
            'ApaczkaType' => ApaczkaType::class,
            'CustomType' => CustomType::class,
            'DHLType' => DHLType::class,
            'EpakaType' => EpakaType::class,
            'FedexType' => FedexType::class,
            'GLSType' => GLSType::class,
            'InPostType' => InPostType::class,
        };
    }
    public function matchDataTransferObject(string $type): string {
        return match($type) {
            'ApaczkaType' => ApaczkaDetails::class,
            'CustomType' => CustomDetails::class,
            'DHLType' => DHLDetails::class,
            'EpakaType' => EpakaDetails::class,
            'FedexType' => FedexDetails::class,
            'GLSType' => GLSDetails::class,
            'InPostType' => InPostDetails::class,
        };
    }
}