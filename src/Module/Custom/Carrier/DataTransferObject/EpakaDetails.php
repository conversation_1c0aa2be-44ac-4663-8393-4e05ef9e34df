<?php

namespace App\Module\Custom\Carrier\DataTransferObject;

class EpakaDetails implements StepTwoInterface
{
    private ?string $LOGIN = null;
    private ?string $PASSWORD = null;
    private ?string $API_URL = null;
    private ?string $NAME = null;
    private ?string $SURNAME = null;
    private ?string $COMPANY_NAME = null;
    private ?string $ADDRESS = null;
    private ?string $STREET = null;
    private ?string $HOUSE_NUMBER = null;
    private ?string $APARTMENT_NUMBER = null;
    private ?string $POSTAL_CODE = null;
    private ?string $CITY = null;
    private ?string $COUNTRY = null;
    private ?string $PHONE = null;
    private ?string $EMAIL = null;
    private ?string $PICKUP_POINT_NUMBER = null;
    private ?string $PICKUP_TYPE = null;
    private ?\DateTime $PICKUP_HOURS_DHL_FROM = null;
    private ?\DateTime $PICKUP_HOURS_DHL_TO = null;
    private ?\DateTime $PICKUP_HOURS_DPD_FROM = null;
    private ?\DateTime $PICKUP_HOURS_DPD_TO = null;
    private ?\DateTime $PICKUP_HOURS_FEDEX_FROM = null;
    private ?\DateTime $PICKUP_HOURS_FEDEX_TO = null;
    private ?string $PRINT_TYPE = null;

    public function __construct() {}

    public function getLogin(): ?string
    {
        return $this->LOGIN;
    }

    public function setLogin(?string $LOGIN): void
    {
        $this->LOGIN = $LOGIN;
    }

    public function getPassword(): ?string
    {
        return $this->PASSWORD;
    }

    public function setPassword(?string $PASSWORD): void
    {
        $this->PASSWORD = $PASSWORD;
    }

    public function getApiUrl(): ?string
    {
        return $this->API_URL;
    }

    public function setApiUrl(?string $API_URL): void
    {
        $this->API_URL = $API_URL;
    }

    public function getName(): ?string
    {
        return $this->NAME;
    }

    public function setName(?string $NAME): void
    {
        $this->NAME = $NAME;
    }

    public function getSurname(): ?string
    {
        return $this->SURNAME;
    }

    public function setSurname(?string $SURNAME): void
    {
        $this->SURNAME = $SURNAME;
    }

    public function getCompanyName(): ?string
    {
        return $this->COMPANY_NAME;
    }

    public function setCompanyName(?string $COMPANY_NAME): void
    {
        $this->COMPANY_NAME = $COMPANY_NAME;
    }

    public function getAddress(): ?string
    {
        return $this->ADDRESS;
    }

    public function setAddress(?string $ADDRESS): void
    {
        $this->ADDRESS = $ADDRESS;
    }

    public function getStreet(): ?string
    {
        return $this->STREET;
    }

    public function setStreet(?string $STREET): void
    {
        $this->STREET = $STREET;
    }

    public function getHouseNumber(): ?string
    {
        return $this->HOUSE_NUMBER;
    }

    public function setHouseNumber(?string $HOUSE_NUMBER): void
    {
        $this->HOUSE_NUMBER = $HOUSE_NUMBER;
    }

    public function getApartmentNumber(): ?string
    {
        return $this->APARTMENT_NUMBER;
    }

    public function setApartmentNumber(?string $APARTMENT_NUMBER): void
    {
        $this->APARTMENT_NUMBER = $APARTMENT_NUMBER;
    }

    public function getPostalCode(): ?string
    {
        return $this->POSTAL_CODE;
    }

    public function setPostalCode(?string $POSTAL_CODE): void
    {
        $this->POSTAL_CODE = $POSTAL_CODE;
    }

    public function getCity(): ?string
    {
        return $this->CITY;
    }

    public function setCity(?string $CITY): void
    {
        $this->CITY = $CITY;
    }

    public function getCountry(): ?string
    {
        return $this->COUNTRY;
    }

    public function setCountry(?string $COUNTRY): void
    {
        $this->COUNTRY = $COUNTRY;
    }

    public function getPhone(): ?string
    {
        return $this->PHONE;
    }

    public function setPhone(?string $PHONE): void
    {
        $this->PHONE = $PHONE;
    }

    public function getEmail(): ?string
    {
        return $this->EMAIL;
    }

    public function setEmail(?string $EMAIL): void
    {
        $this->EMAIL = $EMAIL;
    }

    public function getPickupPointNumber(): ?string
    {
        return $this->PICKUP_POINT_NUMBER;
    }

    public function setPickupPointNumber(?string $PICKUP_POINT_NUMBER): void
    {
        $this->PICKUP_POINT_NUMBER = $PICKUP_POINT_NUMBER;
    }

    public function getPickupType(): ?string
    {
        return $this->PICKUP_TYPE;
    }

    public function setPickupType(?string $PICKUP_TYPE): void
    {
        $this->PICKUP_TYPE = $PICKUP_TYPE;
    }

    public function getPickupHoursDhlFrom(): ?\DateTime
    {
        return $this->PICKUP_HOURS_DHL_FROM;
    }

    public function setPickupHoursDhlFrom(?\DateTime $PICKUP_HOURS_DHL_FROM): void
    {
        $this->PICKUP_HOURS_DHL_FROM = $PICKUP_HOURS_DHL_FROM;
    }

    public function getPickupHoursDhlTo(): ?\DateTime
    {
        return $this->PICKUP_HOURS_DHL_TO;
    }

    public function setPickupHoursDhlTo(?\DateTime $PICKUP_HOURS_DHL_TO): void
    {
        $this->PICKUP_HOURS_DHL_TO = $PICKUP_HOURS_DHL_TO;
    }

    public function getPickupHoursDpdFrom(): ?\DateTime
    {
        return $this->PICKUP_HOURS_DPD_FROM;
    }

    public function setPickupHoursDpdFrom(?\DateTime $PICKUP_HOURS_DPD_FROM): void
    {
        $this->PICKUP_HOURS_DPD_FROM = $PICKUP_HOURS_DPD_FROM;
    }

    public function getPickupHoursDpdTo(): ?\DateTime
    {
        return $this->PICKUP_HOURS_DPD_TO;
    }

    public function setPickupHoursDpdTo(?\DateTime $PICKUP_HOURS_DPD_TO): void
    {
        $this->PICKUP_HOURS_DPD_TO = $PICKUP_HOURS_DPD_TO;
    }

    public function getPickupHoursFedexFrom(): ?\DateTime
    {
        return $this->PICKUP_HOURS_FEDEX_FROM;
    }

    public function setPickupHoursFedexFrom(?\DateTime $PICKUP_HOURS_FEDEX_FROM): void
    {
        $this->PICKUP_HOURS_FEDEX_FROM = $PICKUP_HOURS_FEDEX_FROM;
    }

    public function getPickupHoursFedexTo(): ?\DateTime
    {
        return $this->PICKUP_HOURS_FEDEX_TO;
    }

    public function setPickupHoursFedexTo(?\DateTime $PICKUP_HOURS_FEDEX_TO): void
    {
        $this->PICKUP_HOURS_FEDEX_TO = $PICKUP_HOURS_FEDEX_TO;
    }

    public function getPrintType(): ?string
    {
        return $this->PRINT_TYPE;
    }

    public function setPrintType(?string $PRINT_TYPE): void
    {
        $this->PRINT_TYPE = $PRINT_TYPE;
    }
}