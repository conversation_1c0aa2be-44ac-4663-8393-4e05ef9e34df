<?php

namespace App\Module\Custom\Carrier\DataTransferObject;

class FedexDetails implements StepTwoInterface
{
    private ?string $API_KEY = null;
    private ?string $API_ACCOUNT_NO = null;
    private ?string $API_ACCOUNT_METER_NO = null;
    private ?string $API_SECRET = null;
    private ?string $API_URL = null;
    private ?string $COMPANY_NAME = null;
    private ?string $CONTACT_PERSON = null;
    private ?string $ADDRESS = null;
    private ?string $POSTAL_CODE = null;
    private ?string $CITY = null;
    private ?string $COUNTRY = null;
    private ?string $PHONE = null;
    private ?string $EMAIL = null;
    private ?string $EORI_NUMBER = null;
    private ?string $PAYER_FOR_SHIPPING = null;
    private ?\DateTime $PICKUP_HOURS_FROM = null;
    private ?\DateTime $PICKUP_HOURS_TO = null;
    private ?string $SHIPPING_PURPOSE = null;
    private ?string $LIMIT_SINGLE_LABEL = null;
    private ?string $FORWARD_SHIPPING_COSTS = null;
    private ?string $INCOTERMS = null;

    public function __construct() {}

    public function getApiKey(): ?string
    {
        return $this->API_KEY;
    }

    public function setApiKey(?string $API_KEY): void
    {
        $this->API_KEY = $API_KEY;
    }

    public function getApiAccountNo(): ?string
    {
        return $this->API_ACCOUNT_NO;
    }

    public function setApiAccountNo(?string $API_ACCOUNT_NO): void
    {
        $this->API_ACCOUNT_NO = $API_ACCOUNT_NO;
    }

    public function getApiAccountMeterNo(): ?string
    {
        return $this->API_ACCOUNT_METER_NO;
    }

    public function setApiAccountMeterNo(?string $API_ACCOUNT_METER_NO): void
    {
        $this->API_ACCOUNT_METER_NO = $API_ACCOUNT_METER_NO;
    }

    public function getApiSecret(): ?string
    {
        return $this->API_SECRET;
    }

    public function setApiSecret(?string $API_SECRET): void
    {
        $this->API_SECRET = $API_SECRET;
    }

    public function getApiUrl(): ?string
    {
        return $this->API_URL;
    }

    public function setApiUrl(?string $API_URL): void
    {
        $this->API_URL = $API_URL;
    }

    public function getCompanyName(): ?string
    {
        return $this->COMPANY_NAME;
    }

    public function setCompanyName(?string $COMPANY_NAME): void
    {
        $this->COMPANY_NAME = $COMPANY_NAME;
    }

    public function getContactPerson(): ?string
    {
        return $this->CONTACT_PERSON;
    }

    public function setContactPerson(?string $CONTACT_PERSON): void
    {
        $this->CONTACT_PERSON = $CONTACT_PERSON;
    }

    public function getAddress(): ?string
    {
        return $this->ADDRESS;
    }

    public function setAddress(?string $ADDRESS): void
    {
        $this->ADDRESS = $ADDRESS;
    }

    public function getPostalCode(): ?string
    {
        return $this->POSTAL_CODE;
    }

    public function setPostalCode(?string $POSTAL_CODE): void
    {
        $this->POSTAL_CODE = $POSTAL_CODE;
    }

    public function getCity(): ?string
    {
        return $this->CITY;
    }

    public function setCity(?string $CITY): void
    {
        $this->CITY = $CITY;
    }

    public function getCountry(): ?string
    {
        return $this->COUNTRY;
    }

    public function setCountry(?string $COUNTRY): void
    {
        $this->COUNTRY = $COUNTRY;
    }

    public function getPhone(): ?string
    {
        return $this->PHONE;
    }

    public function setPhone(?string $PHONE): void
    {
        $this->PHONE = $PHONE;
    }

    public function getEmail(): ?string
    {
        return $this->EMAIL;
    }

    public function setEmail(?string $EMAIL): void
    {
        $this->EMAIL = $EMAIL;
    }

    public function getEoriNumber(): ?string
    {
        return $this->EORI_NUMBER;
    }

    public function setEoriNumber(?string $EORI_NUMBER): void
    {
        $this->EORI_NUMBER = $EORI_NUMBER;
    }

    public function getPayerForShipping(): ?string
    {
        return $this->PAYER_FOR_SHIPPING;
    }

    public function setPayerForShipping(?string $PAYER_FOR_SHIPPING): void
    {
        $this->PAYER_FOR_SHIPPING = $PAYER_FOR_SHIPPING;
    }

    public function getPickupHoursFrom(): ?\DateTime
    {
        return $this->PICKUP_HOURS_FROM;
    }

    public function setPickupHoursFrom(?\DateTime $PICKUP_HOURS_FROM): void
    {
        $this->PICKUP_HOURS_FROM = $PICKUP_HOURS_FROM;
    }

    public function getPickupHoursTo(): ?\DateTime
    {
        return $this->PICKUP_HOURS_TO;
    }

    public function setPickupHoursTo(?\DateTime $PICKUP_HOURS_TO): void
    {
        $this->PICKUP_HOURS_TO = $PICKUP_HOURS_TO;
    }

    public function getShippingPurpose(): ?string
    {
        return $this->SHIPPING_PURPOSE;
    }

    public function setShippingPurpose(?string $SHIPPING_PURPOSE): void
    {
        $this->SHIPPING_PURPOSE = $SHIPPING_PURPOSE;
    }

    public function getLimitSingleLabel(): ?string
    {
        return $this->LIMIT_SINGLE_LABEL;
    }

    public function setLimitSingleLabel(?string $LIMIT_SINGLE_LABEL): void
    {
        $this->LIMIT_SINGLE_LABEL = $LIMIT_SINGLE_LABEL;
    }

    public function getForwardShippingCosts(): ?string
    {
        return $this->FORWARD_SHIPPING_COSTS;
    }

    public function setForwardShippingCosts(?string $FORWARD_SHIPPING_COSTS): void
    {
        $this->FORWARD_SHIPPING_COSTS = $FORWARD_SHIPPING_COSTS;
    }

    public function getIncoterms(): ?string
    {
        return $this->INCOTERMS;
    }

    public function setIncoterms(?string $INCOTERMS): void
    {
        $this->INCOTERMS = $INCOTERMS;
    }
}