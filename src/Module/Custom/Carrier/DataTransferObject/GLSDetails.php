<?php

namespace App\Module\Custom\Carrier\DataTransferObject;

class GLSDetails implements StepTwoInterface
{
    private ?string $GLS_USERNAME = null;
    private ?string $GLS_PASSWORD = null;
    private ?string $GLS_WSDL_URL = null;
    private ?string $GLS_SESSIONS_JSON = null;

    public function __construct() {}

    public function getGLSUsername(): ?string
    {
        return $this->GLS_USERNAME;
    }

    public function setGLSUsername(?string $GLS_USERNAME): void
    {
        $this->GLS_USERNAME = $GLS_USERNAME;
    }

    public function getGLSPassword(): ?string
    {
        return $this->GLS_PASSWORD;
    }

    public function setGLSPassword(?string $GLS_PASSWORD): void
    {
        $this->GLS_PASSWORD = $GLS_PASSWORD;
    }

    public function getGLSWsdlUrl(): ?string
    {
        return $this->GLS_WSDL_URL;
    }

    public function setGLSWsdlUrl(?string $GLS_WSDL_URL): void
    {
        $this->G<PERSON>_WSDL_URL = $GLS_WSDL_URL;
    }

    public function getGLSSessionsJson(): ?string
    {
        return $this->GLS_SESSIONS_JSON;
    }

    public function setGLSSessionsJson(?string $GLS_SESSIONS_JSON): void
    {
        $this->GLS_SESSIONS_JSON = $GLS_SESSIONS_JSON;
    }
}