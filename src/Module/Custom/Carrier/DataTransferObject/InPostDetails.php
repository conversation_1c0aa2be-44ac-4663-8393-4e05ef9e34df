<?php

namespace App\Module\Custom\Carrier\DataTransferObject;

class InPostDetails implements StepTwoInterface
{
    private ?string $INPOST_API_URL = null;
    private ?string $INPOST_API_KEY = null;
    private ?string $INPOST_API_ORG_ID = null;

    public function __construct() {}

    public function getInpostApiUrl(): ?string
    {
        return $this->INPOST_API_URL;
    }

    public function setInpostApiUrl(?string $INPOST_API_URL): void
    {
        $this->INPOST_API_URL = $INPOST_API_URL;
    }

    public function getInpostApiKey(): ?string
    {
        return $this->INPOST_API_KEY;
    }

    public function setInpostApiKey(?string $INPOST_API_KEY): void
    {
        $this->INPOST_API_KEY = $INPOST_API_KEY;
    }

    public function getInpostApiOrgId(): ?string
    {
        return $this->INPOST_API_ORG_ID;
    }

    public function setInpostApiOrgId(?string $INPOST_API_ORG_ID): void
    {
        $this->INPOST_API_ORG_ID = $INPOST_API_ORG_ID;
    }
}