<?php

namespace App\Module\Custom\Carrier\DataTransferObject;

class CustomDetails implements StepTwoInterface
{
    private ?string $CUSTOM_NAME = null;

    public function __construct() {}

    public function getCustomName(): ?string
    {
        return $this->CUSTOM_NAME;
    }

    public function setCustomName(?string $CUSTOM_NAME): void
    {
        $this->CUSTOM_NAME = $CUSTOM_NAME;
    }
}