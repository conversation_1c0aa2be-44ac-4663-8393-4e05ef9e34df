<?php

namespace App\Module\Custom\Carrier\DataTransferObject;

class DHLDetails implements StepTwoInterface
{
    private ?string $DHL24_WSDL_URL = null;
    private ?string $DHL24_USERNAME = null;
    private ?string $DHL24_PASSWORD = null;

    public function __construct() {}

    public function getDHL24WsdlUrl(): ?string
    {
        return $this->DHL24_WSDL_URL;
    }

    public function setDHL24WsdlUrl(?string $DHL24_WSDL_URL): void
    {
        $this->DHL24_WSDL_URL = $DHL24_WSDL_URL;
    }

    public function getDHL24Username(): ?string
    {
        return $this->DHL24_USERNAME;
    }

    public function setDHL24Username(?string $DHL24_USERNAME): void
    {
        $this->DHL24_USERNAME = $DHL24_USERNAME;
    }

    public function getDHL24Password(): ?string
    {
        return $this->DHL24_PASSWORD;
    }

    public function setDHL24Password(?string $DHL24_PASSWORD): void
    {
        $this->DHL24_PASSWORD = $DHL24_PASSWORD;
    }
}