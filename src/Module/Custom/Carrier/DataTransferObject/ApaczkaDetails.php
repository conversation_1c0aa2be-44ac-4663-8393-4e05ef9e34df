<?php

namespace App\Module\Custom\Carrier\DataTransferObject;

class ApaczkaDetails implements StepTwoInterface
{
    private ?string $APP_ID = null;
    private ?string $APP_SECRET = null;
    private ?string $API_URL = null;
    private ?int $PRINT_TYPE = null;
    private ?string $COMPANY_NAME = null;
    private ?string $CONTACT_PERSON = null;
    private ?string $ADDRESS = null;
    private ?string $POSTAL_CODE = null;
    private ?string $CITY = null;
    private ?string $COUNTRY = null;
    private ?string $PHONE = null;
    private ?string $EMAIL = null;
    private ?string $PICKUP_TYPE = null;
    private ?\DateTime $PICKUP_HOURS_FROM = null;
    private ?\DateTime $PICKUP_HOURS_TO = null;

    public function __construct() {}

    public function getAppId(): ?string
    {
        return $this->APP_ID;
    }

    public function setAppId(?string $APP_ID): void
    {
        $this->APP_ID = $APP_ID;
    }

    public function getAppSecret(): ?string
    {
        return $this->APP_SECRET;
    }

    public function setAppSecret(?string $APP_SECRET): void
    {
        $this->APP_SECRET = $APP_SECRET;
    }

    public function getApiUrl(): ?string
    {
        return $this->API_URL;
    }

    public function setApiUrl(?string $API_URL): void
    {
        $this->API_URL = $API_URL;
    }

    public function getPrintType(): ?int
    {
        return $this->PRINT_TYPE;
    }

    public function setPrintType(?int $PRINT_TYPE): void
    {
        $this->PRINT_TYPE = $PRINT_TYPE;
    }

    public function getCompanyName(): ?string
    {
        return $this->COMPANY_NAME;
    }

    public function setCompanyName(?string $COMPANY_NAME): void
    {
        $this->COMPANY_NAME = $COMPANY_NAME;
    }

    public function getContactPerson(): ?string
    {
        return $this->CONTACT_PERSON;
    }

    public function setContactPerson(?string $CONTACT_PERSON): void
    {
        $this->CONTACT_PERSON = $CONTACT_PERSON;
    }

    public function getAddress(): ?string
    {
        return $this->ADDRESS;
    }

    public function setAddress(?string $ADDRESS): void
    {
        $this->ADDRESS = $ADDRESS;
    }

    public function getPostalCode(): ?string
    {
        return $this->POSTAL_CODE;
    }

    public function setPostalCode(?string $POSTAL_CODE): void
    {
        $this->POSTAL_CODE = $POSTAL_CODE;
    }

    public function getCity(): ?string
    {
        return $this->CITY;
    }

    public function setCity(?string $CITY): void
    {
        $this->CITY = $CITY;
    }

    public function getCountry(): ?string
    {
        return $this->COUNTRY;
    }

    public function setCountry(?string $COUNTRY): void
    {
        $this->COUNTRY = $COUNTRY;
    }

    public function getPhone(): ?string
    {
        return $this->PHONE;
    }

    public function setPhone(?string $PHONE): void
    {
        $this->PHONE = $PHONE;
    }

    public function getEmail(): ?string
    {
        return $this->EMAIL;
    }

    public function setEmail(?string $EMAIL): void
    {
        $this->EMAIL = $EMAIL;
    }

    public function getPickupType(): ?string
    {
        return $this->PICKUP_TYPE;
    }

    public function setPickupType(?string $PICKUP_TYPE): void
    {
        $this->PICKUP_TYPE = $PICKUP_TYPE;
    }

    public function getPickupHoursFrom(): ?\DateTime
    {
        return $this->PICKUP_HOURS_FROM;
    }

    public function setPickupHoursFrom(?\DateTime $PICKUP_HOURS_FROM): void
    {
        $this->PICKUP_HOURS_FROM = $PICKUP_HOURS_FROM;
    }

    public function getPickupHoursTo(): ?\DateTime
    {
        return $this->PICKUP_HOURS_TO;
    }

    public function setPickupHoursTo(?\DateTime $PICKUP_HOURS_TO): void
    {
        $this->PICKUP_HOURS_TO = $PICKUP_HOURS_TO;
    }
}