<?php

namespace App\Module\Custom\Carrier\DataTransferObject;

class CarrierBaseDetails {

    private string $name;
    private string $type;
    public function __construct() {}

    public function getName(): string {
        return $this->name;
    }

    public function setName(string $name): void {
        $this->name = $name;
    }

    public function getType(): string {
        return $this->type;
    }

    public function setType(string $type): void {
        $this->type = $type;
    }

}