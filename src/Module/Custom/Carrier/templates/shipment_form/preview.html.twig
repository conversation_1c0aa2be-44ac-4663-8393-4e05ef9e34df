<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-eye"></i>
                        Podgląd formularza przesyłki - {{ carrier.name }}
                    </h3>
                    <div class="card-tools">
                        {% if has_specific_form %}
                            <span class="badge badge-success">Dedykowany formularz</span>
                        {% else %}
                            <span class="badge badge-info">Formularz uniwersalny</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>Struktura formularza:</h5>
                            
                            {% if form.children|length > 0 %}
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Nazwa pola</th>
                                                <th>Typ</th>
                                                <th>Etykieta</th>
                                                <th><PERSON><PERSON><PERSON><PERSON></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for child in form.children %}
                                                <tr>
                                                    <td><code>{{ child.vars.name }}</code></td>
                                                    <td>
                                                        <span class="badge badge-secondary">
                                                            {{ child.vars.block_prefixes[1]|default('text') }}
                                                        </span>
                                                    </td>
                                                    <td>{{ child.vars.label|default(child.vars.name) }}</td>
                                                    <td>
                                                        {% if child.vars.required %}
                                                            <i class="fas fa-check text-success"></i>
                                                        {% else %}
                                                            <i class="fas fa-times text-muted"></i>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    Ten przewoźnik nie ma dodatkowych pól formularza.
                                </div>
                            {% endif %}

                            <div class="mt-4">
                                <h5>Podgląd formularza:</h5>
                                <div class="border p-3 bg-light">
                                    {{ form_start(form, {'attr': {'class': 'preview-form'}}) }}
                                    
                                    {% for child in form.children %}
                                        <div class="form-group mb-3">
                                            {{ form_row(child, {
                                                'attr': {
                                                    'class': 'form-control',
                                                    'disabled': 'disabled'
                                                }
                                            }) }}
                                        </div>
                                    {% endfor %}
                                    
                                    {{ form_end(form) }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">Informacje o przewoźniku</h5>
                                </div>
                                <div class="card-body">
                                    <dl class="row">
                                        <dt class="col-sm-4">Nazwa:</dt>
                                        <dd class="col-sm-8">{{ carrier.name }}</dd>
                                        
                                        <dt class="col-sm-4">Typ:</dt>
                                        <dd class="col-sm-8">{{ carrier.type }}</dd>
                                        
                                        <dt class="col-sm-4">Formularz:</dt>
                                        <dd class="col-sm-8">
                                            {% if has_specific_form %}
                                                <span class="text-success">Dedykowany</span>
                                            {% else %}
                                                <span class="text-info">Uniwersalny</span>
                                            {% endif %}
                                        </dd>
                                    </dl>
                                </div>
                            </div>

                            {% if additional_info is not empty %}
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title">Surowe dane additional_info</h5>
                                    </div>
                                    <div class="card-body">
                                        <pre class="bg-dark text-light p-2 rounded" style="font-size: 0.8rem; max-height: 400px; overflow-y: auto;">{{ additional_info|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
                                    </div>
                                </div>
                            {% endif %}

                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title">Akcje</h5>
                                </div>
                                <div class="card-body">
                                    <a href="{{ path('carrier_shipment_form_create', {'carrierId': carrier.id}) }}" 
                                       class="btn btn-primary btn-block mb-2">
                                        <i class="fas fa-edit"></i>
                                        Edytuj formularz
                                    </a>
                                    <a href="{{ path('carrier_list') }}" 
                                       class="btn btn-secondary btn-block">
                                        <i class="fas fa-arrow-left"></i>
                                        Powrót do listy
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.preview-form {
    pointer-events: none;
}

.preview-form .form-control:disabled {
    background-color: #f8f9fa;
    opacity: 0.8;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}

.badge {
    font-size: 0.75em;
}
</style>