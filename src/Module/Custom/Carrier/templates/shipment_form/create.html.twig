{% extends 'base.html.twig' %}

{% block title %}<PERSON>rz przesyłki - {{ carrier.name }}{% endblock %}

{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-shipping-fast"></i>
                        Formularz przesyłki - {{ carrier.name }}
                    </h3>
                </div>
                
                <div class="card-body">
                    {% for message in app.flashes('success') %}
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}

                    {% for message in app.flashes('error') %}
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}

                    <div class="row">
                        <div class="col-md-8">
                            {{ form_start(form, {'attr': {'class': 'shipment-form', 'novalidate': 'novalidate'}}) }}
                            
                            <div class="form-sections">
                                {% if form.children|length > 0 %}
                                    {% for child in form.children %}
                                        <div class="form-group mb-3">
                                            {{ form_row(child, {
                                                'attr': {
                                                    'class': 'form-control'
                                                }
                                            }) }}
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        Ten przewoźnik nie wymaga dodatkowych ustawień przesyłki.
                                    </div>
                                {% endif %}
                            </div>

                            <div class="form-actions mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Zapisz ustawienia przesyłki
                                </button>
                                <a href="{{ path('carrier_list') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i>
                                    Powrót do listy przewoźników
                                </a>
                            </div>

                            {{ form_end(form) }}
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">Informacje o przewoźniku</h5>
                                </div>
                                <div class="card-body">
                                    <dl class="row">
                                        <dt class="col-sm-4">Nazwa:</dt>
                                        <dd class="col-sm-8">{{ carrier.name }}</dd>
                                        
                                        <dt class="col-sm-4">Typ:</dt>
                                        <dd class="col-sm-8">{{ carrier.type }}</dd>
                                        
                                        {% if carrier.description %}
                                            <dt class="col-sm-4">Opis:</dt>
                                            <dd class="col-sm-8">{{ carrier.description }}</dd>
                                        {% endif %}
                                    </dl>
                                </div>
                            </div>

                            {% if additional_info is not empty %}
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="card-title">Dostępne opcje</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="accordion" id="additionalInfoAccordion">
                                            {% set section_counter = 0 %}
                                            {% for key, section in additional_info %}
                                                {% if section is iterable and section is not empty %}
                                                    {% set section_counter = section_counter + 1 %}
                                                    <div class="accordion-item">
                                                        <h2 class="accordion-header" id="heading{{ section_counter }}">
                                                            <button class="accordion-button collapsed" type="button" 
                                                                    data-bs-toggle="collapse" 
                                                                    data-bs-target="#collapse{{ section_counter }}"
                                                                    aria-expanded="false" 
                                                                    aria-controls="collapse{{ section_counter }}">
                                                                {{ key|title }}
                                                            </button>
                                                        </h2>
                                                        <div id="collapse{{ section_counter }}" 
                                                             class="accordion-collapse collapse" 
                                                             aria-labelledby="heading{{ section_counter }}"
                                                             data-bs-parent="#additionalInfoAccordion">
                                                            <div class="accordion-body">
                                                                {% if section is iterable %}
                                                                    <small class="text-muted">
                                                                        Dostępne opcje: {{ section|length }}
                                                                    </small>
                                                                {% endif %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.shipment-form .form-group {
    margin-bottom: 1rem;
}

.shipment-form .form-control {
    border-radius: 0.375rem;
}

.shipment-form .form-check-input {
    margin-top: 0.25rem;
}

.form-actions {
    border-top: 1px solid #dee2e6;
    padding-top: 1rem;
}

.accordion-button:not(.collapsed) {
    background-color: #f8f9fa;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dodaj walidację formularza
    const form = document.querySelector('.shipment-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('Proszę wypełnić wszystkie wymagane pola.');
            }
        });
    }
});
</script>
{% endblock %}