<div class="card">
    <div class="card-title"></div>
    <div class="card-body">
        <table class="table">
            <thead>
            <tr>
                <th>{% trans %}Carrier name{% endtrans %}</th>
                <th>{% trans %}Carrier type{% endtrans %}</th>
                <th>{% trans %}Action{% endtrans %}</th>
            </tr>
            </thead>
            <tbody>
            {% for carrier in carriers %}
                <tr>
                    <td>{{ carrier.name }}</td>
                    <td>{{ carrier.type }}</td>
                    <td class="text-center">
                        <div class="btn-group" role="group">
                            <a href="{{ path('admin_carrier_view', {id: carrier.id}) }}">
                                <button type="button" class="btn btn-sm btn-outline-primary"
                                        title="{% trans %}Carrier Edit{% endtrans %}">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </a>
                        </div>
                    </td>

                </tr>
            {% else %}
                <tr>
                    <td colspan="6">{% trans %}no records found{% endtrans %}</td>
                </tr>
            {% endfor %}

            </tbody>
        </table>
        <a href="{{ path('admin_carrier_create', {step: 'base-elements'}) }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>{% trans %}Add new carrier{% endtrans %}
        </a>
    </div>
</div>