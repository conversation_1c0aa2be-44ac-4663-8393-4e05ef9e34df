<?php

namespace App\Module\Custom\Order\Controller;

use App\Core\Entity\OrderStatusTab;
use App\Core\Repository\OrderStatusTabRepository;
use App\Engine\Response\PageResponse;
use App\Module\Custom\Order\Form\OrderStatusTabType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/order/status/tab')]
final class OrderStatusTabController extends AbstractController
{
    #[Route(name: 'admin_order_status_tab_index', methods: ['GET'])]
    public function index(OrderStatusTabRepository $orderStatusTabRepository): PageResponse
    {
        return PageResponse::create('@orderModule/order_status_tab/index.html.twig', [
            'order_status_tabs' => $orderStatusTabRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'admin_order_status_tab_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): PageResponse|Response
    {
        $orderStatusTab = new OrderStatusTab();
        $form = $this->createForm(OrderStatusTabType::class, $orderStatusTab);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($orderStatusTab);
            $entityManager->flush();

            return $this->redirectToRoute('admin_order_status_tab_index', [], Response::HTTP_SEE_OTHER);
        }

        return PageResponse::create('@orderModule/order_status_tab/new.html.twig', [
            'order_status_tab' => $orderStatusTab,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'admin_order_status_tab_show', methods: ['GET'])]
    public function show(OrderStatusTab $orderStatusTab): PageResponse
    {
        return PageResponse::create('order_status_tab/show.html.twig', [
            'order_status_tab' => $orderStatusTab,
        ]);
    }

    #[Route('/{id}/edit', name: 'admin_order_status_tab_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, OrderStatusTab $orderStatusTab, EntityManagerInterface $entityManager): PageResponse|Response
    {
        $form = $this->createForm(OrderStatusTabType::class, $orderStatusTab);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            return $this->redirectToRoute('admin_order_status_tab_index', [], Response::HTTP_SEE_OTHER);
        }

        return PageResponse::create('@orderModule/order_status_tab/edit.html.twig', [
            'order_status_tab' => $orderStatusTab,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/delete', name: 'admin_order_status_tab_delete', methods: ['POST'])]
    public function delete(Request $request, OrderStatusTab $orderStatusTab, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$orderStatusTab->getId(), $request->getPayload()->getString('_token'))) {
            $entityManager->remove($orderStatusTab);
            $entityManager->flush();
        }

        return $this->redirectToRoute('admin_order_status_tab_index', [], Response::HTTP_SEE_OTHER);
    }
}
