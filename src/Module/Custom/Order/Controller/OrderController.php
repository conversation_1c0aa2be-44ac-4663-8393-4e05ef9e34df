<?php

namespace App\Module\Custom\Order\Controller;

use App\Core\Entity\Order;
use App\Core\Service\Fetcher\Order\PrestashopOrderFetcherService;
use App\Engine\Response\PageResponse;
use App\Module\Custom\Carrier\CarrierUtil;
use App\Module\Custom\Carrier\Form\ShipmentForm\ShipmentFormService;
use App\Module\Custom\Order\Form\OrderType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/orders')]
class OrderController extends AbstractController {

    public function __construct(private readonly EntityManagerInterface $entityManager) {}

    #[Route('/list', name: 'admin_order_list', methods: ['POST', 'GET'])]
    public function list(PrestashopOrderFetcherService $prestashopOrderFetcherService, Request $request): PageResponse {
        $page = (int) $request->get('page') ?? 1;
        $perPage = 50;
        $offset = ($page * $perPage) > 0 ? ($page * $perPage) - $perPage : ($page * $perPage);
        $counted = $this->entityManager->getRepository(Order::class)->count();
        $pages = ceil($counted / $perPage);
        $paginator = [
            'currentPage' => $page,
            'pages' => $pages,
            'totalPages' => $pages,
            'min' => 1,
            'max' => $pages,
        ];
        $orders = $this->entityManager->getRepository(Order::class)->findBy([], ['date_add' => 'DESC'], $perPage, $offset);
        return PageResponse::create('@orderModule/order/list.html.twig', [
            'orders' => $orders,
            'paginator' => $paginator
        ]);
    }

    #[Route('/{id}/view', name: 'admin_order_view', methods: ['POST', 'GET'])]
    public function view(Order $order, Request $request, CarrierUtil $carrierUtil, ShipmentFormService $shipmentFormService): PageResponse|Response {
        $form = $this->createForm(OrderType::class, $order);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $order = $form->getData();
            $this->entityManager->persist($order);
            $this->entityManager->flush();

            return $this->redirectToRoute('admin_order_view', ['id' => $order->getId()]);
        }


        return PageResponse::create('@orderModule/order/view.html.twig', [
            'form' => $form->createView(),
            'order' => $order,
        ]);
    }
}