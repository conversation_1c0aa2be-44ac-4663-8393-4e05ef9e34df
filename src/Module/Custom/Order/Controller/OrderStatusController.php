<?php

namespace App\Module\Custom\Order\Controller;

use App\Core\Entity\OrderStatus;
use App\Core\Repository\OrderStatusRepository;
use App\Engine\Response\PageResponse;
use App\Module\Custom\Order\Form\OrderStatusType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/order/status')]
final class OrderStatusController extends AbstractController
{
    #[Route('/', name: 'admin_order_status_index', methods: ['GET'])]
    public function index(OrderStatusRepository $orderStatusRepository): PageResponse
    {
        return PageResponse::create('@orderModule/order_status/index.html.twig', [
            'order_statuses' => $orderStatusRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'admin_order_status_new', methods: ['GET', 'POST'])]
    public function create(Request $request, EntityManagerInterface $entityManager): PageResponse|Response
    {
        $orderStatus = new OrderStatus();
        $form = $this->createForm(OrderStatusType::class, $orderStatus);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($orderStatus);
            $entityManager->flush();

            return $this->redirectToRoute('admin_order_status_index', [], Response::HTTP_SEE_OTHER);
        }

        return PageResponse::create('@orderModule/order_status/new.html.twig', [
            'order_status' => $orderStatus,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'admin_order_status_show', methods: ['GET'])]
    public function read(OrderStatus $orderStatus): PageResponse
    {
        return PageResponse::create('@orderModule/order_status/show.html.twig', [
            'order_status' => $orderStatus,
        ]);
    }

    #[Route('/{id}/update', name: 'admin_order_status_edit', methods: ['GET', 'POST'])]
    public function update(Request $request, OrderStatus $orderStatus, EntityManagerInterface $entityManager): PageResponse|Response
    {
        $form = $this->createForm(OrderStatusType::class, $orderStatus);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            return $this->redirectToRoute('admin_order_status_index', [], Response::HTTP_SEE_OTHER);
        }

        return PageResponse::create('@orderModule/order_status/edit.html.twig', [
            'order_status' => $orderStatus,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/delete', name: 'admin_order_status_delete', methods: ['POST'])]
    public function delete(Request $request, OrderStatus $orderStatus, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$orderStatus->getId(), $request->getPayload()->getString('_token'))) {
            $entityManager->remove($orderStatus);
            $entityManager->flush();
        }

        return $this->redirectToRoute('admin_order_status_index', [], Response::HTTP_SEE_OTHER);
    }
}
