<h1>OrderStatus index</h1>

<table class="table">
    <thead>
        <tr>
            <th>Id</th>
            <th>Name</th>
            <th>Short_name</th>
            <th>Full_name</th>
            <th>Color</th>
            <th>actions</th>
        </tr>
    </thead>
    <tbody>
    {% for order_status in order_statuses %}
        <tr>
            <td>{{ order_status.id }}</td>
            <td>{{ order_status.name }}</td>
            <td>{{ order_status.shortName }}</td>
            <td>{{ order_status.fullName }}</td>
            <td><input type="color" value="{{ order_status.color }}" disabled></td>
            <td>
                <a href="{{ path('admin_order_status_show', {'id': order_status.id}) }}">show</a>
                <a href="{{ path('admin_order_status_edit', {'id': order_status.id}) }}">edit</a>
            </td>
        </tr>
    {% else %}
        <tr>
            <td colspan="6">no records found</td>
        </tr>
    {% endfor %}
    </tbody>
</table>

<a href="{{ path('admin_order_status_new') }}">Create new</a>
