<h1>OrderStatus index</h1>

<table class="table">
    <thead>
    <tr>
        <th>Id</th>
        <th>Shop Order ID</th>
        <th>Date Add</th>
        <th>Date Updated</th>
        <th>Status</th>
        <th>Actions</th>
    </tr>
    </thead>
    <tbody>
    {% for order in orders %}
        <tr>
            <td>{{ order.id }}</td>
            <td>{{ order.shopOrderId }}</td>
            <td>{{ order.dateAdd|date('Y-m-d H:i:s') }}</td>
            <td>{{ order.dateInStatus|date('Y-m-d H:i:s') }}</td>
            <td>{{ order.internalStatusId.name }}</td>
            <td class="text-center">
                <div class="btn-group" role="group">
                    <a href="{{ path('admin_order_view', {id: order.id}) }}">
                        <button type="button" class="btn btn-sm btn-outline-primary"
                                title="Edytuj integrację">
                            <i class="fas fa-edit"></i>
                        </button>
                    </a>
                </div>
            </td>

        </tr>
    {% else %}
        <tr>
            <td colspan="6">no records found</td>
        </tr>
    {% endfor %}

    </tbody>
</table>
{% if paginator.pages > 1 %}
    <div class="paginator">
        {% if paginator.currentPage > 1 %}
            <a href="{{ path('admin_order_list', { page: 1 }) }}">« Pierwsza</a>
            <a href="{{ path('admin_order_list', { page: paginator.currentPage - 1}) }}">‹ Poprzednia</a>
        {% endif %}

        {% for i in 1..paginator.totalPages %}
            {% if i == paginator.currentPage %}
                <strong>[{{ i }}]</strong>
            {% elseif i <= paginator.currentPage + 2 and i >= paginator.currentPage - 2 %}
                <a href="{{ path('admin_order_list', { page: i }) }}">{{ i }}</a>
            {% endif %}
        {% endfor %}

        {% if paginator.currentPage < paginator.totalPages %}
            <a href="{{ path('admin_order_list', {page: paginator.currentPage + 1}) }}">Następna ›</a>
            <a href="{{ path('admin_order_list', {page: paginator.totalPages}) }}">Ostatnia »</a>
        {% endif %}
    </div>
{% endif %}