<h1><span class="badge text-bg-secondary">Order {{ order.orderId }}</span></h1>

<div class="card">
    <h4 class="card-title m-4">
        <i class="fas fa-info-circle me-2"></i>Products
    </h4>
    <div class="card-body">

        <table class="table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Quantity</th>
                    <th>Price</th>
                </tr>
            </thead>
            <tbody>
            {% for product in order.products %}
                <tr>
                    <td>{{ product.name }}</td>
                    <td>{{ product.quantity }}</td>
                    <td>{{ product.priceBrutto / 100 }} {{ order.currency }}</td>
                </tr>
            {% endfor%}
            </tbody>
        </table>
        {{ form_start(form) }}
            {{ form_widget(form.internalStatusId) }}
            <button class="btn btn-success m-4">{{ button_label|default('Save') }}</button>
        {{ form_end(form) }}

    </div>
</div>

<div class="card mt-3">
    <h4 class="card-title m-4">
        <i class="fas fa-info-circle me-2"></i>Order info
    </h4>
    <div class="card-body">

    </div>
</div>

<div class="row mt-3">
    <div class="col-md-6">
        <div class="card">
            <h4 class="card-title m-4">
                <i class="fas fa-info-circle me-2"></i>Delivery
            </h4>
            <div class="card-body">

            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <h4 class="card-title m-4">
                <i class="fas fa-info-circle me-2"></i>Invoice
            </h4>
            <div class="card-body">

            </div>
        </div>

    </div>
</div>
