<h1>OrderStatusTab index</h1>

<table class="table">
    <thead>
        <tr>
            <th>Id</th>
            <th>Name</th>
            <th>actions</th>
        </tr>
    </thead>
    <tbody>
    {% for order_status_tab in order_status_tabs %}
        <tr>
            <td>{{ order_status_tab.id }}</td>
            <td>{{ order_status_tab.name }}</td>
            <td>
                <a href="{{ path('admin_order_status_tab_show', {'id': order_status_tab.id}) }}">show</a>
                <a href="{{ path('admin_order_status_tab_edit', {'id': order_status_tab.id}) }}">edit</a>
            </td>
        </tr>
    {% else %}
        <tr>
            <td colspan="3">no records found</td>
        </tr>
    {% endfor %}
    </tbody>
</table>

<a href="{{ path('admin_order_status_tab_new') }}">Create new</a>
