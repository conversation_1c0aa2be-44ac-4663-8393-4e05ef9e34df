<?php

namespace App\Module\Custom\Order\Form;

use App\Core\Entity\Order;
use App\Core\Entity\OrderStatus;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Twig\Markup;

class OrderType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('internalStatusId', EntityType::class, [
                'class' => OrderStatus::class,
                'label_html' => true,
                'choice_label' => function(OrderStatus $orderStatus) {
                    return $orderStatus->getName();
                },
                'attr' => [
                    'class' => 'form-select'
                ]
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Order::class,
        ]);
    }
}
