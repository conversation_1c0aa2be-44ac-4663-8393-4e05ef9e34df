<?php

namespace App\Module\Custom\Order\Form;

use App\Core\Entity\OrderStatus;
use App\Core\Entity\OrderStatusTab;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ColorType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class OrderStatusType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name')
            ->add('short_name')
            ->add('full_name')
            ->add('color', ColorType::class, [])
            ->add('tab', EntityType::class, [
                'class' => OrderStatusTab::class,
                'choice_label' => 'name',
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => OrderStatus::class,
        ]);
    }
}
