<?php

namespace App\Module\Custom\Example;

use App\Engine\Hook\RegionComponent;
use App\Module\AbstractModule;

class ExampleModule extends AbstractModule
{
    public function getId(): string
    {
        return 'example';
    }

    public function getName(): string
    {
        return 'Example Module';
    }

    public function getDescription(): string
    {
        return 'A demonstration module showing how to create custom modules with hooks and controllers';
    }

    public function getVersion(): string
    {
        return '1.0.0';
    }

    public function getDependencies(): array
    {
        return [];
    }

    public function getHooks(): array
    {
        return ['page_footer', 'content_after'];
    }

    protected function hook_content_after(array $context): array
    {
        $request = $context['request'] ?? null;

        if (!$request) {
            return [];
        }

        return [
            RegionComponent::create('@example/footer_widget.html.twig', [
                'message' => 'Hello from Example Module!',
                'current_route' => $request->attributes->get('_route'),
                'timestamp' => new \DateTime()
            ], 10)
        ];
    }

    public function getConfigurationSchema(): array
    {
        return [
            'show_message' => [
                'type' => 'checkbox',
                'label' => 'Show Footer Message',
                'default' => true,
                'description' => 'Display example message in page footer'
            ],
            'message_text' => [
                'type' => 'text',
                'label' => 'Custom Message',
                'default' => 'Hello from Example Module!',
                'description' => 'Custom message to display'
            ],
            'message_color' => [
                'type' => 'select',
                'label' => 'Message Color',
                'options' => [
                    'primary' => 'Blue',
                    'success' => 'Green',
                    'warning' => 'Yellow',
                    'danger' => 'Red',
                    'info' => 'Light Blue'
                ],
                'default' => 'primary'
            ]
        ];
    }

    protected function onInstall(): void
    {

    }

    protected function onEnable(): void
    {

    }

    protected function onDisable(): void
    {

    }

    protected function onUninstall(): void
    {

    }
}
