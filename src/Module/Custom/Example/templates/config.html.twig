<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                    <div class="card-tools">
                        <a href="{{ path('example_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Module
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Configuration Demo</h5>
                        <p>This is a demonstration of module configuration. In a real implementation,
                        these settings would be managed through the module management interface.</p>
                    </div>

                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="show_message" class="form-label">
                                        <input type="checkbox" id="show_message"
                                               {% if settings.show_message %}checked{% endif %}>
                                        Show Footer Message
                                    </label>
                                    <div class="form-text">Display example message in page footer</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="message_text" class="form-label">Custom Message</label>
                            <input type="text" class="form-control" id="message_text"
                                   value="{{ settings.message_text }}">
                            <div class="form-text">Custom message to display</div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="message_color" class="form-label">Message Color</label>
                            <select class="form-control" id="message_color">
                                <option value="primary" {% if settings.message_color == 'primary' %}selected{% endif %}>Blue</option>
                                <option value="success" {% if settings.message_color == 'success' %}selected{% endif %}>Green</option>
                                <option value="warning" {% if settings.message_color == 'warning' %}selected{% endif %}>Yellow</option>
                                <option value="danger" {% if settings.message_color == 'danger' %}selected{% endif %}>Red</option>
                                <option value="info" {% if settings.message_color == 'info' %}selected{% endif %}>Light Blue</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <button type="button" class="btn btn-primary" onclick="saveConfig()">
                                <i class="fas fa-save"></i> Save Configuration
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetConfig()">
                                <i class="fas fa-undo"></i> Reset to Defaults
                            </button>
                        </div>
                    </form>

                    <div class="mt-4">
                        <h5>Current Configuration</h5>
                        <pre class="bg-light p-3 rounded"><code>{{ settings|json_encode(constant('JSON_PRETTY_PRINT')) }}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function saveConfig() {
    const config = {
        show_message: document.getElementById('show_message').checked,
        message_text: document.getElementById('message_text').value,
        message_color: document.getElementById('message_color').value
    };

    alert('Configuration saved!\n\n' + JSON.stringify(config, null, 2));
}

function resetConfig() {
    document.getElementById('show_message').checked = true;
    document.getElementById('message_text').value = 'Hello from Example Module!';
    document.getElementById('message_color').value = 'primary';

    alert('Configuration reset to defaults!');
}
</script>
