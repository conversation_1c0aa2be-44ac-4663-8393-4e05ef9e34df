<?php

namespace App\Module\Custom\Example\Controller;

use App\Engine\Response\PageResponse;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

class ExampleController extends AbstractController {
    #[Route('/', name: 'example_index')]
    public function index(): PageResponse
    {
        return PageResponse::create('@example/index.html.twig', [
            'title' => 'Example Module',
            'message' => 'Welcome to the Example Module!',
            'features' => [
                'Custom controllers in modules',
                'Module-specific templates',
                'Hook system integration',
                'Configuration management'
            ]
        ]);
    }

    #[Route('/api/status', name: 'example_api_status', methods: ['GET'])]
    public function apiStatus(): JsonResponse
    {
        return new JsonResponse([
            'status' => 'ok',
            'module' => 'example',
            'timestamp' => time(),
            'message' => 'Example module API is working!'
        ]);
    }

    #[Route('/config', name: 'example_config')]
    public function config(): PageResponse
    {
        return PageResponse::create('@example/config.html.twig', [
            'title' => 'Example Module Configuration',
            'settings' => [
                'show_message' => true,
                'message_text' => 'Hello from Example Module!',
                'message_color' => 'primary'
            ]
        ]);
    }
}
