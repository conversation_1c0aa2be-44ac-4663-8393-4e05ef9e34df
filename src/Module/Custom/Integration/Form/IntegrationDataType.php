<?php

namespace App\Module\Custom\Integration\Form;

use App\Core\Entity\IntegrationData;
use App\Core\Enum\FetchProducts;
use App\Core\Enum\OrderFetchInterval;
use App\Core\Enum\OrderStatusSendPaid;
use App\Core\Enum\OrderStatusSynchronize;
use App\Core\Service\OrderStatusService;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class IntegrationDataType extends AbstractType
{

    public function __construct(private readonly OrderStatusService $orderStatusService, private readonly TranslatorInterface $translator) {}
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('apiUrl', TextType::class, [
                'label' => $this->translator->trans('Api Url'),
            ])
            ->add('apiToken', TextType::class, [
                'label' => $this->translator->trans('Api Token'),
            ])
            ->add('apiSecret', TextType::class, [
                'label' => $this->translator->trans('Api Secret'),
            ])
            ->add('fetchStatus', ChoiceType::class, [
                'label' => 'Pobranym zamówieniom ustaw status',
                'choices' => $this->getAllStatuses(),
                'choice_value' => 'id',
                'choice_label' => 'name',
            ])
            ->add('cancelStatus', ChoiceType::class, [
                'label' => 'Anulowanym zamówieniom ustaw status',
                'choices' => $this->getAllStatuses(),
                'choice_value' => 'id',
                'choice_label' => 'name',
            ])
            ->add('orderFetchInterval', EnumType::class, [
                'class' => OrderFetchInterval::class,
                'label' => 'Co ile pobierać zamówienia',
            ])
            ->add('fetchProducts', EnumType::class, [
                'class' => FetchProducts::class,
                'label' => 'Czy pobierać produkty',
            ])
            ->add('orderStatusSendPaid', EnumType::class, [
                'class' => OrderStatusSendPaid::class,
                'label' => 'Czy wysyłać do sklepu informację o opłaceniu zamówienia?',
            ])
            ->add('orderStatusSynchronize', EnumType::class, [
                'class' => OrderStatusSynchronize::class,
                'label' => 'Czy synchronizować statusy pomiędzy systemem a sklepem?',
                'choices' => [
                    OrderStatusSynchronize::YES,
                    OrderStatusSynchronize::NO,
                ],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => IntegrationData::class,
        ]);
    }

    protected function getAllStatuses() {
        return $this->orderStatusService->getAll();
    }
}