<?php

namespace App\Module\Custom\Integration\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;

class IntegrationSettingsType extends AbstractType {

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Setting name',
                'required' => true,
            ])
            ->add('value', TextType::class, [
                'label' => 'Value',
                'required' => true,
            ])
        ;
    }
}