<?php

namespace App\Module\Custom\Integration\Form;

use App\Core\Entity\Integration;
use App\Core\Entity\User;
use App\Core\Integration\IntegrationService;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class IntegrationType extends AbstractType
{
    public function __construct(private readonly IntegrationService $integrationService) {}
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $integrationTypes = $this->integrationService->getAllTypes();

        $builder
            ->add('name', TextType::class, [
                'label' => 'Integration name',
                'required' => TRUE,
            ])
            ->add('owner', EntityType::class, [
                'class' => User::class,
                'choice_label' => 'username',
            ])
            ->add('type', ChoiceType::class, [
                'choices' => array_flip(array_map(function ($item) {
                    return $item['type'];
                }, $integrationTypes)),
                'label' => 'Integration type',
                'required' => TRUE,
            ])
            ->add('integrationData', IntegrationDataType::class, [
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Integration::class,
        ]);
    }

}
