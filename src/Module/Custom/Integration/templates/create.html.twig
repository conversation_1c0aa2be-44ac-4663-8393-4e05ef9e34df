{{ form_start(form) }}
<div class="card">
    <div class="card-body">
        <h4 class="card-title mb-4">
            <i class="fas fa-info-circle me-2"></i>Integration info
        </h4>

        {{ form_row(form.name) }}
        {{ form_row(form.type) }}
        {{ form_row(form.owner) }}
        {{ form_rest(form) }}
        <button class="btn btn-success">{{ button_label|default('Save') }}</button>
    </div>
</div>

{#<div class="card">#}
{#    <div class="card-body">#}
{#        <h4 class="card-title mb-4">#}
{#            <i class='bx bx-building-house me-2'></i>Integration Settings#}
{#        </h4>#}
{#        <div data-controller="collection">#}
{#            <div data-collection-target="container">#}
{#                {% for setting in form.integrationSettings %}#}
{#                    <div data-collection-item>#}
{#                        {% include 'integrationSettings.html.twig' with {'settingForm': setting} %}#}
{#                        <button type="button" data-action="collection#remove" class="btn btn-danger btn-sm">#}
{#                            <i class='fas fa-trash me-1'></i>Delete setting</button>#}
{#                    </div>#}
{#                {% endfor %}#}
{#            </div>#}

{#            <template data-collection-target="template">#}
{#                <div data-collection-item>#}
{#                    <div class="border p-3 mb-3">#}
{#                        {{ form_widget(form.integrationSettings.vars.prototype)|replace({'__name__label__': ''})|raw }}#}
{#                    </div>#}
{#                    <button type="button" data-action="collection#remove" class="btn btn-danger btn-sm">#}
{#                        <i class='fas fa-trash me-1'></i>Delete setting</button>#}
{#                </div>#}
{#            </template>#}

{#            <button type="button" data-action="collection#add" class="add-address-btn btn btn-primary btn-sm">#}
{#                <i class="fas fa-plus me-1"></i>#}
{#                Add new setting</button>#}
{#        </div>#}
{#        <hr>#}



{{ form_end(form) }}
