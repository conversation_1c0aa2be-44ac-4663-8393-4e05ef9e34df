<?php

namespace App\Module\Custom\Integration\Controller;

use App\Core\Entity\Integration;
use App\Engine\Response\PageResponse;
use App\Module\Custom\Integration\Form\IntegrationType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/integrations')]
class IntegrationController extends AbstractController {

    public function __construct(
        private readonly EntityManagerInterface $entityManager) {

    }

    #[Route('/list', name: 'admin_integration_list')]
    public function list() {
        return PageResponse::create(
            '@integrationModule/list.html.twig', [
                'integrations' => $this->entityManager->getRepository(Integration::class)->findAll()
            ]);
    }

    #[Route('/create', name: 'admin_integration_create', methods: ['GET', 'POST'])]
    public function create(Request $request) {
        $form = $this->createForm(IntegrationType::class, new Integration());
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $integration = $form->getData();
            $this->entityManager->persist($integration);
            $this->entityManager->flush();

            return $this->redirectToRoute('admin_integration_list');
        }

        return PageResponse::create('@integrationModule/create.html.twig', ['form' => $form->createView()]);
    }

    #[Route('/{id}', name: 'admin_integration_show', methods: ['GET'])]
    public function read(Integration $integration): PageResponse|Response {
        return PageResponse::create('@integrationModule/show.html.twig', [
            'integration' => $integration,
        ]);
    }

    #[Route('/{id}/update', name: 'admin_integration_update', methods: ['POST', 'GET'])]
    public function update(Integration $integration, Request $request): PageResponse|Response {
        $form = $this->createForm(IntegrationType::class, $integration);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $integration = $form->getData();
            $this->entityManager->persist($integration);
            $this->entityManager->flush();

            return $this->redirectToRoute('admin_integration_list');
        }

        return PageResponse::create('@integrationModule/edit.html.twig', ['form' => $form->createView(), 'integration' => $integration]);

    }

    #[Route('/{id}/delete', name: 'admin_integration_delete', methods: ['POST'])]
    public function delete(Request $request, Integration $integration, EntityManagerInterface $entityManager): PageResponse|Response {
        if ($this->isCsrfTokenValid('delete'.$integration->getId(), $request->getPayload()->getString('_token'))) {
            $entityManager->remove($integration);
            $entityManager->flush();
        }

        return $this->redirectToRoute('admin_integration_list', [], Response::HTTP_SEE_OTHER);
    }
}