<?php

namespace App\Engine\Controller\Admin;

use App\Core\Service\BlockManager;
use App\Engine\Response\PageResponse;
use App\Engine\Service\AssetFlowManager;
use App\Engine\Service\ThemeService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/blocks')]
class BlockController extends AbstractController
{
    public function __construct(
        private BlockManager $blockManager,
        private ThemeService $themeService,
        private AssetFlowManager $assetFlow
    ) {}

    #[Route('/', name: 'admin_blocks')]
    public function index(): PageResponse
    {
        $availableBlocks = $this->blockManager->getAvailableBlocks();
        $blocksByRegion = $this->blockManager->getBlocksByRegion();
        $regions = $this->themeService->getRegions();

        $this->assetFlow
            ->addScript('controllers/block_admin_controller.js', ['weight' => 10])
            ->addStylesheet('admin/block-admin.css', ['weight' => 5])
            ->addStimulusController('block-admin', [
                'addUrl' => $this->generateUrl('admin_blocks_add'),
                'updateUrl' => $this->generateUrl('admin_blocks_update', ['placementId' => '__ID__']),
                'removeUrl' => $this->generateUrl('admin_blocks_remove', ['placementId' => '__ID__'])
            ]);

        $response = PageResponse::create('admin/blocks/index.html.twig', [
            'title' => 'Block Management',
            'available_blocks' => $availableBlocks,
            'blocks_by_region' => $blocksByRegion,
            'regions' => $regions
        ]);

        $response->setMetadata('assetFlow', $this->assetFlow->getAssetFlow());

        return $response;
    }

    #[Route('/add', name: 'admin_blocks_add', methods: ['POST'])]
    public function addBlock(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $blockId = $data['block_id'] ?? '';
        $region = $data['region'] ?? '';
        $weight = $data['weight'] ?? 0;

        if (!$blockId || !$region) {
            return new JsonResponse(['error' => 'Missing block_id or region'], 400);
        }

        $block = $this->blockManager->getBlock($blockId);
        if (!$block) {
            return new JsonResponse(['error' => 'Block not found'], 404);
        }

        $placement = $this->blockManager->placeBlock($blockId, $region, $weight);

        return new JsonResponse([
            'success' => true,
            'placement_id' => $placement->getId(),
            'message' => 'Block added successfully'
        ]);
    }

    #[Route('/remove/{placementId}', name: 'admin_blocks_remove', methods: ['DELETE'])]
    public function removeBlock(int $placementId): JsonResponse
    {
        $this->blockManager->removeBlock($placementId);

        return new JsonResponse([
            'success' => true,
            'message' => 'Block removed successfully'
        ]);
    }

    #[Route('/update/{placementId}', name: 'admin_blocks_update', methods: ['PUT'])]
    public function updateBlock(int $placementId, Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $this->blockManager->updateBlockPlacement($placementId, $data);

        return new JsonResponse([
            'success' => true,
            'message' => 'Block updated successfully'
        ]);
    }

    #[Route('/configure/{placementId}', name: 'admin_blocks_configure')]
    public function configureBlock(int $placementId, Request $request): PageResponse|RedirectResponse
    {

        $placements = $this->blockManager->getAllPlacements();
        $placement = null;

        foreach ($placements as $p) {
            if ($p->getId() === $placementId) {
                $placement = $p;
                break;
            }
        }

        if (!$placement) {
            throw $this->createNotFoundException('Block placement not found');
        }

        $block = $this->blockManager->getBlock($placement->getBlockId());
        if (!$block) {
            throw $this->createNotFoundException('Block not found');
        }

        if ($request->isMethod('POST')) {
            $config = $request->request->all();
            $this->blockManager->updateBlockPlacement($placementId, ['configuration' => $config]);

            $this->addFlash('success', 'Block configuration saved');
            return $this->redirectToRoute('admin_blocks');
        }

        return PageResponse::create('admin/blocks/configure.html.twig', [
            'title' => 'Configure Block: ' . $block->getLabel(),
            'block' => $block,
            'placement' => $placement,
            'form_fields' => $block->getConfigurationForm(),
            'current_config' => $placement->getConfiguration()
        ]);
    }

    #[Route('/available', name: 'admin_blocks_available', methods: ['GET'])]
    public function getAvailableBlocks(): JsonResponse
    {
        $blocks = [];
        foreach ($this->blockManager->getAvailableBlocks() as $block) {
            $blocks[] = [
                'id' => $block->getId(),
                'label' => $block->getLabel(),
                'description' => $block->getDescription(),
                'category' => $block->getCategory()
            ];
        }

        return new JsonResponse($blocks);
    }
}
