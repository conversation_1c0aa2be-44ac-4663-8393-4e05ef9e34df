<?php

namespace App\Engine\Controller\Admin;

use App\Engine\Response\PageResponse;
use App\Engine\Service\AssetFlowManager;
use App\Engine\Service\ModuleManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/modules')]
class ModuleController extends AbstractController
{
    public function __construct(
        private ModuleManager $moduleManager,
        private AssetFlowManager $assetFlow
    ) {}

    #[Route('/', name: 'admin_modules')]
    public function index(): PageResponse
    {
        $modulesByStatus = $this->moduleManager->getModulesByStatus();

        $this->assetFlow
            ->addStylesheet('admin/module-admin.css', ['weight' => 5])
            ->addPageSetting('moduleUrls', [
                'install' => $this->generateUrl('admin_modules_install', ['moduleId' => '__ID__']),
                'uninstall' => $this->generateUrl('admin_modules_uninstall', ['moduleId' => '__ID__']),
                'enable' => $this->generateUrl('admin_modules_enable', ['moduleId' => '__ID__']),
                'disable' => $this->generateUrl('admin_modules_disable', ['moduleId' => '__ID__'])
            ]);

        $response = PageResponse::create('admin/modules/index.html.twig', [
            'title' => 'Module Management',
            'modules_by_status' => $modulesByStatus
        ]);

        $response->setMetadata('assetFlow', $this->assetFlow->getAssetFlow());
        return $response;
    }

    #[Route('/install/{moduleId}', name: 'admin_modules_install', methods: ['POST'])]
    public function installModule(string $moduleId): JsonResponse
    {
        try {
            $success = $this->moduleManager->installModule($moduleId);

            if ($success) {
                return new JsonResponse([
                    'success' => true,
                    'message' => 'Module installed successfully'
                ]);
            } else {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Module not found'
                ], 404);
            }
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/uninstall/{moduleId}', name: 'admin_modules_uninstall', methods: ['POST'])]
    public function uninstallModule(string $moduleId): JsonResponse
    {
        try {
            $success = $this->moduleManager->uninstallModule($moduleId);

            if ($success) {
                return new JsonResponse([
                    'success' => true,
                    'message' => 'Module uninstalled successfully'
                ]);
            } else {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Module not found'
                ], 404);
            }
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/enable/{moduleId}', name: 'admin_modules_enable', methods: ['POST'])]
    public function enableModule(string $moduleId): JsonResponse
    {
        try {
            $success = $this->moduleManager->enableModule($moduleId);

            if ($success) {
                return new JsonResponse([
                    'success' => true,
                    'message' => 'Module enabled successfully'
                ]);
            } else {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Module not found or not installed'
                ], 404);
            }
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/disable/{moduleId}', name: 'admin_modules_disable', methods: ['POST'])]
    public function disableModule(string $moduleId): JsonResponse
    {
        try {
            $success = $this->moduleManager->disableModule($moduleId);

            if ($success) {
                return new JsonResponse([
                    'success' => true,
                    'message' => 'Module disabled successfully'
                ]);
            } else {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Module not found'
                ], 404);
            }
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/configure/{moduleId}', name: 'admin_modules_configure')]
    public function configureModule(string $moduleId, Request $request): PageResponse|RedirectResponse
    {
        $module = $this->moduleManager->getModule($moduleId);
        if (!$module) {
            throw $this->createNotFoundException('Module not found');
        }

        if (!$this->moduleManager->isModuleInstalled($moduleId)) {
            $this->addFlash('error', 'Module must be installed before configuration');
            return $this->redirectToRoute('admin_modules');
        }

        if ($request->isMethod('POST')) {
            $config = $request->request->all();
            $this->moduleManager->updateModuleConfiguration($moduleId, $config);

            $this->addFlash('success', 'Module configuration saved');
            return $this->redirectToRoute('admin_modules');
        }

        return PageResponse::create('admin/modules/configure.html.twig', [
            'title' => 'Configure Module: ' . $module->getName(),
            'module' => $module,
            'form_fields' => $module->getConfigurationSchema(),
            'current_config' => $this->moduleManager->getModuleConfiguration($moduleId)
        ]);
    }

    #[Route('/info/{moduleId}', name: 'admin_modules_info', methods: ['GET'])]
    public function getModuleInfo(string $moduleId): JsonResponse
    {
        $module = $this->moduleManager->getModule($moduleId);
        if (!$module) {
            return new JsonResponse(['error' => 'Module not found'], 404);
        }

        return new JsonResponse([
            'id' => $module->getId(),
            'name' => $module->getName(),
            'description' => $module->getDescription(),
            'version' => $module->getVersion(),
            'dependencies' => $module->getDependencies(),
            'hooks' => $module->getHooks(),
            'installed' => $this->moduleManager->isModuleInstalled($moduleId),
            'enabled' => $this->moduleManager->isModuleEnabled($moduleId)
        ]);
    }

    #[Route('/create', name: 'admin_modules_create')]
    public function createModule(Request $request): PageResponse|RedirectResponse
    {
        if ($request->isMethod('POST')) {
            $moduleData = [
                'id' => $request->request->get('module_id'),
                'name' => $request->request->get('module_name'),
                'description' => $request->request->get('module_description'),
                'version' => $request->request->get('module_version', '1.0.0'),
                'hooks' => array_filter(explode(',', $request->request->get('module_hooks', '')))
            ];

            if (empty($moduleData['id']) || empty($moduleData['name'])) {
                $this->addFlash('error', 'Module ID and name are required');
                return $this->redirectToRoute('admin_modules_create');
            }

            if (!preg_match('/^[a-zA-Z][a-zA-Z0-9_]*$/', $moduleData['id'])) {
                $this->addFlash('error', 'Module ID must start with a letter and contain only letters, numbers, and underscores');
                return $this->redirectToRoute('admin_modules_create');
            }

            try {
                $moduleName = ucfirst($moduleData['id']);
                $success = $this->moduleManager->createModule($moduleName, $moduleData);

                if ($success) {
                    $this->addFlash('success', 'Module created successfully');
                    return $this->redirectToRoute('admin_modules');
                }
            } catch (\Exception $e) {
                $this->addFlash('error', 'Failed to create module: ' . $e->getMessage());
            }
        }

        return PageResponse::create('admin/modules/create.html.twig', [
            'title' => 'Create New Module'
        ]);
    }
}
