<?php

namespace App\Engine\Controller\Admin;

use App\Core\Entity\User;
use App\Engine\Response\PageResponse;
use App\Engine\Service\AssetFlowManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/admin/users')]
class UserController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserPasswordHasherInterface $passwordHasher,
        private ValidatorInterface $validator,
        private AssetFlowManager $assetFlow
    ) {}

    #[Route('/', name: 'admin_users')]
    public function index(): PageResponse
    {
        $users = $this->entityManager->getRepository(User::class)->findBy([], ['createdAt' => 'DESC']);

        $this->assetFlow
            ->addStylesheet('admin/user-admin.css', ['weight' => 5])
            ->addPageSetting('userUrls', [
                'create' => $this->generateUrl('admin_users_new'),
                'edit' => $this->generateUrl('admin_users_edit', ['id' => '__ID__']),
                'delete' => $this->generateUrl('admin_users_delete', ['id' => '__ID__'])
            ]);

        return PageResponse::create('admin/users/index.html.twig', [
            'title' => 'User Management',
            'users' => $users
        ]);
    }

    #[Route('/new', name: 'admin_users_new', methods: ['GET', 'POST'])]
    public function new(Request $request): PageResponse|RedirectResponse
    {
        if ($request->isMethod('POST')) {
            $data = $request->request->all();

            $user = new User();
            $user->setUsername($data['username'] ?? '');
            $user->setEmail($data['email'] ?? '');
            $user->setEnabled($data['enabled'] ?? false);

            // Handle roles
            $roles = [];
            if (isset($data['roles']) && is_array($data['roles'])) {
                $roles = array_filter($data['roles']);
            }
            $user->setRoles($roles);

            // Hash password
            if (!empty($data['password'])) {
                $hashedPassword = $this->passwordHasher->hashPassword($user, $data['password']);
                $user->setPassword($hashedPassword);
            }

            // Validate
            $errors = $this->validator->validate($user);
            if (count($errors) > 0) {
                $errorMessages = [];
                foreach ($errors as $error) {
                    $errorMessages[] = $error->getMessage();
                }
                $this->addFlash('error', 'Validation failed: ' . implode(', ', $errorMessages));
                
                return PageResponse::create('admin/users/new.html.twig', [
                    'title' => 'Create New User',
                    'user' => $user,
                    'available_roles' => $this->getAvailableRoles(),
                    'errors' => $errors
                ]);
            }

            try {
                $this->entityManager->persist($user);
                $this->entityManager->flush();

                $this->addFlash('success', 'User created successfully');
                return $this->redirectToRoute('admin_users');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Failed to create user: ' . $e->getMessage());
            }
        }

        return PageResponse::create('admin/users/new.html.twig', [
            'title' => 'Create New User',
            'user' => new User(),
            'available_roles' => $this->getAvailableRoles()
        ]);
    }

    #[Route('/{id}/edit', name: 'admin_users_edit', methods: ['GET', 'POST'])]
    public function edit(string $id, Request $request): PageResponse|RedirectResponse
    {
        $user = $this->entityManager->getRepository(User::class)->find(Uuid::fromString($id));
        
        if (!$user) {
            $this->addFlash('error', 'User not found');
            return $this->redirectToRoute('admin_users');
        }

        if ($request->isMethod('POST')) {
            $data = $request->request->all();

            $user->setUsername($data['username'] ?? $user->getUsername());
            $user->setEmail($data['email'] ?? $user->getEmail());
            $user->setEnabled($data['enabled'] ?? false);

            // Handle roles
            $roles = [];
            if (isset($data['roles']) && is_array($data['roles'])) {
                $roles = array_filter($data['roles']);
            }
            $user->setRoles($roles);

            // Hash password if provided
            if (!empty($data['password'])) {
                $hashedPassword = $this->passwordHasher->hashPassword($user, $data['password']);
                $user->setPassword($hashedPassword);
            }

            // Validate
            $errors = $this->validator->validate($user);
            if (count($errors) > 0) {
                $errorMessages = [];
                foreach ($errors as $error) {
                    $errorMessages[] = $error->getMessage();
                }
                $this->addFlash('error', 'Validation failed: ' . implode(', ', $errorMessages));
                
                return PageResponse::create('admin/users/edit.html.twig', [
                    'title' => 'Edit User',
                    'user' => $user,
                    'available_roles' => $this->getAvailableRoles(),
                    'errors' => $errors
                ]);
            }

            try {
                $this->entityManager->flush();

                $this->addFlash('success', 'User updated successfully');
                return $this->redirectToRoute('admin_users');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Failed to update user: ' . $e->getMessage());
            }
        }

        return PageResponse::create('admin/users/edit.html.twig', [
            'title' => 'Edit User',
            'user' => $user,
            'available_roles' => $this->getAvailableRoles()
        ]);
    }

    #[Route('/{id}/delete', name: 'admin_users_delete', methods: ['POST', 'DELETE'])]
    public function delete(string $id): JsonResponse|RedirectResponse
    {
        $user = $this->entityManager->getRepository(User::class)->find(Uuid::fromString($id));
        
        if (!$user) {
            if ($this->isJsonRequest()) {
                return new JsonResponse(['error' => 'User not found'], 404);
            }
            $this->addFlash('error', 'User not found');
            return $this->redirectToRoute('admin_users');
        }

        try {
            $this->entityManager->remove($user);
            $this->entityManager->flush();

            if ($this->isJsonRequest()) {
                return new JsonResponse(['success' => true, 'message' => 'User deleted successfully']);
            }

            $this->addFlash('success', 'User deleted successfully');
            return $this->redirectToRoute('admin_users');
        } catch (\Exception $e) {
            if ($this->isJsonRequest()) {
                return new JsonResponse(['error' => 'Failed to delete user: ' . $e->getMessage()], 500);
            }

            $this->addFlash('error', 'Failed to delete user: ' . $e->getMessage());
            return $this->redirectToRoute('admin_users');
        }
    }

    private function getAvailableRoles(): array
    {
        return [
            'ROLE_USER' => 'User',
            'ROLE_ADMIN' => 'Administrator',
            'ROLE_MODERATOR' => 'Moderator',
            'ROLE_EDITOR' => 'Editor'
        ];
    }

    private function isJsonRequest(): bool
    {
        $request = $this->container->get('request_stack')->getCurrentRequest();
        return $request && in_array('application/json', $request->getAcceptableContentTypes());
    }
}
