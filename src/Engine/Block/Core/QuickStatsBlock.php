<?php

namespace App\Engine\Block\Core;

use App\Core\Entity\User;
use App\Engine\Block\AbstractBlock;
use Doctrine\ORM\EntityManagerInterface;
use Twig\Environment;

class QuickStatsBlock extends AbstractBlock
{
    public function __construct(
        Environment $twig,
        private EntityManagerInterface $entityManager
    ) {
        parent::__construct($twig);
    }

    public function getId(): string
    {
        return 'quick_stats';
    }

    public function getLabel(): string
    {
        return 'Quick Statistics';
    }

    public function getDescription(): string
    {
        return 'Displays quick system statistics in the top panel';
    }

    public function getCategory(): string
    {
        return 'Dashboard';
    }

    public function render(array $context): string
    {
        $userCount = $this->entityManager->getRepository(User::class)->count([]);
        $activeUsers = $this->entityManager->getRepository(User::class)->count(['enabled' => true]);

        return $this->renderTemplate('blocks/quick_stats.html.twig', [
            'user_count' => $userCount,
            'active_users' => $activeUsers,
            'show_icons' => $this->configuration['show_icons'] ?? true,
            'layout' => $this->configuration['layout'] ?? 'horizontal'
        ]);
    }

    public function getConfigurationForm(): array
    {
        return [
            'show_icons' => [
                'type' => 'checkbox',
                'label' => 'Show Icons',
                'default' => true
            ],
            'layout' => [
                'type' => 'select',
                'label' => 'Layout',
                'options' => [
                    'horizontal' => 'Horizontal',
                    'vertical' => 'Vertical'
                ],
                'default' => 'horizontal'
            ]
        ];
    }
}
