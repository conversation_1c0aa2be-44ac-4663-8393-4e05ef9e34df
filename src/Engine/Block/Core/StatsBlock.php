<?php

namespace App\Engine\Block\Core;

use App\Engine\Block\AbstractBlock;

class StatsBlock extends AbstractBlock
{
    public function getId(): string
    {
        return 'stats';
    }

    public function getLabel(): string
    {
        return 'Statistics';
    }

    public function getDescription(): string
    {
        return 'Displays system statistics and metrics';
    }

    public function getCategory(): string
    {
        return 'Content';
    }

    public function render(array $context): string
    {
        $stats = [
            'users' => 1234,
            'orders' => 567,
            'revenue' => 89012
        ];

        return $this->renderTemplate('blocks/stats.html.twig', [
            'stats' => $stats,
            'title' => $this->configuration['title'] ?? 'Statistics',
            'show_icons' => $this->configuration['show_icons'] ?? true
        ]);
    }

    public function getConfigurationForm(): array
    {
        return array_merge(parent::getConfigurationForm(), [
            'title' => [
                'type' => 'text',
                'label' => 'Block Title',
                'default' => 'Statistics'
            ],
            'show_icons' => [
                'type' => 'checkbox',
                'label' => 'Show Icons',
                'default' => true
            ]
        ]);
    }
}
