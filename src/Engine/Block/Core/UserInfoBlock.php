<?php

namespace App\Engine\Block\Core;

use App\Engine\Block\AbstractBlock;

class UserInfoBlock extends AbstractBlock
{
    public function getId(): string
    {
        return 'user_info';
    }

    public function getLabel(): string
    {
        return 'User Information';
    }

    public function getDescription(): string
    {
        return 'Displays current user information and avatar';
    }

    public function getCategory(): string
    {
        return 'User';
    }

    public function render(array $context): string
    {
        $user = $context['user'] ?? null;

        return $this->renderTemplate('blocks/user_info.html.twig', [
            'user' => $user,
            'show_avatar' => $this->configuration['show_avatar'] ?? true,
            'show_email' => $this->configuration['show_email'] ?? true
        ]);
    }

    public function getConfigurationForm(): array
    {
        return array_merge(parent::getConfigurationForm(), [
            'show_avatar' => [
                'type' => 'checkbox',
                'label' => 'Show Avatar',
                'default' => true
            ],
            'show_email' => [
                'type' => 'checkbox',
                'label' => 'Show Email',
                'default' => true
            ]
        ]);
    }
}
