<?php

namespace App\Engine\Block\Core;

use App\Block\AbstractBlock;

class MenuBlock extends AbstractBlock
{

    public function getId(): string
    {
        return 'mainMenuBlock';
    }

    public function getLabel(): string
    {
        return 'MainMenu';
    }

    public function getDescription(): string
    {
        return 'Main menu block description';
    }

    public function getCategory(): string
    {
        return 'Navigation';
    }

    public function render(array $context): string
    {
        return '<div id="mainMenuBlock">Main menu block</div>';
    }

    public function getConfigurationForm(): array
    {
        return array_merge(parent::getConfigurationForm(), [
            'show_avatar' => [
                'type' => 'checkbox',
                'label' => 'Show Avatar',
                'default' => true
            ],
            'show_email' => [
                'type' => 'checkbox',
                'label' => 'Show Email',
                'default' => true
            ]
        ]);
    }
}
