<?php

namespace App\Engine\Block\Core;

use App\Engine\Block\AbstractBlock;

class WelcomeMessageBlock extends AbstractBlock
{
    public function getId(): string
    {
        return 'welcome_message';
    }

    public function getLabel(): string
    {
        return 'Welcome Message';
    }

    public function getDescription(): string
    {
        return 'Displays a welcome message before the main content';
    }

    public function getCategory(): string
    {
        return 'Content';
    }

    public function render(array $context): string
    {
        $user = $context['user'] ?? null;
        $messageType = $this->configuration['message_type'] ?? 'info';
        $customMessage = $this->configuration['custom_message'] ?? '';

        return $this->renderTemplate('blocks/welcome_message.html.twig', [
            'user' => $user,
            'message_type' => $messageType,
            'custom_message' => $customMessage,
            'show_user_info' => $this->configuration['show_user_info'] ?? true,
            'show_time' => $this->configuration['show_time'] ?? true
        ]);
    }

    public function getConfigurationForm(): array
    {
        return [
            'message_type' => [
                'type' => 'select',
                'label' => 'Message Type',
                'options' => [
                    'info' => 'Info',
                    'success' => 'Success',
                    'warning' => 'Warning',
                    'primary' => 'Primary'
                ],
                'default' => 'info'
            ],
            'custom_message' => [
                'type' => 'textarea',
                'label' => 'Custom Message',
                'default' => ''
            ],
            'show_user_info' => [
                'type' => 'checkbox',
                'label' => 'Show User Information',
                'default' => true
            ],
            'show_time' => [
                'type' => 'checkbox',
                'label' => 'Show Current Time',
                'default' => true
            ]
        ];
    }
}
