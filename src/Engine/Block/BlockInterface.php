<?php

namespace App\Engine\Block;

use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag(BlockInterface::class)]
interface BlockInterface
{

    public function getId(): string;

    public function getLabel(): string;

    public function getDescription(): string;

    public function getCategory(): string;

    public function isEnabled(): bool;

    public function getWeight(): int;

    public function getCacheSettings(): array;

    public function isVisible(array $context): bool;

    public function render(array $context): string;

    public function getConfigurationForm(): array;

    public function setConfiguration(array $config): void;

    public function getConfiguration(): array;
}
