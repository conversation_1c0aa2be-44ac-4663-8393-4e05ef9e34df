<?php

namespace App\Engine\Block;

use Twig\Environment;

abstract class AbstractBlock implements BlockInterface
{
    protected array $configuration = [];
    protected bool $enabled = true;
    protected int $weight = 0;

    public function __construct(
        protected Environment $twig
    ) {}

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function getWeight(): int
    {
        return $this->weight;
    }

    public function getCacheSettings(): array
    {
        return [
            'max_age' => 3600,
            'contexts' => ['route'],
            'tags' => ['block:' . $this->getId()]
        ];
    }

    public function isVisible(array $context): bool
    {
        return $this->isEnabled();
    }

    public function setConfiguration(array $config): void
    {
        $this->configuration = array_merge($this->configuration, $config);

        if (isset($config['enabled'])) {
            $this->enabled = (bool) $config['enabled'];
        }

        if (isset($config['weight'])) {
            $this->weight = (int) $config['weight'];
        }
    }

    public function getConfiguration(): array
    {
        return array_merge([
            'enabled' => $this->enabled,
            'weight' => $this->weight
        ], $this->configuration);
    }

    public function getConfigurationForm(): array
    {
        return [
            'enabled' => [
                'type' => 'checkbox',
                'label' => 'Enabled',
                'default' => true
            ],
            'weight' => [
                'type' => 'number',
                'label' => 'Weight',
                'default' => 0,
                'description' => 'Blocks with lower weights appear first'
            ]
        ];
    }

    protected function renderTemplate(string $template, array $variables = []): string
    {
        return $this->twig->render($template, $variables);
    }
}
