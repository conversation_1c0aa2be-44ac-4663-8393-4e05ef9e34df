<?php

namespace App\Engine\EventListener;

use App\Core\Template\Twig\DynamicTwigLoader;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\KernelEvent;
use Symfony\Component\HttpKernel\KernelEvents;

#[AsEventListener(event: KernelEvents::REQUEST, method: 'onRequest')]
readonly class TwigRequestListener {

    public function __construct(
        private EntityManagerInterface $entityManager,
        private DynamicTwigLoader      $dynamicTwigLoader
    ) {}

    public function onRequest(KernelEvent $event): void {
        $this->dynamicTwigLoader->setTheme($this->getActiveTheme());
    }

    private function getActiveTheme() {
        $this->entityManager->getConnection();
        return 'base';
    }
}
