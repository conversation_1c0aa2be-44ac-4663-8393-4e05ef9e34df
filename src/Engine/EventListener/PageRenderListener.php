<?php

namespace App\Engine\EventListener;

use App\Engine\Response\PageResponse;
use App\Engine\Service\ThemeService;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ViewEvent;
use Symfony\Component\HttpKernel\KernelEvents;

#[AsEventListener(event: KernelEvents::VIEW, method: 'onKernelView')]
readonly class PageRenderListener
{
    public function __construct(
        private ThemeService $themeService
    ) {}

    public function onKernelView(ViewEvent $event): void
    {
        $result = $event->getControllerResult();

        if (!$result instanceof PageResponse) {
            return;
        }

        $content = $this->themeService->renderPage($result, $event->getRequest());

        $response = new Response($content);
        $event->setResponse($response);
    }
}
