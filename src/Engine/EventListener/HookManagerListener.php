<?php

namespace App\Engine\EventListener;

use App\Engine\Hook\HookManager;
use App\Engine\Service\ModuleManager;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\KernelEvents;

#[AsEventListener(event: KernelEvents::REQUEST, method: 'onKernelRequest', priority: 10000)]

class HookManagerListener
{
    public function __construct(
        private readonly HookManager $hookManager,
        private readonly ModuleManager $moduleManager
    ) {}

    public function onKernelRequest($event): void
    {
        $this->hookManager->registerModuleHooks($this->moduleManager);
    }
}
