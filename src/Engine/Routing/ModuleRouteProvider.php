<?php

namespace App\Engine\Routing;

use App\Engine\Service\ModuleDiscoveryService;
use Symfony\Cmf\Component\Routing\RouteProviderInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouteCollection;

class ModuleRouteProvider implements RouteProviderInterface
{
    private array $routes = [];
    private bool $routesLoaded = false;

    public function __construct(
        private ModuleDiscoveryService $moduleDiscovery
    ) {}

    public function getRouteCollectionForRequest(Request $request): RouteCollection
    {
        $this->loadRoutes();

        $collection = new RouteCollection();
        $pathInfo = $request->getPathInfo();

        error_log("ModuleRouteProvider: Looking for routes matching: " . $pathInfo);
        error_log("ModuleRouteProvider: Available routes: " . implode(', ', array_keys($this->routes)));

        foreach ($this->routes as $name => $route) {
            error_log("ModuleRouteProvider: Checking route {$name} with path: " . $route->getPath());
            if ($this->routeMatches($route, $pathInfo)) {
                error_log("ModuleRouteProvider: Route {$name} matches!");
                $collection->add($name, $route);
            }
        }

        error_log("ModuleRouteProvider: Returning " . $collection->count() . " routes");
        return $collection;
    }

    public function getRouteByName(string $name): Route
    {
        $this->loadRoutes();
        $route = $this->routes[$name] ?? null;

        if (!$route) {
            throw new \Symfony\Component\Routing\Exception\RouteNotFoundException("Route '$name' not found");
        }

        return $route;
    }

    public function getRoutesByNames(?array $names = null): array
    {
        $this->loadRoutes();

        if ($names === null) {
            return $this->routes;
        }

        return array_intersect_key($this->routes, array_flip($names));
    }

    private function loadRoutes(): void
    {
        if ($this->routesLoaded) {
            return;
        }

        $this->routes = [];
        $discoveredModules = $this->moduleDiscovery->discoverModules();

        error_log("ModuleRouteProvider: Discovered " . count($discoveredModules) . " modules");
        foreach ($discoveredModules as $moduleInfo) {
            error_log("ModuleRouteProvider: Processing module: " . $moduleInfo['name']);
            $moduleRoutes = $this->loadModuleControllerRoutes($moduleInfo);
            error_log("ModuleRouteProvider: Module " . $moduleInfo['name'] . " has " . count($moduleRoutes) . " routes");
            $this->routes = array_merge($this->routes, $moduleRoutes);
        }

        error_log("ModuleRouteProvider: Total routes loaded: " . count($this->routes));
        $this->routesLoaded = true;
    }

    private function loadModuleControllerRoutes(array $moduleInfo): array
    {
        $routes = [];
        $controllerPath = $moduleInfo['path'] . '/Controller';

        if (!is_dir($controllerPath)) {
            return $routes;
        }

        $finder = new \Symfony\Component\Finder\Finder();
        $finder->files()
            ->in($controllerPath)
            ->name('*.php')
            ->depth('== 0');

        foreach ($finder as $file) {
            $controllerClass = $moduleInfo['namespace'] . '\\Controller\\' . $file->getBasename('.php');

            if (class_exists($controllerClass)) {
                $moduleRoutes = $this->extractRoutesFromController($controllerClass, $moduleInfo['name']);
                $routes = array_merge($routes, $moduleRoutes);
            }
        }

        return $routes;
    }

    private function extractRoutesFromController(string $controllerClass, string $moduleName): array
    {
        $routes = [];

        try {
            $reflection = new \ReflectionClass($controllerClass);

            $classRoute = $this->getRouteFromAttributes($reflection->getAttributes());
            $classPrefix = $classRoute['path'] ?? '';

            $modulePrefix = '/' . strtolower($moduleName);
            if (!str_starts_with($classPrefix, $modulePrefix)) {
                $classPrefix = $modulePrefix . $classPrefix;
            }
            foreach ($reflection->getMethods(\ReflectionMethod::IS_PUBLIC) as $method) {
                if ($method->getDeclaringClass()->getName() === $controllerClass) {
                    $methodRoute = $this->getRouteFromAttributes($method->getAttributes());

                    if ($methodRoute) {
                        $routeName = $methodRoute['name'] ?? strtolower($moduleName) . '_' . strtolower($method->getName());
                        $path = $classPrefix . ($methodRoute['path'] ?? '');
                        $methods = $methodRoute['methods'] ?? ['GET'];

                        $route = new Route(
                            $path,
                            [
                                '_controller' => $controllerClass . '::' . $method->getName(),
                                '_module' => $moduleName
                            ],
                            [],
                            [],
                            '',
                            [],
                            $methods
                        );

                        $routes[$routeName] = $route;
                    }
                }
            }
        } catch (\Exception $e) {

            error_log("Failed to extract routes from {$controllerClass}: " . $e->getMessage());
        }

        return $routes;
    }

    private function getRouteFromAttributes(array $attributes): ?array
    {
        foreach ($attributes as $attribute) {
            $attributeName = $attribute->getName();

            if ($attributeName === 'Symfony\Component\Routing\Attribute\Route') {
                $args = $attribute->getArguments();

                return [
                    'path' => $args[0] ?? $args['path'] ?? '',
                    'name' => $args['name'] ?? null,
                    'methods' => $args['methods'] ?? ['GET']
                ];
            }
        }

        return null;
    }

    private function routeMatches(Route $route, string $pathInfo): bool
    {
        $pattern = $route->getPath();

        $regex = preg_replace('/\{[^}]+\}/', '[^/]+', $pattern);
        $regex = '#^' . $regex . '$#';

        return (bool) preg_match($regex, $pathInfo);
    }
}
