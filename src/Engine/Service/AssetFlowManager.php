<?php

namespace App\Engine\Service;

class AssetFlowManager
{
    private array $assetFlow = [
        'stylesheets' => [],
        'scripts' => [],
        'libraries' => [],
        'pageSettings' => []
    ];

    public function addStylesheet(string $path, array $options = []): self
    {
        $this->assetFlow['stylesheets'][] = array_merge([
            'path' => $path,
            'weight' => 0,
            'group' => 'theme',
            'media' => 'all'
        ], $options);

        return $this;
    }

    public function addScript(string $path, array $options = []): self
    {
        $this->assetFlow['scripts'][] = array_merge([
            'path' => $path,
            'weight' => 0,
            'scope' => 'footer',
            'defer' => false,
            'async' => false
        ], $options);

        return $this;
    }

    public function addLibrary(string $library): self
    {
        $this->assetFlow['libraries'][] = $library;
        return $this;
    }

    public function addPageSetting(string $key, mixed $value): self
    {
        $this->assetFlow['pageSettings'][$key] = $value;
        return $this;
    }

    public function addStimulusController(string $controller, array $values = [], array $targets = []): self
    {
        $this->addPageSetting('stimulus.' . $controller, [
            'values' => $values,
            'targets' => $targets
        ]);

        return $this;
    }

    public function getAssetFlow(): array
    {

        usort($this->assetFlow['stylesheets'], fn($a, $b) => $a['weight'] <=> $b['weight']);
        usort($this->assetFlow['scripts'], fn($a, $b) => $a['weight'] <=> $b['weight']);

        return $this->assetFlow;
    }

    public function getStylesheets(): array
    {
        return $this->assetFlow['stylesheets'];
    }

    public function getScripts(): array
    {
        return $this->assetFlow['scripts'];
    }

    public function getLibraries(): array
    {
        return $this->assetFlow['libraries'];
    }

    public function getPageSettings(): array
    {
        return $this->assetFlow['pageSettings'];
    }

    public function clear(): self
    {
        $this->assetFlow = [
            'stylesheets' => [],
            'scripts' => [],
            'libraries' => [],
            'pageSettings' => []
        ];

        return $this;
    }

    public function merge(array $assetFlow): self
    {
        if (isset($assetFlow['stylesheets'])) {
            $this->assetFlow['stylesheets'] = array_merge($this->assetFlow['stylesheets'], $assetFlow['stylesheets']);
        }

        if (isset($assetFlow['scripts'])) {
            $this->assetFlow['scripts'] = array_merge($this->assetFlow['scripts'], $assetFlow['scripts']);
        }

        if (isset($assetFlow['libraries'])) {
            $this->assetFlow['libraries'] = array_merge($this->assetFlow['libraries'], $assetFlow['libraries']);
        }

        if (isset($assetFlow['pageSettings'])) {
            $this->assetFlow['pageSettings'] = array_merge($this->assetFlow['pageSettings'], $assetFlow['pageSettings']);
        }

        return $this;
    }

    public function hasAssets(): bool
    {
        return !empty($this->assetFlow['stylesheets']) ||
               !empty($this->assetFlow['scripts']) ||
               !empty($this->assetFlow['libraries']) ||
               !empty($this->assetFlow['pageSettings']);
    }

    public function generateSettingsScript(): string
    {
        if (empty($this->assetFlow['pageSettings'])) {
            return '';
        }

        $settings = json_encode($this->assetFlow['pageSettings'], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_AMP | JSON_HEX_QUOT);

        return sprintf(
            '<script>window.AssetFlow = window.AssetFlow || {}; window.AssetFlow.settings = %s;</script>',
            $settings
        );
    }
}
