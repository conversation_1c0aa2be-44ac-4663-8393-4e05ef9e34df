<?php

namespace App\Engine\Service;

use App\Core\Service\BlockManager;
use App\Engine\Hook\HookManager;
use App\Engine\Response\PageResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Yaml\Yaml;
use Twig\Environment;

class ThemeService
{
    private array $themeConfig;
    private array $regions = [];

    public function __construct(
        private Environment $twig,
        private HookManager $hookManager,
        private BlockManager $blockManager,
        private string $projectDir
    ) {
        $this->loadThemeConfig();
        $this->initializeRegions();
    }

    public function renderPage(PageResponse $pageResponse, Request $request): string
    {

        $mainContent = $this->twig->render(
            $pageResponse->getTemplate(),
            $pageResponse->getVariables()
        );

        $context = [
            'request' => $request,
            'page_data' => $pageResponse->getVariables(),
            'page_metadata' => $pageResponse->getMetadata(),
            'user' => null,
            'current_route' => $request->attributes->get('_route')
        ];

        $renderedRegions = [];
        $componentFlowData = [
            'scripts' => [],
            'stylesheets' => [],
            'libraries' => [],
            'pageSettings' => []
        ];
        foreach ($this->regions as $regionName => $regionConfig) {
            $output = '';

            $hookName = $regionName;
            $components = $this->hookManager->executeHook($hookName, $context);
            $output .= $this->renderRegionComponents($components);
            $this->extractAssetsFromComponents($componentFlowData, $components);
            try {
                $output .= $this->blockManager->renderRegionBlocks($regionName, $context);
            } catch (\Exception $e) {

                error_log('BlockManager error: ' . $e->getMessage());
            }

            $renderedRegions[$regionName] = $output;
        }

        $renderedRegions['page_content'] = $mainContent . $renderedRegions['page_content'];

        return $this->twig->render($this->themeConfig['layout'], [
            'regions' => $renderedRegions,
            'theme_config' => $this->themeConfig,
            'page_title' => $pageResponse->getVariables()['title'] ?? 'Page',
            'request' => $request,
            'assetFlow' => $this->mergePageAndComponentAssets($pageResponse->getMetadata()['assetFlow'] ?? [], $componentFlowData)
        ]);
    }

    private function extractAssetsFromComponents(&$assetsFlowData, $components): void
    {
        foreach ($components as $component) {
            if ($assets = $component->getAssets()) {
                foreach (['scripts', 'stylesheets', 'libraries', 'pageSettings'] as $type) {
                    if (isset($assets[$type])) {
                        $assetsFlowData[$type] = array_merge($assetsFlowData[$type] ?? [], $assets[$type] ?? []);
                    }
                }
            }
        }
    }

    private function mergePageAndComponentAssets($pageAssets, $componentAssets): array
    {
        return [
            'scripts' => array_merge($pageAssets['scripts'] ?? [], $componentAssets['scripts'] ?? []),
            'stylesheets' => array_merge($pageAssets['stylesheets'] ?? [], $componentAssets['stylesheets'] ?? []),
            'libraries' => array_merge($pageAssets['libraries'] ?? [], $componentAssets['libraries'] ?? []),
            'pageSettings' => array_merge($pageAssets['pageSettings'] ?? [], $componentAssets['pageSettings'] ?? [])
        ];
    }

    private function renderRegionComponents(array $components): string
    {
        if (empty($components)) {
            return '';
        }

        usort($components, function ($a, $b) {
            $weightA = method_exists($a, 'getWeight') ? $a->getWeight() : 0;
            $weightB = method_exists($b, 'getWeight') ? $b->getWeight() : 0;
            return $weightA <=> $weightB;
        });

        $output = '';
        foreach ($components as $component) {
            if (method_exists($component, 'getTemplate') && method_exists($component, 'getVariables')) {
                $output .= $this->twig->render($component->getTemplate(), $component->getVariables());
            } elseif (is_string($component)) {
                $output .= $component;
            }
        }

        return $output;
    }

    public function getRegions(): array
    {
        return $this->regions;
    }

    public function getThemeConfig(): array
    {
        return $this->themeConfig;
    }

    private function loadThemeConfig(): void
    {
        $configPath = $this->projectDir . '/config/theme.yaml';

        if (!file_exists($configPath)) {
            throw new \RuntimeException('Theme configuration file not found: ' . $configPath);
        }

        $config = Yaml::parseFile($configPath);
        $this->themeConfig = $config['theme'] ?? [];
    }

    private function initializeRegions(): void
    {
        $this->regions = $this->themeConfig['regions'] ?? [];

        uasort($this->regions, function ($a, $b) {
            $weightA = $a['weight'] ?? 0;
            $weightB = $b['weight'] ?? 0;
            return $weightA <=> $weightB;
        });
    }
}
