<?php

namespace App\Engine\Service;

use App\Core\Entity\ModuleState;
use App\Module\ModuleInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;

class ModuleManager
{
    private array $availableModules = [];

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly ModuleDiscoveryService $moduleDiscovery,
        #[AutowireIterator(ModuleInterface::class)] $modules
    ) {
        $this->registerModules($modules);
        $this->registerCustomModules();
    }

    public function getAvailableModules(): array
    {
        return $this->availableModules;
    }

    public function getModule(string $moduleId): ?ModuleInterface
    {
        return $this->availableModules[$moduleId] ?? null;
    }

    public function installModule(string $moduleId): bool
    {
        $module = $this->getModule($moduleId);
        if (!$module) {
            return false;
        }

        foreach ($module->getDependencies() as $dependency) {
            if (!$this->isModuleInstalled($dependency)) {
                throw new \RuntimeException("Dependency '{$dependency}' is not installed");
            }
        }

        try {
            $module->install();
            $this->saveModuleState($moduleId, true, true);
            return true;
        } catch (\Exception $e) {
            throw new \RuntimeException("Failed to install module: " . $e->getMessage());
        }
    }

    public function uninstallModule(string $moduleId): bool
    {
        $module = $this->getModule($moduleId);
        if (!$module) {
            return false;
        }

        $dependents = $this->getModuleDependents($moduleId);
        if (!empty($dependents)) {
            throw new \RuntimeException("Cannot uninstall module. These modules depend on it: " . implode(', ', $dependents));
        }

        try {
            $module->uninstall();
            $this->removeModuleState($moduleId);
            return true;
        } catch (\Exception $e) {
            throw new \RuntimeException("Failed to uninstall module: " . $e->getMessage());
        }
    }

    public function enableModule(string $moduleId): bool
    {
        $module = $this->getModule($moduleId);
        if (!$module || !$module->isInstalled()) {
            return false;
        }

        try {
            $module->enable();
            $this->saveModuleState($moduleId, true, true);
            return true;
        } catch (\Exception $e) {
            throw new \RuntimeException("Failed to enable module: " . $e->getMessage());
        }
    }

    public function disableModule(string $moduleId): bool
    {
        $module = $this->getModule($moduleId);
        if (!$module) {
            return false;
        }

        try {
            $module->disable();
            $this->saveModuleState($moduleId, true, false);
            return true;
        } catch (\Exception $e) {
            throw new \RuntimeException("Failed to disable module: " . $e->getMessage());
        }
    }

    public function isModuleInstalled(string $moduleId): bool
    {
        try {
            $state = $this->getModuleState($moduleId);
            return $state ? $state->isInstalled() : false;
        } catch (\Exception $e) {

            return false;
        }
    }

    public function isModuleEnabled(string $moduleId): bool
    {
        try {
            $state = $this->getModuleState($moduleId);
            return $state ? $state->isEnabled() : false;
        } catch (\Exception $e) {

            return false;
        }
    }

    public function getModulesByStatus(): array
    {
        $grouped = [
            'enabled' => [],
            'disabled' => [],
            'not_installed' => []
        ];

        foreach ($this->availableModules as $moduleId => $module) {
            if ($this->isModuleEnabled($moduleId)) {
                $grouped['enabled'][] = $module;
            } elseif ($this->isModuleInstalled($moduleId)) {
                $grouped['disabled'][] = $module;
            } else {
                $grouped['not_installed'][] = $module;
            }
        }

        return $grouped;
    }

    public function getModuleConfiguration(string $moduleId): array
    {
        $state = $this->getModuleState($moduleId);
        return $state ? $state->getConfiguration() : [];
    }

    public function updateModuleConfiguration(string $moduleId, array $config): void
    {
        $module = $this->getModule($moduleId);
        if ($module) {
            $module->setConfiguration($config);

            $state = $this->getModuleState($moduleId);
            if ($state) {
                $state->setConfiguration($config);
                $this->entityManager->flush();
            }
        }
    }

    private function registerModules(iterable $modules): void
    {
        foreach ($modules as $module) {
            if ($module instanceof ModuleInterface) {
                $this->availableModules[$module->getId()] = $module;

                $this->loadModuleStateIfNeeded($module);
            }
        }
    }

    private function getModuleState(string $moduleId): ?ModuleState
    {
        return $this->entityManager
            ->getRepository(ModuleState::class)
            ->findOneBy(['moduleId' => $moduleId]);
    }

    private function saveModuleState(string $moduleId, bool $installed, bool $enabled): void
    {
        $state = $this->getModuleState($moduleId);

        if (!$state) {
            $state = new ModuleState();
            $state->setModuleId($moduleId);
            $this->entityManager->persist($state);
        }

        $state->setInstalled($installed);
        $state->setEnabled($enabled);
        $this->entityManager->flush();
    }

    private function removeModuleState(string $moduleId): void
    {
        $state = $this->getModuleState($moduleId);
        if ($state) {
            $this->entityManager->remove($state);
            $this->entityManager->flush();
        }
    }

    private function getModuleDependents(string $moduleId): array
    {
        $dependents = [];

        foreach ($this->availableModules as $id => $module) {
            if (in_array($moduleId, $module->getDependencies()) && $this->isModuleInstalled($id)) {
                $dependents[] = $id;
            }
        }

        return $dependents;
    }

    private function registerCustomModules(): void
    {
        $discoveredModules = $this->moduleDiscovery->discoverModules();

        foreach ($discoveredModules as $moduleInfo) {
            $module = $this->moduleDiscovery->loadModule($moduleInfo);

            if ($module instanceof ModuleInterface) {
                $this->availableModules[$module->getId()] = $module;

                $this->loadModuleStateIfNeeded($module);
            }
        }
    }

    public function createModule(string $moduleName, array $moduleData): bool
    {
        try {

            $modulePath = $this->moduleDiscovery->createModuleDirectory($moduleName);

            $this->generateModuleClass($moduleName, $modulePath, $moduleData);

            $this->moduleDiscovery->discoverModules();

            return true;
        } catch (\Exception $e) {
            throw new \RuntimeException("Failed to create module: " . $e->getMessage());
        }
    }

    private function generateModuleClass(string $moduleName, string $modulePath, array $moduleData): void
    {
        $className = $moduleName . 'Module';
        $namespace = "App\\Module\\{$moduleName}";

        $template = $this->getModuleTemplate($className, $namespace, $moduleData);

        $filePath = $modulePath . '/' . $className . '.php';
        file_put_contents($filePath, $template);
    }

    private function getModuleTemplate(string $className, string $namespace, array $data): string
    {
        $name = $data['name'] ?? $className;
        $description = $data['description'] ?? 'Custom module';
        $version = $data['version'] ?? '1.0.0';
        $hooks = $data['hooks'] ?? [];

        $hooksArray = empty($hooks) ? '[]' : "['" . implode("', '", $hooks) . "']";

        return <<<PHP
<?php

namespace {$namespace};

use App\Module\AbstractModule;
use App\Hook\RegionComponent;

/**
 * {$name} - {$description}
 */
class {$className} extends AbstractModule
{
    public function getId(): string
    {
        return '{$data['id']}';
    }

    public function getName(): string
    {
        return '{$name}';
    }

    public function getDescription(): string
    {
        return '{$description}';
    }

    public function getVersion(): string
    {
        return '{$version}';
    }

    public function getDependencies(): array
    {
        return [];
    }

    public function getHooks(): array
    {
        return {$hooksArray};
    }

    public function getConfigurationSchema(): array
    {
        return [];
    }

    protected function onInstall(): void
    {
        // Module installation logic
    }

    protected function onEnable(): void
    {
        // Module enable logic
    }
}
PHP;
    }

    private function loadModuleStateIfNeeded(ModuleInterface $module): void
    {
        try {
            $state = $this->getModuleState($module->getId());
            if ($state) {

                if ($state->isInstalled()) {
                    $module->install();
                    if ($state->isEnabled()) {
                        $module->enable();
                    }
                }
                $module->setConfiguration($state->getConfiguration());
            }
        } catch (\Exception $e) {

            error_log("Could not load module state for {$module->getId()}: " . $e->getMessage());
        }
    }
}
