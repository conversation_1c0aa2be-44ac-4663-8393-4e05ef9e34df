<?php

namespace App\Engine\Service;

use App\Module\ModuleInterface;
use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;
use Symfony\Component\Finder\Finder;

class ModuleDiscoveryService
{
    private string $moduleDirectory;
    private array $discoveredModules = [];

    public function __construct(string $projectDir, #[AutowireIterator(ModuleInterface::class)] private iterable $modules)
    {
        $this->moduleDirectory = $projectDir . '/src/Module/Custom';
    }

    public function discoverModules(): array
    {
        if (!empty($this->discoveredModules)) {
            return $this->discoveredModules;
        }

        $this->discoveredModules = [];

        $finder = new Finder();
        $finder->directories()
            ->in($this->moduleDirectory)
            ->depth('== 0') 
            ->exclude(['Core']); 

        foreach ($finder as $directory) {
            $moduleName = $directory->getBasename();
            $modulePath = $directory->getRealPath();

            $moduleClass = $this->findModuleClass($modulePath, $moduleName);

            if ($moduleClass) {

                $moduleId = $this->getModuleIdFromClass($moduleClass);

                $this->discoveredModules[$moduleId] = [
                    'name' => $moduleName,
                    'path' => $modulePath,
                    'class' => $moduleClass,
                    'namespace' => "App\\Module\\Custom\\{$moduleName}",
                    'id' => $moduleId
                ];
            }
        }

        return $this->discoveredModules;
    }

    private function findModuleClass(string $modulePath, string $moduleName): ?string
    {
        $expectedFile = $modulePath . '/' . $moduleName . 'Module.php';

        if (file_exists($expectedFile)) {
            $className = "App\\Module\\Custom\\{$moduleName}\\{$moduleName}Module";

            if (class_exists($className)) {
                $reflection = new \ReflectionClass($className);
                if ($reflection->implementsInterface(ModuleInterface::class)) {
                    return $className;
                }
            }
        }

        $finder = new Finder();
        $finder->files()
            ->in($modulePath)
            ->name('*Module.php')
            ->depth('== 0');

        foreach ($finder as $file) {
            $className = "App\\Module\\Custom\\{$moduleName}\\" . $file->getBasename('.php');
            if (class_exists($className)) {
                $reflection = new \ReflectionClass($className);
                if ($reflection->implementsInterface(ModuleInterface::class)) {
                    return $className;
                }
            }
        }

        return null;
    }

    public function loadModule(array $moduleInfo): ?ModuleInterface
    {
        $className = $moduleInfo['class'];

        if (!class_exists($className)) {
            return null;
        }

        try {
            $module = $this->getModuleInstance($className);

            if ($module instanceof ModuleInterface) {
                if (method_exists($module, 'setModulePath')) {
                    $module->setModulePath($moduleInfo['path']);
                }
                if (method_exists($module, 'setModuleNamespace')) {
                    $module->setModuleNamespace($moduleInfo['namespace']);
                }

                return $module;
            }
        } catch (\Exception $e) {
            error_log("Failed to load module {$className}: " . $e->getMessage());
        }

        return null;
    }

    public function getModuleDirectory(): string
    {
        return $this->moduleDirectory;
    }

    public function moduleDirectoryExists(string $moduleName): bool
    {
        return is_dir($this->moduleDirectory . '/' . $moduleName);
    }

    public function createModuleDirectory(string $moduleName): string
    {
        $modulePath = $this->moduleDirectory . '/' . $moduleName;

        if (!is_dir($modulePath)) {
            mkdir($modulePath, 0755, true);
        }

        $subdirs = ['Controller', 'Service', 'templates'];
        foreach ($subdirs as $subdir) {
            $subdirPath = $modulePath . '/' . $subdir;
            if (!is_dir($subdirPath)) {
                mkdir($subdirPath, 0755, true);
            }
        }

        return $modulePath;
    }

    private function getModuleIdFromClass(string $className): string
    {
        try {
            $instance = $this->getModuleInstance($className);
            if (!$instance) {
                throw new \Exception("Failed to get module instance for $className");
            }
            if (method_exists($instance, 'getId')) {
                return $instance->getId();
            }
        } catch (\Exception $e) {

        }

        $parts = explode('\\', $className);
        $lastPart = end($parts);

        if (str_ends_with($lastPart, 'Module')) {
            $lastPart = substr($lastPart, 0, -6);
        }

        return strtolower($lastPart);
    }

    private function getModuleInstance(string $className): ?ModuleInterface {
        foreach ($this->modules as $possibleModule) {
            if ($possibleModule instanceof $className) {
                return $possibleModule;
            }
        }

        return null;
    }
}
