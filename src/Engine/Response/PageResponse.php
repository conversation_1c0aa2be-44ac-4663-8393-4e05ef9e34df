<?php

namespace App\Engine\Response;

class PageResponse
{
    public function __construct(
        private readonly string $template,
        private array $variables = [],
        private array $metadata = []
    ) {}

    public function getTemplate(): string
    {
        return $this->template;
    }

    public function getVariables(): array
    {
        return $this->variables;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public function setVariable(string $key, mixed $value): self
    {
        $this->variables[$key] = $value;
        return $this;
    }

    public function setMetadata(string $key, mixed $value): self
    {
        $this->metadata[$key] = $value;
        return $this;
    }

    public static function create(string $template, array $variables = [], array $metadata = []): self
    {
        return new self($template, $variables, $metadata);
    }

}
