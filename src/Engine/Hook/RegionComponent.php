<?php

namespace App\Engine\Hook;

class RegionComponent
{
    public function __construct(
        private string $template,
        private array $variables = [],
        private array $assets = [],
        private int $weight = 0,
        private array $cache = []
    ) {}

    public function getTemplate(): string
    {
        return $this->template;
    }

    public function getVariables(): array
    {
        return $this->variables;
    }

    public function getWeight(): int
    {
        return $this->weight;
    }

    public function getAssets(): array
    {
        return $this->assets;
    }

    public function getCache(): array
    {
        return $this->cache;
    }

    public function setVariable(string $key, mixed $value): self
    {
        $this->variables[$key] = $value;
        return $this;
    }

    public static function create(string $template, array $variables = [], array $assets = [], array $cache = [], int $weight = 0, ): self
    {
        return new self($template, $variables, $assets, $weight, $cache);
    }
}
