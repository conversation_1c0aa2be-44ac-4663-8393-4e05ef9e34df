<?php

namespace App\Engine\Hook;

use App\Engine\Service\ModuleManager;
use App\Module\ModuleInterface;

class HookManager
{
    private array $hooks = [];

    public function callModuleHooks(string $hookName, array $context = []): array
    {
        if (!isset($this->hooks[$hookName])) {
            return $context;
        }

        foreach ($this->hooks[$hookName] as $handler) {
            $context = $handler->execute($context);
        }

        return $context;
    }
    public function executeHook(string $hookName, array $context = []): array
    {
        $results = [];

        if (!isset($this->hooks[$hookName])) {
            return $results;
        }

        foreach ($this->hooks[$hookName] as $handler) {
            $result = $handler->execute($context);
            if ($result !== null) {
                if (is_array($result)) {
                    $results = array_merge($results, $result);
                } else {
                    $results[] = $result;
                }
            }
        }

        return $results;
    }

    public function registerHook(string $hookName, HookHandlerInterface $handler): void
    {
        if (!isset($this->hooks[$hookName])) {
            $this->hooks[$hookName] = [];
        }

        $this->hooks[$hookName][] = $handler;
    }

    public function registerModuleHooks(ModuleManager $moduleManager): void
    {
        foreach ($moduleManager->getAvailableModules() as $module) {
            if ($module instanceof ModuleInterface && $module->isEnabled()) {
                foreach ($module->getHooks() as $hookName) {
                    $this->registerModuleHook($hookName, $module);
                }
            }
        }
    }

    private function registerModuleHook(string $hookName, ModuleInterface $module): void
    {
        if (!isset($this->hooks[$hookName])) {
            $this->hooks[$hookName] = [];
        }

        $this->hooks[$hookName][] = new class($module, $hookName) implements HookHandlerInterface {
            public function __construct(
                private ModuleInterface $module,
                private readonly string $hookName
            ) {}

            public function getHooks(): array
            {
                return [$this->hookName];
            }

            public function execute(array $context): mixed
            {
                return $this->module->executeHook($this->hookName, $context);
            }
        };
    }

    public function getRegisteredHooks(): array
    {
        return array_keys($this->hooks);
    }
}
