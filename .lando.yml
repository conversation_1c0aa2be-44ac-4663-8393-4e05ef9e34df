name: hal
recipe: symfony
config:
  webroot: public
  php: '8.2'
  via: nginx
  database: mariadb:10.4
  xdebug: true
  config:
    server: .lando/nginx.conf
    php: .lando/php.ini
    xdebug: 'develop, debug'
services:
  database:
    portforward: 3310
  appserver:
    overrides:
      environment:
        XDEBUG_MODE: 'develop, debug'
        PHP_IDE_CONFIG: serverName=appserver
  mailcatcher:
    type: mailhog:v1.0.0
    portforward: true
    hogfrom:
      - appserver

tooling:
  xdebug:
    description: "Loads Xdebug in the selected mode"
    cmd:
      - appserver: /app/.lando/xdebug.sh
    user: root